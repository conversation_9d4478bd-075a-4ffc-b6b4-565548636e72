# spec/lib/state_normalizer_spec.rb
require 'rails_helper'

RSpec.describe StateNormalizer do
  describe '.to_code' do
    context 'when country_code is US' do
      it 'uses UsStateNormalizer' do
        expect(StateNormalizer.to_code('California', 'US')).to eq('CA')
        expect(StateNormalizer.to_code('Texas', 'US')).to eq('TX')
      end
    end

    context 'when country_code is CA' do
      it 'uses CanadianProvinceNormalizer' do
        expect(StateNormalizer.to_code('Ontario', 'CA')).to eq('ON')
        expect(StateNormalizer.to_code('Quebec', 'CA')).to eq('QC')
      end
    end

    context 'when country_code is AU' do
      it 'uses AustralianStateNormalizer' do
        expect(StateNormalizer.to_code('Victoria', 'AU')).to eq('VIC')
        expect(StateNormalizer.to_code('New South Wales', 'AU')).to eq('NSW')
      end
    end

    context 'when country_code is nil or unknown' do
      it 'falls back to UsStateNormalizer' do
        expect(StateNormalizer.to_code('California')).to eq('CA')
        expect(StateNormalizer.to_code('California', 'UNKNOWN')).to eq('CA')
      end
    end

    context 'when state is blank' do
      it 'returns nil' do
        expect(StateNormalizer.to_code('', 'US')).to be_nil
        expect(StateNormalizer.to_code(nil, 'CA')).to be_nil
      end
    end

    context 'when normalizer is not defined' do
      it 'returns the original state in uppercase' do
        # Temporarily undefine the normalizers to test fallback
        allow(StateNormalizer).to receive(:to_code).and_call_original
        
        # This should fall back to the original logic
        expect(StateNormalizer.to_code('UNKNOWN_STATE', 'US')).to eq('UNKNOWN_STATE')
      end
    end
  end
end
