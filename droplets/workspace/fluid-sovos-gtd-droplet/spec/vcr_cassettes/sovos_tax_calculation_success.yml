---
http_interactions:
- request:
    method: post
    uri: https://gtduat.sovos.com/Twe/api/rest/calcTax/doc
    body:
      encoding: UTF-8
      string: '{"usrname":"<SOVOS_USERNAME>","pswrd":"<SOVOS_PASSWORD>","rsltLvl":1,"isAudit":false,"tdcReqrd":true,"tdmRequired":false,"currn":"USD","trnDocNum":9,"txCalcTp":1,"lines":[{"orgCd":"<SOVOS_ORG_CODE>","lnItmId":19,"grossAmt":352.17,"goodSrvCd":"1980110101","goodSrvDesc":"Product
        1980110101","qnty":7.0,"trnTp":1,"custVendCd":null,"custVendName":null,"sTCity":"San
        francisco","sTStateProv":"CA","sTCountry":"US","sTPstlCd":"91060","sTStNameNum":"fake
        12","bTCity":"San francisco","bTStateProv":"CA","bTCountry":"US","bTPstlCd":"91060","bTStNameNum":"fake
        12"}]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      Authorization:
      - "<AUTHORIZATION>"
      Date:
      - '2025-07-28T20:15:54Z'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Proxy-Connection:
      - Close
      Content-Type:
      - application/json
      Transfer-Encoding:
      - chunked
      Date:
      - Mon, 28 Jul 2025 20:15:55 GMT
      Server:
      - Layer7-API-Gateway
    body:
      encoding: UTF-8
      string: |-
        {
          "trnDocNum" : "9",
          "txwTrnDocId" : "0",
          "txAmt" : "30.37",
          "lnRslts" : [ {
            "lnNm" : "1",
            "lnId" : "19",
            "txwLnId" : "0",
            "txAmt" : "30.37",
            "grossAmt" : "352.17",
            "txRate" : "0.08625",
            "jurRslts" : [ {
              "txAmt" : "21.13",
              "txRate" : "0.06",
              "txableAmt" : "352.17",
              "xmptAmt" : "0.0",
              "recvrPercnt" : "0.0",
              "recvrAmt" : "0.0",
              "txName" : "CA State Tax",
              "txNameID" : "446",
              "txTp" : "1",
              "thrshldLvl" : "0",
              "aggrTxJurTp1" : "2",
              "aggrTxJurTp2" : "2",
              "txPaidToDt" : "0.0",
              "txablePaidToDt" : "0.0",
              "leasePmtTp" : "S",
              "txJurUIDCntry" : "UNITED STATES",
              "txJurUIDStatePrv" : "CALIFORNIA",
              "txJurUIDJurTp" : "2",
              "txJurUIDTxwTJId" : "290",
              "txJurUIDStatePrvISO" : "CA",
              "txJurUIDCntryISO" : "US",
              "txDecisSets" : [ {
                "txDecisSetTp" : "CA State Tax",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9100",
                  "dscrp" : "Tax Rate rule applied.",
                  "dtls" : "Tax Rate rule applied.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_RULE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "21385",
                  "dscrp" : "Used Situs Rule: 12487",
                  "dtls" : "Used Situs Rule: 12487",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "TAXPOINT_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "1",
                  "id" : "9318",
                  "dscrp" : "Tax Point defaulted to System Date.",
                  "dtls" : "Tax Point defaulted to System Date.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9323",
                  "dscrp" : "Accounting Method defaulted to Accrual.",
                  "dtls" : "Accounting Method defaulted to Accrual.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9401",
                  "dscrp" : "Situs is Ship-To (ST).",
                  "dtls" : "Situs is Ship-To (ST).",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9419",
                  "dscrp" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "dtls" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9423",
                  "dscrp" : "Situs rule successfully applied.",
                  "dtls" : "Situs rule successfully applied.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9442",
                  "dscrp" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "dtls" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "attrbs" : { }
                } ] ]
              } ]
            }, {
              "txAmt" : "3.52",
              "txRate" : "0.01",
              "txableAmt" : "352.17",
              "xmptAmt" : "0.0",
              "recvrPercnt" : "0.0",
              "recvrAmt" : "0.0",
              "txName" : "CA County Tax",
              "txNameID" : "453",
              "txTp" : "1",
              "thrshldLvl" : "0",
              "aggrTxJurTp1" : "3",
              "aggrTxJurTp2" : "3",
              "txPaidToDt" : "0.0",
              "txablePaidToDt" : "0.0",
              "leasePmtTp" : "S",
              "txJurUIDCntry" : "UNITED STATES",
              "txJurUIDStatePrv" : "CALIFORNIA",
              "txJurUIDCityCntyDist" : "SAN FRANCISCO",
              "txJurUIDJurTp" : "3",
              "txJurUIDTxwTJId" : "1503",
              "txJurUIDStatePrvISO" : "CA",
              "txJurUIDCntryISO" : "US",
              "txDecisSets" : [ {
                "txDecisSetTp" : "CA County Tax",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9100",
                  "dscrp" : "Tax Rate rule applied.",
                  "dtls" : "Tax Rate rule applied.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_RULE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "13811",
                  "dscrp" : "Used Situs Rule: 4981",
                  "dtls" : "Used Situs Rule: 4981",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "TAXPOINT_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "1",
                  "id" : "9318",
                  "dscrp" : "Tax Point defaulted to System Date.",
                  "dtls" : "Tax Point defaulted to System Date.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9323",
                  "dscrp" : "Accounting Method defaulted to Accrual.",
                  "dtls" : "Accounting Method defaulted to Accrual.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9401",
                  "dscrp" : "Situs is Ship-To (ST).",
                  "dtls" : "Situs is Ship-To (ST).",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9419",
                  "dscrp" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "dtls" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9423",
                  "dscrp" : "Situs rule successfully applied.",
                  "dtls" : "Situs rule successfully applied.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9442",
                  "dscrp" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "dtls" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "attrbs" : { }
                } ] ]
              } ]
            }, {
              "txAmt" : "0.88",
              "txRate" : "0.0025",
              "txableAmt" : "352.17",
              "xmptAmt" : "0.0",
              "recvrPercnt" : "0.0",
              "recvrAmt" : "0.0",
              "txName" : "CA Bradley Burns Tax",
              "txNameID" : "451",
              "txTp" : "1",
              "thrshldLvl" : "0",
              "aggrTxJurTp1" : "3",
              "aggrTxJurTp2" : "3",
              "txPaidToDt" : "0.0",
              "txablePaidToDt" : "0.0",
              "leasePmtTp" : "S",
              "txJurUIDCntry" : "UNITED STATES",
              "txJurUIDStatePrv" : "CALIFORNIA",
              "txJurUIDCityCntyDist" : "SAN FRANCISCO",
              "txJurUIDJurTp" : "3",
              "txJurUIDTxwTJId" : "1503",
              "txJurUIDStatePrvISO" : "CA",
              "txJurUIDCntryISO" : "US",
              "txDecisSets" : [ {
                "txDecisSetTp" : "CA Bradley Burns Tax",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9100",
                  "dscrp" : "Tax Rate rule applied.",
                  "dtls" : "Tax Rate rule applied.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_RULE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "13811",
                  "dscrp" : "Used Situs Rule: 4981",
                  "dtls" : "Used Situs Rule: 4981",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "TAXPOINT_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "1",
                  "id" : "9318",
                  "dscrp" : "Tax Point defaulted to System Date.",
                  "dtls" : "Tax Point defaulted to System Date.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9323",
                  "dscrp" : "Accounting Method defaulted to Accrual.",
                  "dtls" : "Accounting Method defaulted to Accrual.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9401",
                  "dscrp" : "Situs is Ship-To (ST).",
                  "dtls" : "Situs is Ship-To (ST).",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9419",
                  "dscrp" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "dtls" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9423",
                  "dscrp" : "Situs rule successfully applied.",
                  "dtls" : "Situs rule successfully applied.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9442",
                  "dscrp" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "dtls" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "attrbs" : { }
                } ] ]
              } ]
            }, {
              "txAmt" : "4.84",
              "txRate" : "0.01375",
              "txableAmt" : "352.17",
              "xmptAmt" : "0.0",
              "recvrPercnt" : "0.0",
              "recvrAmt" : "0.0",
              "txName" : "CA District Tax",
              "txNameID" : "445",
              "txTp" : "1",
              "thrshldLvl" : "0",
              "aggrTxJurTp1" : "7",
              "aggrTxJurTp2" : "7",
              "txPaidToDt" : "0.0",
              "txablePaidToDt" : "0.0",
              "leasePmtTp" : "S",
              "txJurUIDCntry" : "UNITED STATES",
              "txJurUIDStatePrv" : "CALIFORNIA",
              "txJurUIDCityCntyDist" : "SAN FRANCISCO COUNTY",
              "txJurUIDJurTp" : "5",
              "txJurUIDTxwTJId" : "1547",
              "txJurUIDStatePrvISO" : "CA",
              "txJurUIDCntryISO" : "US",
              "txDecisSets" : [ {
                "txDecisSetTp" : "CA District Tax",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9100",
                  "dscrp" : "Tax Rate rule applied.",
                  "dtls" : "Tax Rate rule applied.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_RULE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "21314",
                  "dscrp" : "Used Situs Rule: 12416",
                  "dtls" : "Used Situs Rule: 12416",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "TAXPOINT_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "1",
                  "id" : "9318",
                  "dscrp" : "Tax Point defaulted to System Date.",
                  "dtls" : "Tax Point defaulted to System Date.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9323",
                  "dscrp" : "Accounting Method defaulted to Accrual.",
                  "dtls" : "Accounting Method defaulted to Accrual.",
                  "attrbs" : { }
                } ] ]
              }, {
                "txDecisSetTp" : "SITUS_ENGINE",
                "txDeciss" : [ [ {
                  "severity" : "0",
                  "id" : "9401",
                  "dscrp" : "Situs is Ship-To (ST).",
                  "dtls" : "Situs is Ship-To (ST).",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9419",
                  "dscrp" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "dtls" : "Situs: Client Taxpayer Registration Number or Nexus used to determine Situs.",
                  "attrbs" : { }
                }, {
                  "severity" : "0",
                  "id" : "9423",
                  "dscrp" : "Situs rule successfully applied.",
                  "dtls" : "Situs rule successfully applied.",
                  "attrbs" : { }
                }, {
                  "severity" : "1",
                  "id" : "9442",
                  "dscrp" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "dtls" : "Drop Ship Indicator reset to 0 (Regular Transaction) by System.",
                  "attrbs" : { }
                } ] ]
              } ]
            } ],
            "myRegNms" : [ {
              "taxable" : "false",
              "cntry" : "UNITED STATES",
              "txwTJId" : "227",
              "status" : "2",
              "jurTp" : "1"
            }, {
              "statePrv" : "CALIFORNIA",
              "taxable" : "true",
              "cntry" : "UNITED STATES",
              "txwTJId" : "290",
              "status" : "2",
              "jurTp" : "2"
            } ],
            "mergedResult" : "false",
            "txDecisSets" : [ {
              "txDecisSetTp" : "ROUNDING_ENGINE",
              "txDeciss" : [ [ {
                "severity" : "0",
                "id" : "9161",
                "dscrp" : "Rounding is performed on the total tax for a Document",
                "dtls" : "Rounding is performed on the total tax for a Document",
                "attrbs" : { }
              } ], [ {
                "severity" : "0",
                "id" : "9153",
                "dscrp" : "Document Level Rounding was applied.",
                "dtls" : "Document Level Rounding was applied.",
                "attrbs" : { }
              } ] ]
            }, {
              "txDecisSetTp" : "LINE_ITEM",
              "txDeciss" : [ [ {
                "severity" : "1",
                "id" : "9652",
                "dscrp" : "Invalid or inactive Customer Code passed. Customer Code ignored.",
                "dtls" : "Invalid or inactive Customer Code passed. Customer Code ignored.",
                "attrbs" : { }
              }, {
                "severity" : "1",
                "id" : "9429",
                "dscrp" : "Client Taxpayer Registration Number or Nexus used in Situs determination.",
                "dtls" : "Client Taxpayer Registration Number or Nexus used in Situs determination.",
                "attrbs" : { }
              }, {
                "severity" : "1",
                "id" : "9656",
                "dscrp" : "The My Good/Service code passed in the transaction is not mapped to a TWE Good/Service Code.  Standard tax rates are applied.",
                "dtls" : "The My Good/Service code passed in the transaction is not mapped to a TWE Good/Service Code.  Standard tax rates are applied.",
                "attrbs" : { }
              } ] ]
            }, {
              "txDecisSetTp" : "TJC",
              "txDeciss" : [ [ {
                "severity" : "1",
                "id" : "9170",
                "dscrp" : "At least one address associated with the line item has failed validation",
                "dtls" : "At least one address associated with the line item has failed validation",
                "attrbs" : { }
              }, {
                "severity" : "0",
                "id" : "9029",
                "dscrp" : "Geo Code found using partial address match (Zip 5)",
                "dtls" : "Geo Code found using partial address match (Zip 5)",
                "attrbs" : { }
              } ] ]
            }, {
              "txDecisSetTp" : "LINE_ITEM",
              "txDeciss" : [ [ {
                "severity" : "1",
                "id" : "9652",
                "dscrp" : "Invalid or inactive Customer Code passed. Customer Code ignored.",
                "dtls" : "Invalid or inactive Customer Code passed. Customer Code ignored.",
                "attrbs" : { }
              }, {
                "severity" : "1",
                "id" : "9429",
                "dscrp" : "Client Taxpayer Registration Number or Nexus used in Situs determination.",
                "dtls" : "Client Taxpayer Registration Number or Nexus used in Situs determination.",
                "attrbs" : { }
              }, {
                "severity" : "1",
                "id" : "9656",
                "dscrp" : "The My Good/Service code passed in the transaction is not mapped to a TWE Good/Service Code.  Standard tax rates are applied.",
                "dtls" : "The My Good/Service code passed in the transaction is not mapped to a TWE Good/Service Code.  Standard tax rates are applied.",
                "attrbs" : { }
              } ] ]
            }, {
              "txDecisSetTp" : "TJC",
              "txDeciss" : [ [ {
                "severity" : "1",
                "id" : "9170",
                "dscrp" : "At least one address associated with the line item has failed validation",
                "dtls" : "At least one address associated with the line item has failed validation",
                "attrbs" : { }
              }, {
                "severity" : "0",
                "id" : "9029",
                "dscrp" : "Geo Code found using partial address match (Zip 5)",
                "dtls" : "Geo Code found using partial address match (Zip 5)",
                "attrbs" : { }
              } ] ]
            } ]
          } ],
          "jurSumRslts" : [ {
            "txAmt" : "3.52",
            "txableAmt" : "352.17",
            "xmptAmt" : "0.0",
            "txName" : "CA County Tax",
            "txNameID" : "453",
            "txTp" : "1",
            "txJurUIDCntry" : "UNITED STATES",
            "txJurUIDStatePrv" : "CALIFORNIA",
            "txJurUIDCityCntyDist" : "SAN FRANCISCO",
            "txJurUIDJurTp" : "3",
            "txJurUIDTxwTJId" : "1503",
            "txJurUIDStatePrvISO" : "CA",
            "txJurUIDCntryISO" : "US",
            "thrshldLvl" : "0",
            "txPaidToDt" : "0.0",
            "txablePaidToDt" : "0.0",
            "leasePmtTp" : " "
          }, {
            "txAmt" : "21.13",
            "txableAmt" : "352.17",
            "xmptAmt" : "0.0",
            "txName" : "CA State Tax",
            "txNameID" : "446",
            "txTp" : "1",
            "txJurUIDCntry" : "UNITED STATES",
            "txJurUIDStatePrv" : "CALIFORNIA",
            "txJurUIDJurTp" : "2",
            "txJurUIDTxwTJId" : "290",
            "txJurUIDStatePrvISO" : "CA",
            "txJurUIDCntryISO" : "US",
            "thrshldLvl" : "0",
            "txPaidToDt" : "0.0",
            "txablePaidToDt" : "0.0",
            "leasePmtTp" : " "
          }, {
            "txAmt" : "0.88",
            "txableAmt" : "352.17",
            "xmptAmt" : "0.0",
            "txName" : "CA Bradley Burns Tax",
            "txNameID" : "451",
            "txTp" : "1",
            "txJurUIDCntry" : "UNITED STATES",
            "txJurUIDStatePrv" : "CALIFORNIA",
            "txJurUIDCityCntyDist" : "SAN FRANCISCO",
            "txJurUIDJurTp" : "3",
            "txJurUIDTxwTJId" : "1503",
            "txJurUIDStatePrvISO" : "CA",
            "txJurUIDCntryISO" : "US",
            "thrshldLvl" : "0",
            "txPaidToDt" : "0.0",
            "txablePaidToDt" : "0.0",
            "leasePmtTp" : " "
          }, {
            "txAmt" : "4.84",
            "txableAmt" : "352.17",
            "xmptAmt" : "0.0",
            "txName" : "CA District Tax",
            "txNameID" : "445",
            "txTp" : "1",
            "txJurUIDCntry" : "UNITED STATES",
            "txJurUIDStatePrv" : "CALIFORNIA",
            "txJurUIDCityCntyDist" : "SAN FRANCISCO COUNTY",
            "txJurUIDJurTp" : "5",
            "txJurUIDTxwTJId" : "1547",
            "txJurUIDStatePrvISO" : "CA",
            "txJurUIDCntryISO" : "US",
            "thrshldLvl" : "0",
            "txPaidToDt" : "0.0",
            "txablePaidToDt" : "0.0",
            "leasePmtTp" : " "
          } ],
          "txDecisSet" : {
            "txDecisSetTp" : "DOCUMENT",
            "txDeciss" : [ [ {
              "severity" : "0",
              "id" : "9168",
              "dscrp" : "Address validation enabled",
              "dtls" : "Address validation enabled",
              "attrbs" : { }
            } ] ]
          }
        }
  recorded_at: Mon, 28 Jul 2025 20:15:56 GMT
recorded_with: VCR 6.3.1
