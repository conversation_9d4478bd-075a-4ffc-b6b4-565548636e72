---
http_interactions:
- request:
    method: post
    uri: https://gtduat.sovos.com/Twe/api/rest/calcTax/doc
    body:
      encoding: UTF-8
      string: '{"usrname":"invalid_user","pswrd":"invalid_pass","rsltLvl":1,"isAudit":false,"tdcReqrd":true,"tdmRequired":false,"currn":"USD","trnDocNum":9,"txCalcTp":1,"lines":[{"orgCd":"<SOVOS_ORG_CODE>","lnItmId":19,"grossAmt":352.17,"goodSrvCd":"1980110101","goodSrvDesc":"Product
        1980110101","qnty":7.0,"trnTp":1,"custVendCd":null,"custVendName":null,"sTCity":"San
        francisco","sTStateProv":"CA","sTCountry":"US","sTPstlCd":"91060","sTStNameNum":"fake
        12","bTCity":"San francisco","bTStateProv":"CA","bTCountry":"US","bTPstlCd":"91060","bTStNameNum":"fake
        12"}]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      Authorization:
      - "<AUTHORIZATION>"
      Date:
      - '2025-07-28T20:31:19Z'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
  response:
    status:
      code: 500
      message: Internal Server Error
    headers:
      Content-Type:
      - text/plain;charset=UTF-8
      Content-Length:
      - '22'
      Date:
      - Mon, 28 Jul 2025 20:31:19 GMT
      Connection:
      - close
      Server:
      - Layer7-API-Gateway
    body:
      encoding: UTF-8
      string: Unable to Retrieve Key
  recorded_at: Mon, 28 Jul 2025 20:31:20 GMT
recorded_with: VCR 6.3.1
