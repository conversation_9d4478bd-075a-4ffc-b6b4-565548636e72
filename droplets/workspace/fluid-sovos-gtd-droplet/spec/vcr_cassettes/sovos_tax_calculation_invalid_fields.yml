---
http_interactions:
- request:
    method: post
    uri: https://gtduat.sovos.com/Twe/api/rest/calcTax/doc
    body:
      encoding: UTF-8
      string: '{"usrname":"<SOVOS_USERNAME>","pswrd":"<SOVOS_PASSWORD>","rsltLvl":1,"isAudit":false,"tdcReqrd":true,"tdmRequired":false,"currn":"INVENTEDCURRENCY","trnDocNum":9,"txCalcTp":1,"lines":[{"orgCd":"<SOVOS_ORG_CODE>","lnItmId":19,"grossAmt":352.17,"goodSrvCd":"1980110101","goodSrvDesc":"Product
        1980110101","qnty":7.0,"trnTp":1,"custVendCd":null,"custVendName":null,"sTCity":"InvalidCity","sTStateProv":"XX","sTCountry":"US","sTPstlCd":"00000","sTStNameNum":"fake
        12","bTCity":"InvalidCity","bTStateProv":"XX","bTCountry":"US","bTPstlCd":"00000","bTStNameNum":"fake
        12"}]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      Authorization:
      - "<AUTHORIZATION>"
      Date:
      - '2025-07-28T20:28:21Z'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
  response:
    status:
      code: 500
      message: Internal Server Error
    headers:
      Proxy-Connection:
      - Close
      Content-Type:
      - application/json
      Content-Length:
      - '118'
      Date:
      - Mon, 28 Jul 2025 20:28:22 GMT
      Connection:
      - close
      Server:
      - Layer7-API-Gateway
    body:
      encoding: UTF-8
      string: |-
        {
          "errorCode" : "99692",
          "errorMessage" : "Could not perform the Tax Calculation. Invalid Currency in the Input"
        }
  recorded_at: Mon, 28 Jul 2025 20:28:22 GMT
recorded_with: VCR 6.3.1
