# spec/support/vcr.rb

require 'vcr'
require 'webmock/rspec'

VCR.configure do |config|
  config.cassette_library_dir = 'spec/vcr_cassettes'
  config.hook_into :webmock
  config.configure_rspec_metadata!
  
  # Allow localhost connections (for Capybara)
  config.ignore_localhost = true
  
  # Filter sensitive data from recordings
  config.filter_sensitive_data('<SOVOS_USERNAME>') { ENV['SOVOS_TEST_USERNAME'] }
  config.filter_sensitive_data('<SOVOS_PASSWORD>') { ENV['SOVOS_TEST_PASSWORD'] }
  config.filter_sensitive_data('<SOVOS_HMAC_KEY>') { ENV['SOVOS_TEST_HMAC_KEY'] }
  config.filter_sensitive_data('<SOVOS_ORG_CODE>') { ENV['SOVOS_TEST_ORG_CODE'] }
  
  # Filter Authorization headers
  config.filter_sensitive_data('<AUTHORIZATION>') do |interaction|
    interaction.request.headers['Authorization']&.first
  end
  
  # Default cassette options
  config.default_cassette_options = {
    record: :once, # Record once, then replay
    match_requests_on: [:method, :uri, :body],
    allow_unused_http_interactions: false
  }
  
  # Automatically use VCR for tests tagged with :vcr
  config.configure_rspec_metadata!
  
  # Debug mode (optional)
  config.debug_logger = File.open('log/vcr.log', 'w') if ENV['VCR_DEBUG']
end

# Configure WebMock
WebMock.disable_net_connect!(
  allow_localhost: true,
  allow: 'chromedriver.storage.googleapis.com' # For system tests
)
