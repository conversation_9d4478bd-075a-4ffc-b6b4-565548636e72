# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OrderCalculator::TransformOrderToSovosFormat do
  describe '.call' do
    let(:sovos_gtd_setting) do
      build(:sovos_gtd_setting,
        username: 'test_user',
        password: 'test_pass',
        hmac_key: 'test_hmac_key',
        organization_identification_code: 'TEST_ORG'
      )
    end

    let(:order_data) do
      {
        id: 6404252,
        order_number: "1002",
        amount: "87.56",
        currency_code: "USD",
        discount: "0.0",
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        items: [
          {
            id: 14628588,
            price: "87.56",
            quantity: 1,
            sku: "CTZHOME",
            title: "Citizen Cabin"
          }
        ],
        ship_to: {
          id: 11665930,
          address1: "Test 123",
          city: "San Francisco",
          country_code: "US",
          first_name: "<PERSON>",
          last_name: "<PERSON>",
          postal_code: "94102",
          state: "California",
          subdivision_code: "CA"
        },
        shipping: "0.0",
        subtotal: "87.56"
      }
    end

    let(:context) do
      {
        sovos_gtd_setting: sovos_gtd_setting,
        order_data: order_data
      }
    end

    subject { described_class.call(context) }

    context 'when all required data is present' do
      it 'succeeds' do
        expect(subject).to be_success
      end

      it 'transforms the order to Sovos format' do
        result = subject
        expect(result.sovos_payload).to be_present
        expect(result.sovos_payload[:usrname]).to eq('test_user')
        expect(result.sovos_payload[:pswrd]).to eq('test_pass')
        expect(result.sovos_payload[:currn]).to eq('USD')
        expect(result.sovos_payload[:trnDocNum]).to eq('1002')
        expect(result.sovos_payload[:lines]).to be_present
      end

      it 'transforms line items correctly' do
        result = subject
        lines = result.sovos_payload[:lines]
        expect(lines.length).to eq(1)

        line = lines.first
        expect(line[:orgCd]).to eq('TEST_ORG')
        expect(line[:lnItmId]).to eq(14628588)
        expect(line[:grossAmt]).to eq(87.56)
        expect(line[:goodSrvCd]).to eq('CTZHOME')
        expect(line[:goodSrvDesc]).to eq('Citizen Cabin')
        expect(line[:qnty]).to eq(1.0)
        expect(line[:trnTp]).to eq(1)
        expect(line[:custVendCd]).to eq('<EMAIL>')
        expect(line[:custVendName]).to eq('John Smith')
      end

      it 'includes shipping address information' do
        result = subject
        line = result.sovos_payload[:lines].first

        expect(line[:sTCity]).to eq('San Francisco')
        expect(line[:sTStateProv]).to eq('CA')
        expect(line[:sTCountry]).to eq('US')
        expect(line[:sTPstlCd]).to eq('94102')
        expect(line[:sTStNameNum]).to eq('Test 123')
      end

      it 'sets bill to same as ship to' do
        result = subject
        line = result.sovos_payload[:lines].first

        expect(line[:bTCity]).to eq('San Francisco')
        expect(line[:bTStateProv]).to eq('CA')
        expect(line[:bTCountry]).to eq('US')
        expect(line[:bTPstlCd]).to eq('94102')
        expect(line[:bTStNameNum]).to eq('Test 123')
      end
    end

    context 'when processing a refund/cancellation order' do
      let(:context) do
        {
          sovos_gtd_setting: sovos_gtd_setting,
          order_data: order_data,
          is_refund_or_cancellation: true
        }
      end

      it 'succeeds' do
        expect(subject).to be_success
      end

      it 'creates a unique refund document number' do
        result = subject
        expect(result.sovos_payload[:trnDocNum]).to start_with('Cancel-1002-')
        expect(result.sovos_payload[:trnDocNum]).to_not eq('1002')
      end

      it 'includes the original document number' do
        result = subject
        expect(result.sovos_payload[:origDocNum]).to eq('1002')
      end

      it 'sets transaction type to Return A/R (14)' do
        result = subject
        lines = result.sovos_payload[:lines]
        expect(lines.first[:trnTp]).to eq(14)
      end

      it 'sets debit/credit indicator to Credit (2)' do
        result = subject
        lines = result.sovos_payload[:lines]
        expect(lines.first[:debCredIndr]).to eq(2)
      end

      it 'maintains all other fields correctly' do
        result = subject
        expect(result.sovos_payload[:usrname]).to eq('test_user')
        expect(result.sovos_payload[:pswrd]).to eq('test_pass')
        expect(result.sovos_payload[:currn]).to eq('USD')
        expect(result.sovos_payload[:isAudit]).to eq(true)
      end
    end

    context 'when processing a regular sale order' do
      let(:context) do
        {
          sovos_gtd_setting: sovos_gtd_setting,
          order_data: order_data,
          is_refund_or_cancellation: false
        }
      end

      it 'succeeds' do
        expect(subject).to be_success
      end

      it 'uses the original order number as document number' do
        result = subject
        expect(result.sovos_payload[:trnDocNum]).to eq('1002')
      end

      it 'does not include original document number' do
        result = subject
        expect(result.sovos_payload[:origDocNum]).to be_nil
      end

      it 'sets transaction type to Sale (1)' do
        result = subject
        lines = result.sovos_payload[:lines]
        expect(lines.first[:trnTp]).to eq(1)
      end

      it 'does not include debit/credit indicator' do
        result = subject
        lines = result.sovos_payload[:lines]
        expect(lines.first[:debCredIndr]).to be_nil
      end
    end

    context 'when sovos_gtd_setting is missing' do
      let(:context) { { order_data: order_data } }

      it 'fails' do
        expect(subject).to be_failure
        expect(subject.error).to eq('Missing sovos_gtd_setting')
      end
    end

    context 'when order_data is missing' do
      let(:context) { { sovos_gtd_setting: sovos_gtd_setting } }

      it 'fails' do
        expect(subject).to be_failure
        expect(subject.error).to eq('Missing order_data')
      end
    end

    context 'when order has no items' do
      let(:order_data) do
        {
          id: 6404252,
          order_number: "1002",
          currency_code: "USD",
          email: "<EMAIL>",
          first_name: "John",
          last_name: "Smith",
          items: [],
          ship_to: {
            city: "San Francisco",
            state: "California",
            country_code: "US",
            postal_code: "94102",
            address1: "Test 123"
          }
        }
      end

      it 'succeeds with empty lines' do
        result = subject
        expect(result).to be_success
        expect(result.sovos_payload[:lines]).to eq([])
      end
    end

    context 'when order has no shipping address' do
      let(:order_data) do
        {
          id: 6404252,
          order_number: "1002",
          currency_code: "USD",
          email: "<EMAIL>",
          first_name: "John",
          last_name: "Smith",
          items: [
            {
              id: 14628588,
              price: "87.56",
              quantity: 1,
              sku: "CTZHOME",
              title: "Citizen Cabin"
            }
          ]
        }
      end

      it 'succeeds without address fields' do
        result = subject
        expect(result).to be_success
        line = result.sovos_payload[:lines].first
        expect(line[:sTCity]).to be_nil
        expect(line[:sTStateProv]).to be_nil
      end
    end
  end
end
