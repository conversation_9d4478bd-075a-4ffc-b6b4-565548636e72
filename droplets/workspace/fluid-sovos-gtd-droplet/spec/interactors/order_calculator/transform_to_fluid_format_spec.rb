# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OrderCalculator::TransformToFluidFormat do
  let(:interactor) { described_class.new(context) }
  let(:context) do
    Interactor::Context.new(
      sovos_response: sovos_response,
      cart_data: cart_data
    )
  end

  let(:sovos_response) do
    {
      "trnDocNum" => "12345",
      "txwTrnDocId" => "80021604",
      "txAmt" => "31.70",
      "lnRslts" => [
        {
          "lnNm" => "1",
          "lnId" => "item1",
          "txwLnId" => "80016664",
          "txAmt" => "31.70",
          "grossAmt" => "352.17",
          "txRate" => "0.09",
          "jurRslts" => [
            {
              "txAmt" => "24.65",
              "txRate" => "0.07",
              "txableAmt" => "352.17",
              "exmptAmt" => "0.0",
              "txName" => "CA State Tax",
              "txNameID" => "446",
              "txTp" => "1",
              "txJurUIDCntry" => "UNITED STATES",
              "txJurUIDStatePrv" => "CALIFORNIA",
              "txJurUIDJurTp" => "2",
              "txJurUIDStatePrvISO" => "CA",
              "txJurUIDCntryISO" => "US"
            },
            {
              "txAmt" => "7.04",
              "txRate" => "0.02",
              "txableAmt" => "352.17",
              "exmptAmt" => "0.0",
              "txName" => "San Francisco County Tax",
              "txNameID" => "447",
              "txTp" => "1",
              "txJurUIDCntry" => "UNITED STATES",
              "txJurUIDStatePrv" => "CALIFORNIA",
              "txJurUIDCityCntyDist" => "SAN FRANCISCO",
              "txJurUIDJurTp" => "3",
              "txJurUIDStatePrvISO" => "CA",
              "txJurUIDCntryISO" => "US"
            }
          ]
        }
      ]
    }
  end

  let(:cart_data) do
    {
      "id" => 9,
      "sub_total" => "352.17",
      "sub_total_in_currency" => "$352.17",
      "shipping_total_in_currency" => "$10.00",
      "discount_total_in_currency" => "$5.00",
      "currency_code" => "USD",
      "shipping_method" => {
        "id" => 1,
        "name" => "Standard Shipping",
        "price" => "10.00"
      },
      "ship_to" => {
        "city" => "San Francisco",
        "state" => "California",
        "country" => "US",
        "postal_code" => "94105",
        "line1" => "123 Market St"
      },
      "items" => [
        {
          "id" => "item1",
          "variant_id" => 79,
          "sku" => "01KOH3B9TG",
          "price" => "50.31",
          "quantity" => 7,
          "product" => {
            "title" => "Awesome Copper Gloves"
          }
        }
      ]
    }
  end

  before do
    # Clear cache before each test
    Rails.cache.clear
    
    # Ensure SovosCache is available
    unless defined?(SovosCache)
      stub_const('SovosCache', Module.new do
        def self.generate_key(cart_data, type:)
          "sovos_cache:#{type}:#{cart_data[:id]}"
        end
      end)
    end
    
    # Stub ActiveSupport::Notifications
    allow(ActiveSupport::Notifications).to receive(:instrument)
  end

  describe '#call' do
    context 'successful transformation' do
      it 'transforms Sovos response to Fluid format' do
        interactor.call

        expect(context).to be_success
        expect(context.fluid_response).to be_present
        expect(context.transform_from_cache).to eq(false)
      end

      it 'creates the correct response structure' do
        interactor.call

        response = context.fluid_response
        
        expect(response[:success]).to eq(true)
        expect(response[:data]).to be_present
        expect(response[:data][:subtotal]).to eq(352.17)
        expect(response[:data][:taxes]).to eq(31.70)
        expect(response[:data][:shipping]).to eq(10.0)
        expect(response[:data][:discount]).to eq(5.0)
        expect(response[:data][:total]).to eq(388.87) # 352.17 + 31.70 + 10.00 - 5.00
      end

      it 'includes shipping information' do
        interactor.call

        data = context.fluid_response[:data]
        
        expect(data[:shipping_method]).to eq(cart_data["shipping_method"])
        expect(data[:shipping_object]).to eq({
          id: 1,
          price: 10.0
        })
      end

      it 'includes tax information' do
        interactor.call

        data = context.fluid_response[:data]
        
        expect(data[:tax]).to eq({
          price: 31.70
        })
      end

      it 'transforms line items with tax details' do
        interactor.call

        items = context.fluid_response[:data][:items]
        
        expect(items.size).to eq(1)
        
        item = items.first
        expect(item[:variant_id]).to eq(79)
        expect(item[:sku]).to eq("01KOH3B9TG")
        expect(item[:quantity]).to eq(7)
        expect(item[:price]).to eq(50.31)
        expect(item[:subtotal]).to eq(352.17)
        expect(item[:tax]).to eq(31.70)
        expect(item[:tax_rate]).to eq(0.09)
      end

      it 'includes tax jurisdiction breakdown' do
        interactor.call

        item = context.fluid_response[:data][:items].first
        jurisdictions = item[:tax_jurisdictions]
        
        expect(jurisdictions.size).to eq(2)
        
        state_tax = jurisdictions.first
        expect(state_tax[:name]).to eq("CA State Tax")
        expect(state_tax[:amount]).to eq(24.65)
        expect(state_tax[:rate]).to eq(0.07)
        expect(state_tax[:type]).to eq("state")
        
        county_tax = jurisdictions.last
        expect(county_tax[:name]).to eq("San Francisco County Tax")
        expect(county_tax[:amount]).to eq(7.04)
        expect(county_tax[:rate]).to eq(0.02)
        expect(county_tax[:type]).to eq("county")
      end
    end

    context 'with multiple line items' do
      let(:sovos_response) do
        super().merge(
          "lnRslts" => [
            {
              "lnId" => "item1",
              "txAmt" => "20.00",
              "grossAmt" => "200.00",
              "txRate" => "0.10",
              "jurRslts" => []
            },
            {
              "lnId" => "item2",
              "txAmt" => "11.70",
              "grossAmt" => "152.17",
              "txRate" => "0.077",
              "jurRslts" => []
            }
          ],
          "txAmt" => "31.70"
        )
      end

      let(:cart_data) do
        super().merge(
          "items" => [
            {
              "id" => "item1",
              "variant_id" => 79,
              "sku" => "SKU1",
              "price" => "100.00",
              "quantity" => 2
            },
            {
              "id" => "item2",
              "variant_id" => 80,
              "sku" => "SKU2",
              "price" => "50.72",
              "quantity" => 3
            }
          ]
        )
      end

      it 'transforms multiple line items correctly' do
        interactor.call

        items = context.fluid_response[:data][:items]
        
        expect(items.size).to eq(2)
        
        expect(items[0][:variant_id]).to eq(79)
        expect(items[0][:tax]).to eq(20.00)
        
        expect(items[1][:variant_id]).to eq(80)
        expect(items[1][:tax]).to eq(11.70)
      end
    end

    context 'with missing optional fields' do
      let(:cart_data) do
        {
          "id" => 9,
          "sub_total" => "352.17",
          "items" => [
            {
              "id" => "item1",
              "quantity" => 7
            }
          ]
        }
      end

      it 'handles missing fields gracefully' do
        interactor.call

        expect(context).to be_success
        
        data = context.fluid_response[:data]
        expect(data[:shipping]).to eq(0.0)
        expect(data[:discount]).to eq(0.0)
        
        item = data[:items].first
        expect(item[:variant_id]).to be_nil
        expect(item[:sku]).to be_nil
      end
    end

    context 'with free shipping' do
      let(:cart_data) do
        super().merge(
          "shipping_total_in_currency" => "$0.00",
          "shipping_method" => nil
        )
      end

      it 'handles free shipping correctly' do
        interactor.call

        data = context.fluid_response[:data]
        
        expect(data[:shipping]).to eq(0.0)
        expect(data[:shipping_object][:price]).to eq(0.0)
        expect(data[:shipping_object][:id]).to be_nil
      end
    end

    context 'caching behavior' do
      it 'caches the transformation' do
        expect(Rails.cache).to receive(:write).with(
          anything,
          anything,
          expires_in: 30.minutes
        )

        interactor.call
      end

      context 'when cache hit occurs' do
        let(:cached_response) do
          {
            success: true,
            data: {
              subtotal: 100.0,
              taxes: 10.0,
              total: 110.0
            }
          }
        end

        before do
          # Cache reads are not mocked, so we need to actually check if cache was read
          allow(Rails.cache).to receive(:read).and_return(cached_response)
        end

        it 'uses cached transformation' do
          interactor.call

          expect(context.fluid_response).to eq(cached_response)
          expect(context.transform_from_cache).to eq(true)
        end
      end
    end

    context 'error handling' do
      it 'handles transformation errors' do
        allow_any_instance_of(described_class).to receive(:transform_sovos_to_fluid_format).and_raise(StandardError, 'Transformation error')

        expect { interactor.call }.to raise_error(Interactor::Failure)
        
        expect(context).to be_failure
        expect(context.error).to eq('Error transforming to Fluid format: Transformation error')
      end
    end

    context 'numeric value extraction' do
      let(:cart_data) do
        super().merge(
          "sub_total_in_currency" => "$1,234.56",
          "shipping_total_in_currency" => "Free",
          "discount_total_in_currency" => "-$50.00"
        )
      end

      it 'correctly extracts numeric values from various formats' do
        interactor.call

        data = context.fluid_response[:data]
        
        expect(data[:subtotal]).to eq(1234.56)
        expect(data[:shipping]).to eq(0.0)
        expect(data[:discount]).to eq(-50.0)  # Negative value is preserved
      end
    end

    context 'jurisdiction type mapping' do
      let(:sovos_response) do
        super().tap do |response|
          response["lnRslts"][0]["jurRslts"] = [
            { "txJurUIDJurTp" => "1", "txName" => "US Tax", "txAmt" => "1.0", "txRate" => "0.01" },
            { "txJurUIDJurTp" => "2", "txName" => "State Tax", "txAmt" => "2.0", "txRate" => "0.02" },
            { "txJurUIDJurTp" => "3", "txName" => "County Tax", "txAmt" => "3.0", "txRate" => "0.03" },
            { "txJurUIDJurTp" => "4", "txName" => "City Tax", "txAmt" => "4.0", "txRate" => "0.04" },
            { "txJurUIDJurTp" => "5", "txName" => "District Tax", "txAmt" => "5.0", "txRate" => "0.05" },
            { "txJurUIDJurTp" => "99", "txName" => "Unknown Tax", "txAmt" => "6.0", "txRate" => "0.06" }
          ]
        end
      end

      it 'maps all jurisdiction types correctly' do
        interactor.call

        jurisdictions = context.fluid_response[:data][:items].first[:tax_jurisdictions]
        
        expect(jurisdictions[0][:type]).to eq("country")
        expect(jurisdictions[1][:type]).to eq("state")
        expect(jurisdictions[2][:type]).to eq("county")
        expect(jurisdictions[3][:type]).to eq("city")
        expect(jurisdictions[4][:type]).to eq("district")
        expect(jurisdictions[5][:type]).to eq("unknown")
      end
    end
  end
end
