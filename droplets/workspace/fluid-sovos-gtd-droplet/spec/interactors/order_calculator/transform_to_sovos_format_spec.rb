# frozen_string_literal: true

require 'rails_helper'

# Ensure SovosCache is loaded
require_relative '../../../app/services/sovos_cache'

RSpec.describe OrderCalculator::TransformToSovosFormat do

  let(:interactor) { described_class.new(context) }
  let(:context) { Interactor::Context.new(cart_data: cart_data, sovos_gtd_setting: sovos_gtd_setting) }
  
  let(:sovos_gtd_setting) do
    double(
      username: 'test_user',
      password: 'test_password',
      organization_identification_code: 'TEST_ORG'
    )
  end

  let(:cart_data) do
    {
      id: '12345',
      currency_code: 'USD',
      shipping_total_in_currency: '$10.00',
      buyer_rep_id: 'BUYER001',
      buyer_name: 'Test Buyer',
      ship_to: {
        city: 'Denver',
        state: 'Colorado',
        country_code: 'US',
        postal_code: '80202',
        address1: '123 Main St'
      },
      items: [
        {
          id: 'item1',
          sku: 'SKU123',
          variant_id: 'VAR123',
          name: 'Test Product',
          price: 50.00,
          quantity: 2
        }
      ]
    }
  end

  before do
    # Set up thread storage for sovos_gtd_setting
    Thread.current[:current_sovos_integration_setting] = sovos_gtd_setting
    
    # Stub Rails.cache methods
    allow(Rails.cache).to receive(:read).and_return(nil)
    allow(Rails.cache).to receive(:write).and_return(true)
    
    # Stub SovosApi configuration check
    allow(SovosApi).to receive(:configuration).and_return(
      double(cache_enabled: true)
    ) if defined?(SovosApi)
    
    # Stub ActiveSupport::Notifications
    allow(ActiveSupport::Notifications).to receive(:instrument)
  end

  after do
    # Clean up thread storage
    Thread.current[:current_sovos_integration_setting] = nil
  end

  describe '#call' do
    context 'successful transformation' do
      it 'transforms cart data to Sovos format' do
        interactor.call

        expect(context).to be_success
        expect(context.sovos_payload).to be_present
        expect(context.transform_from_cache).to eq(false)
      end

      it 'creates the correct payload structure' do
        interactor.call

        payload = context.sovos_payload
        
        expect(payload[:usrname]).to eq('test_user')
        expect(payload[:pswrd]).to eq('test_password')
        expect(payload[:rsltLvl]).to eq(1)
        expect(payload[:isAudit]).to eq(false)
        expect(payload[:tdcReqrd]).to eq(true)
        expect(payload[:tdmRequired]).to eq(false)
        expect(payload[:currn]).to eq('USD')
        expect(payload[:trnDocNum]).to eq('12345')
        expect(payload[:txCalcTp]).to eq(1)
        expect(payload[:lines]).to be_an(Array)
      end

      it 'transforms line items correctly' do
        interactor.call

        line_item = context.sovos_payload[:lines].first
        
        expect(line_item[:orgCd]).to eq('TEST_ORG')
        expect(line_item[:lnItmId]).to eq('item1')
        expect(line_item[:grossAmt]).to eq(100.0) # 50.00 * 2
        expect(line_item[:goodSrvCd]).to eq('SKU123')
        expect(line_item[:goodSrvDesc]).to eq('Test Product')
        expect(line_item[:qnty]).to eq(2.0)
        expect(line_item[:trnTp]).to eq(1)
        expect(line_item[:custVendCd]).to eq('BUYER001')
        expect(line_item[:custVendName]).to eq('Test Buyer')
      end

      it 'includes ship to address in line items' do
        interactor.call

        line_item = context.sovos_payload[:lines].first
        
        expect(line_item[:sTCity]).to eq('Denver')
        expect(line_item[:sTStateProv]).to eq('CO')  # Changed from 'COLORADO' to 'CO'
        expect(line_item[:sTCountry]).to eq('US')
        expect(line_item[:sTPstlCd]).to eq('80202')
        expect(line_item[:sTStNameNum]).to eq('123 Main St')
      end

      context 'with free shipping' do
        let(:cart_data) do
          super().merge(shipping_total_in_currency: '$0.00')
        end

        it 'does not include dlvrAmt when shipping is free' do
          interactor.call
          
          expect(context.sovos_payload[:dlvrAmt]).to be_nil
        end
      end

      context 'with paid shipping' do
        it 'includes dlvrAmt when shipping has a cost' do
          interactor.call
          
          expect(context.sovos_payload[:dlvrAmt]).to eq(10.0)
        end
      end

      context 'with multiple items' do
        let(:cart_data) do
          super().merge(
            items: [
              {
                id: 'item1',
                sku: 'SKU123',
                name: 'Product 1',
                price: 50.00,
                quantity: 2
              },
              {
                id: 'item2',
                sku: 'SKU456',
                name: 'Product 2',
                price: 30.00,
                quantity: 1
              }
            ]
          )
        end

        it 'transforms multiple line items' do
          interactor.call
          
          lines = context.sovos_payload[:lines]
          expect(lines.size).to eq(2)
          expect(lines[0][:lnItmId]).to eq('item1')
          expect(lines[1][:lnItmId]).to eq('item2')
        end
      end

      context 'when item is missing variant SKU' do
        let(:cart_data) do
          super().merge(
            items: [{
              id: 'item1',
              product: { sku: 'PROD_SKU' },
              name: 'Test Product',
              price: 50.00,
              quantity: 1
            }]
          )
        end

        it 'uses variant_id as fallback' do
          interactor.call
          
          line_item = context.sovos_payload[:lines].first
          expect(line_item[:goodSrvCd]).to eq('')  # No SKU, no variant_id, so empty string
        end
      end

      context 'when item has variant_id but no SKU' do
        let(:cart_data) do
          super().merge(
            items: [{
              id: 'item1',
              variant_id: 'VAR123',
              name: 'Test Product',
              price: 50.00,
              quantity: 1
            }]
          )
        end

        it 'uses variant_id as goodSrvCd' do
          interactor.call

          line_item = context.sovos_payload[:lines].first
          expect(line_item[:goodSrvCd]).to eq('VAR123')
        end
      end

      context 'with commit_transaction context (not yet implemented)' do
        let(:context) do
          Interactor::Context.new(
            cart_data: cart_data,
            sovos_gtd_setting: sovos_gtd_setting,
            commit_transaction: true
          )
        end

        it 'sets isAudit to false (commit_transaction not yet implemented)' do
          interactor.call

          expect(context.sovos_payload[:isAudit]).to eq(false)
        end
      end
    end

    context 'caching behavior' do
      context 'when caching is enabled' do
        it 'caches the transformation' do
          expect(Rails.cache).to receive(:write).with(
            anything,
            anything,
            expires_in: 1.hour
          )

          interactor.call
        end
      end

      context 'when cache hit occurs' do
        let(:cached_payload) { { cached: 'payload' } }

        before do
          allow(Rails.cache).to receive(:read).and_return(cached_payload)
        end

        it 'uses cached transformation' do
          interactor.call

          expect(context.sovos_payload).to eq(cached_payload)
          expect(context.transform_from_cache).to eq(true)
        end
      end
    end

    context 'error handling' do
      it 'handles transformation errors' do
        allow_any_instance_of(described_class).to receive(:transform_cart_to_sovos_format).and_raise(StandardError, 'Test error')

        # Interactor#call raises Interactor::Failure when context.fail! is called
        # We need to rescue it to test the failure state
        expect { interactor.call }.to raise_error(Interactor::Failure)

        expect(context).to be_failure
        expect(context.error).to eq('Error transforming to Sovos format: Test error')
      end
    end

    context 'state normalization' do
      it 'normalizes state names to uppercase' do
        interactor.call

        line_item = context.sovos_payload[:lines].first
        expect(line_item[:sTStateProv]).to eq('CO')  # Changed from 'COLORADO' to 'CO'
      end

      context 'with UsStateNormalizer available' do
        before do
          stub_const('UsStateNormalizer', Class.new do
            def self.to_code(state)
              'CO' if state.downcase == 'colorado'
            end
          end)
        end

        it 'uses UsStateNormalizer when available' do
          interactor.call

          line_item = context.sovos_payload[:lines].first
          expect(line_item[:sTStateProv]).to eq('CO')
        end
      end
    end
  end
end
