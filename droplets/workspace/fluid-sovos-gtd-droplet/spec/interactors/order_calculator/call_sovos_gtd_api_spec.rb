# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OrderCalculator::CallSovosGtdApi do
  describe '.call' do
    let(:sovos_gtd_setting) do
      build(:sovos_gtd_setting,
        username: 'test_user',
        password: 'test_pass',
        hmac_key: 'test_hmac_key',
        organization_identification_code: 'TEST_ORG'
      )
    end

    let(:cart_data) do
      {
        id: 123,
        currency_code: 'USD',
        sub_total: '100.00',
        items: [
          {
            id: 1,
            quantity: 2,
            price: '50.00',
            sku: 'SKU123'
          }
        ],
        ship_to: {
          city: 'Austin',
          state: 'TX',
          postal_code: '78701',
          country_code: 'US'
        }
      }
    end

    let(:sovos_payload) do
      {
        'usrname' => 'test_user',
        'pswrd' => 'test_pass',
        'rsltLvl' => 1,
        'isAudit' => false,
        'currn' => 'USD',
        'trnDocNum' => '123',
        'lines' => [
          {
            'lnItmId' => 1,
            'grossAmt' => 100.00,
            'qnty' => 2,
            'sTCity' => 'Austin',
            'sTStateProv' => 'TX',
            'sTPstlCd' => '78701'
          }
        ]
      }
    end

    let(:sovos_response) do
      {
        txAmt: '8.25',
        trnDocNum: '123',
        lnRslts: [
          {
            lnNm: '1',
            txAmt: '8.25',
            grossAmt: '100.00',
            txRate: '0.0825'
          }
        ]
      }
    end

    let(:context) do
      {
        sovos_gtd_setting: sovos_gtd_setting,
        sovos_payload: sovos_payload,
        cart_data: cart_data,
        calculation_type: 'quote',
        skip_cache_check: false
      }
    end

    let(:api_client) { instance_double(SovosApi::Client) }
    
    let(:validator_result) do
      result = Interactor::Context.new
      result.success = true
      result.payload = sovos_response
      result
    end

    before do
      allow(SovosApi::Client).to receive(:new).and_return(api_client)
      # Mock Rails logger to avoid logging during tests
      allow(Rails.logger).to receive(:info)
      allow(Rails.logger).to receive(:debug)
      allow(Rails.logger).to receive(:error)
      allow(Rails.logger).to receive(:warn)
      allow(Rails.logger).to receive(:tagged).and_yield
    end

    describe 'successful API call' do
      before do
        allow(api_client).to receive(:post).with('/calcTax/doc', sovos_payload).and_return(sovos_response)
        allow(SovosApi::ResponseValidator).to receive(:call).and_return(validator_result)
        allow(SovosCache).to receive(:store).and_return({ key: 'cache_key', expires_at: 1.hour.from_now })
        # Ensure caching is enabled for these tests
        allow(SovosApi.configuration).to receive(:cache_enabled).and_return(true)
        # Mock Rails.cache to return nil (cache miss)
        allow(Rails.cache).to receive(:read).and_return(nil)
      end

      it 'calls the Sovos API and returns the response' do
        result = described_class.call(context)

        expect(result).to be_success
        expect(result.sovos_response).to eq(sovos_response)
        expect(result.from_cache).to be false
      end

      it 'validates the response using SovosApi::ResponseValidator' do
        expect(SovosApi::ResponseValidator).to receive(:call).with(
          response: sovos_response,
          calculation_type: 'quote'
        ).and_return(validator_result)

        described_class.call(context)
      end

      context 'when caching is enabled' do
        it 'caches the successful response' do
          expect(SovosCache).to receive(:store).with(
            cart_data,
            sovos_response,
            type: :tax_calculation
          ).and_return({ key: 'cache_key', expires_at: 1.hour.from_now })

          described_class.call(context)
        end
      end

      context 'when skip_cache_check is false' do
        before do
          # Mock the cache key generation and cache read
          allow(SovosCache).to receive(:generate_key).with(cart_data, type: :tax_calculation).and_return('test_cache_key')
          allow(Rails.cache).to receive(:read).with('test_cache_key').and_return(nil)
        end

        it 'checks the cache before making API call' do
          expect(SovosCache).to receive(:generate_key).with(cart_data, type: :tax_calculation).and_return('test_cache_key')
          expect(Rails.cache).to receive(:read).with('test_cache_key').and_return(nil)

          described_class.call(context)
        end
      end

      context 'when skip_cache_check is true' do
        before { context[:skip_cache_check] = true }

        it 'does not check the cache' do
          expect(SovosCache).not_to receive(:generate_key)
          expect(Rails.cache).not_to receive(:read)

          described_class.call(context)
        end
      end
    end

    describe 'cache hit' do
      before do
        # Mock caching to be enabled
        allow(SovosApi.configuration).to receive(:cache_enabled).and_return(true)
        # Mock the cache key generation
        allow(SovosCache).to receive(:generate_key).with(cart_data, type: :tax_calculation).and_return('test_cache_key')
        # Mock the cache to return a value (cache hit)
        allow(Rails.cache).to receive(:read).with('test_cache_key').and_return(sovos_response)
      end

      it 'returns cached response without calling API' do
        expect(api_client).not_to receive(:post)

        result = described_class.call(context)

        expect(result).to be_success
        expect(result.sovos_response).to eq(sovos_response)
        expect(result.from_cache).to be true
      end

      it 'logs cache hit' do
        expect(Rails.logger).to receive(:info).with(/Using cached Sovos API response/)

        described_class.call(context)
      end
    end

    describe 'validation failures' do
      context 'when sovos_payload is not a Hash' do
        before { context[:sovos_payload] = 'invalid' }

        it 'fails with appropriate error' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Invalid input: sovos_payload must be a Hash')
        end
      end

      context 'when sovos_gtd_setting is missing' do
        before { context[:sovos_gtd_setting] = nil }

        it 'fails with appropriate error' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Missing sovos_gtd_setting')
        end
      end

      context 'when critical fields are missing' do
        before { context[:sovos_payload].delete('lines') }

        it 'fails with appropriate error' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Missing critical fields for API call: lines')
        end
      end

      context 'when multiple critical fields are missing' do
        before do
          context[:sovos_payload].delete('usrname')
          context[:sovos_payload].delete('pswrd')
        end

        it 'fails with appropriate error listing all missing fields' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Missing critical fields for API call: usrname, pswrd')
        end
      end
    end

    describe 'API error handling' do
      before do
        # Mock cache to return nil (cache miss) for error scenarios
        allow(SovosCache).to receive(:generate_key).with(cart_data, type: :tax_calculation).and_return('test_cache_key')
        allow(Rails.cache).to receive(:read).with('test_cache_key').and_return(nil)
      end

      context 'when API returns validation error' do
        let(:error) { SovosApi::Errors::ValidationError.new('Invalid address') }

        before do
          allow(api_client).to receive(:post).and_raise(error)
        end

        it 'fails with validation error message' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Validation error: Invalid address')
        end
      end

      context 'when API returns authentication error' do
        let(:error) { SovosApi::Errors::AuthenticationError.new('Invalid credentials') }

        before do
          allow(api_client).to receive(:post).and_raise(error)
        end

        it 'fails with authentication error message' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Authentication error: Invalid credentials')
        end
      end

      context 'when API returns server error' do
        let(:error) { SovosApi::Errors::ServerError.new('Internal server error') }

        before do
          allow(api_client).to receive(:post).and_raise(error)
        end

        it 'fails with server error message' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Sovos server error: Internal server error')
        end
      end

      context 'when network timeout occurs' do
        let(:error) { SovosApi::Errors::TimeoutError.new('Request timed out') }

        before do
          allow(api_client).to receive(:post).and_raise(error)
        end

        it 'retries the request once' do
          expect(api_client).to receive(:post).twice.and_raise(error)

          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Connection error: Request timed out')
        end

        it 'succeeds if retry is successful' do
          expect(api_client).to receive(:post).once.and_raise(error)
          expect(api_client).to receive(:post).once.and_return(sovos_response)
          allow(SovosApi::ResponseValidator).to receive(:call).and_return(validator_result)
          allow(SovosCache).to receive(:store)

          result = described_class.call(context)

          expect(result).to be_success
          expect(result.sovos_response).to eq(sovos_response)
        end
      end

      context 'when connection error occurs' do
        let(:error) { SovosApi::Errors::ConnectionError.new('Connection refused') }

        before do
          allow(api_client).to receive(:post).and_raise(error)
        end

        it 'retries the request once' do
          expect(api_client).to receive(:post).twice.and_raise(error)

          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Connection error: Connection refused')
        end
      end

      context 'when unexpected error occurs' do
        let(:error) { StandardError.new('Unexpected error') }

        before do
          allow(api_client).to receive(:post).and_raise(error)
        end

        it 'fails with unexpected error message' do
          result = described_class.call(context)

          expect(result).to be_failure
          expect(result.error).to eq('Unexpected error: Unexpected error')
        end
      end
    end

    describe 'edge cases' do
      context 'when cart_data is missing' do
        before do
          context.delete(:cart_data)
          allow(api_client).to receive(:post).and_return(sovos_response)
          allow(SovosApi::ResponseValidator).to receive(:call).and_return(validator_result)
        end

        it 'still processes successfully but skips caching' do
          expect(SovosCache).not_to receive(:store)

          result = described_class.call(context)

          expect(result).to be_success
        end
      end

      context 'when response has no tax amount' do
        let(:sovos_response) do
          {
            trnDocNum: '123',
            lnRslts: []
          }
        end

        before do
          allow(api_client).to receive(:post).and_return(sovos_response)
          allow(SovosApi::ResponseValidator).to receive(:call).and_return(validator_result)
          # Mock cache to return nil (cache miss)
          allow(SovosCache).to receive(:generate_key).with(cart_data, type: :tax_calculation).and_return('test_cache_key')
          allow(Rails.cache).to receive(:read).with('test_cache_key').and_return(nil)
          allow(SovosCache).to receive(:store)
        end

        it 'handles response without tax amount' do
          result = described_class.call(context)

          expect(result).to be_success
          expect(result.sovos_response[:txAmt]).to be_nil
        end
      end
    end
  end
end
