# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CallbackProcessorController, type: :controller do
  describe 'POST #update_cart_tax' do
    let(:cart_data) do
      {
        "cart" => {
          "id" => 9,
          "agreements" => [],
          "amount_total" => "352.17",
          "amount_total_in_currency" => "$352.17",
          "attribution" => nil,
          "available_payment_methods" => [
            {
              "id" => 1,
              "type" => "credit_card",
              "name" => "Credit Card",
              "supported_brands" => [],
              "test" => true,
              "sandbox" => true,
              "bogus_gateway" => false,
              "tokenize_path" => nil,
              "apple_pay" => false,
              "apple_pay_merchant_identifier" => nil
            }
          ],
          "available_shipping_methods" => [],
          "bill_to" => nil,
          "buyer_rep_id" => nil,
          "cart_token" => "xBSwvkPquxngxsKAJMRsUhXZ",
          "company" => {
            "id" => *********,
            "checkout_primary_button_color" => nil,
            "checkout_primary_text_color" => nil,
            "checkout_secondary_button_color" => nil,
            "checkout_secondary_text_color" => nil,
            "collect_phone" => nil,
            "fluid_shop" => nil,
            "logo_url" => "https://cdn.filestackcontent.com/1Rnqia44T2mLcXuFZCTV",
            "name" => "Fluid",
            "primary_domain_hostname" => nil,
            "require_billing_zip" => nil,
            "require_phone" => nil,
            "subdomain" => "fluid"
          },
          "country" => {
            "id" => 214,
            "iso" => "US",
            "name" => "United States",
            "province_required" => false
          },
          "currency_code" => "USD",
          "currency_symbol" => "$",
          "cv_total" => 0,
          "digital_only" => true,
          "discount_code" => nil,
          "discount_total" => "0.0",
          "discount_total_in_currency" => "$0.00",
          "email" => "",
          "enrollment_fee" => 0,
          "enrollment_fee_in_currency" => "$0.00",
          "enrollment_pack" => nil,
          "enrollment_pack_id" => nil,
          "immutable_items" => false,
          "items" => [
            {
              "id" => 19,
              "allow_subscription" => true,
              "cv" => 0,
              "discount_amount" => "0.0",
              "discount_amount_in_currency" => "$0.00",
              "display_to_customer" => true,
              "enrollment" => false,
              "enrollment_pack_id" => nil,
              "errors" => nil,
              "price" => "50.31",
              "price_in_currency" => "$50.31",
              "product" => {
                "id" => 179,
                "cv" => 0,
                "image_path" => nil,
                "image_url" => "https://d1r16g5m5p3ba7.cloudfront.net/uploads/product/image/*********/gotime.png",
                "price" => "50.31",
                "price_in_currency" => "$50.31",
                "sku" => "1980110101",
                "tax" => "0.0",
                "tax_in_currency" => "$0.00",
                "title" => "Awesome Copper Gloves 35"
              },
              "product_title" => "Awesome Copper Gloves 35",
              "quantity" => 7,
              "qv" => 0,
              "subscribe_and_save" => nil,
              "subscribe_and_save_for_display" => nil,
              "subscribe_and_save_in_currency" => nil,
              "subscription" => false,
              "subscription_interval" => nil,
              "subscription_interval_unit" => nil,
              "subscription_only" => false,
              "subscription_plan_id" => nil,
              "subscription_plans" => [],
              "subscription_price" => "50.31",
              "subscription_price_in_currency" => "$50.31",
              "subscription_start" => nil,
              "tax" => "0.0",
              "tax_in_currency" => "$0.00",
              "title" => nil,
              "variant" => {
                "id" => 79,
                "image_path" => nil,
                "image_url" => nil,
                "price" => "50.31",
                "price_in_currency" => "$50.31",
                "primary_image" => nil,
                "sku" => "01KOH3B9TG",
                "title" => "Default Variant"
              }
            }
          ],
          "language_iso" => "en",
          "payment_method" => nil,
          "phone" => nil,
          "processed" => false,
          "qv_total" => 0,
          "recurring" => [],
          "ship_to" => {
            "id" => *********,
            "address1" => "fake 12",
            "address2" => "asd 123",
            "address3" => nil,
            "city" => "San francisco",
            "country_code" => "US",
            "default" => false,
            "first_name" => "Martin",
            "last_name" => "Villalba",
            "name" => "Martin Villalba",
            "postal_code" => "91060",
            "state" => "California",
            "subdivision_code" => nil
          },
          "shipping_method" => nil,
          "shipping_total" => "Free",
          "shipping_total_for_display" => "Free",
          "shipping_total_in_currency" => "$0.00",
          "state" => "products",
          "sub_total" => "352.17",
          "sub_total_in_currency" => "$352.17",
          "tax_total" => "0.0",
          "tax_total_in_currency" => "$0.00",
          "transaction_fee" => "0.0",
          "transaction_fee_in_currency" => "$0.00",
          "type" => "regular",
          "valid_for_checkout" => false
        },
        "change_type" => "add_items",
        "controller" => "api/v1/webhooks",
        "action" => "cart_updated",
        "webhook" => {
          "cart" => {
            "id" => 9,
            "agreements" => [],
            "amount_total" => "352.17",
            "amount_total_in_currency" => "$352.17",
            "attribution" => nil,
            "available_payment_methods" => [
              {
                "id" => 1,
                "type" => "credit_card",
                "name" => "Credit Card",
                "supported_brands" => [],
                "test" => true,
                "sandbox" => true,
                "bogus_gateway" => false,
                "tokenize_path" => nil,
                "apple_pay" => false,
                "apple_pay_merchant_identifier" => nil
              }
            ],
            "available_shipping_methods" => [],
            "bill_to" => nil,
            "buyer_rep_id" => nil,
            "cart_token" => "xBSwvkPquxngxsKAJMRsUhXZ",
            "company" => {
              "id" => *********,
              "checkout_primary_button_color" => nil,
              "checkout_primary_text_color" => nil,
              "checkout_secondary_button_color" => nil,
              "checkout_secondary_text_color" => nil,
              "collect_phone" => nil,
              "fluid_shop" => nil,
              "logo_url" => "https://cdn.filestackcontent.com/1Rnqia44T2mLcXuFZCTV",
              "name" => "Fluid",
              "primary_domain_hostname" => nil,
              "require_billing_zip" => nil,
              "require_phone" => nil,
              "subdomain" => "fluid"
            },
            "country" => {
              "id" => 214,
              "iso" => "US",
              "name" => "United States",
              "province_required" => false
            },
            "currency_code" => "USD",
            "currency_symbol" => "$",
            "cv_total" => 0,
            "digital_only" => true,
            "discount_code" => nil,
            "discount_total" => "0.0",
            "discount_total_in_currency" => "$0.00",
            "email" => "",
            "enrollment_fee" => 0,
            "enrollment_fee_in_currency" => "$0.00",
            "enrollment_pack" => nil,
            "enrollment_pack_id" => nil,
            "immutable_items" => false,
            "items" => [
              {
                "id" => 19,
                "allow_subscription" => true,
                "cv" => 0,
                "discount_amount" => "0.0",
                "discount_amount_in_currency" => "$0.00",
                "display_to_customer" => true,
                "enrollment" => false,
                "enrollment_pack_id" => nil,
                "errors" => nil,
                "price" => "50.31",
                "price_in_currency" => "$50.31",
                "product" => {
                  "id" => 179,
                  "cv" => 0,
                  "image_path" => nil,
                  "image_url" => "https://d1r16g5m5p3ba7.cloudfront.net/uploads/product/image/*********/gotime.png",
                  "price" => "50.31",
                  "price_in_currency" => "$50.31",
                  "sku" => "1980110101",
                  "tax" => "0.0",
                  "tax_in_currency" => "$0.00",
                  "title" => "Awesome Copper Gloves 35"
                },
                "product_title" => "Awesome Copper Gloves 35",
                "quantity" => 7,
                "qv" => 0,
                "subscribe_and_save" => nil,
                "subscribe_and_save_for_display" => nil,
                "subscribe_and_save_in_currency" => nil,
                "subscription" => false,
                "subscription_interval" => nil,
                "subscription_interval_unit" => nil,
                "subscription_only" => false,
                "subscription_plan_id" => nil,
                "subscription_plans" => [],
                "subscription_price" => "50.31",
                "subscription_price_in_currency" => "$50.31",
                "subscription_start" => nil,
                "tax" => "0.0",
                "tax_in_currency" => "$0.00",
                "title" => nil,
                "variant" => {
                  "id" => 79,
                  "image_path" => nil,
                  "image_url" => nil,
                  "price" => "50.31",
                  "price_in_currency" => "$50.31",
                  "primary_image" => nil,
                  "sku" => "01KOH3B9TG",
                  "title" => "Default Variant"
                }
              }
            ],
            "language_iso" => "en",
            "payment_method" => nil,
            "phone" => nil,
            "processed" => false,
            "qv_total" => 0,
            "recurring" => [],
            "ship_to" => {
              "id" => *********,
              "address1" => "fake 12",
              "address2" => "asd 123",
              "address3" => nil,
              "city" => "San francisco",
              "country_code" => "US",
              "default" => false,
              "first_name" => "Martin",
              "last_name" => "Villalba",
              "name" => "Martin Villalba",
              "postal_code" => "91060",
              "state" => "California",
              "subdivision_code" => nil
            },
            "shipping_method" => nil,
            "shipping_total" => "Free",
            "shipping_total_for_display" => "Free",
            "shipping_total_in_currency" => "$0.00",
            "state" => "products",
            "sub_total" => "352.17",
            "sub_total_in_currency" => "$352.17",
            "tax_total" => "0.0",
            "tax_total_in_currency" => "$0.00",
            "transaction_fee" => "0.0",
            "transaction_fee_in_currency" => "$0.00",
            "type" => "regular",
            "valid_for_checkout" => false
          },
          "change_type" => "add_items"
        }
      }
    end

    let!(:company) { create(:company, fluid_company_id: *********, name: 'Fluid') }
    let!(:sovos_gtd_setting) do
      create(:sovos_gtd_setting, 
        company: company,
        username: ENV.fetch('SOVOS_TEST_USERNAME', 'test_username'),
        password: ENV.fetch('SOVOS_TEST_PASSWORD', 'test_password'),
        organization_identification_code: ENV.fetch('SOVOS_TEST_ORG_CODE', 'SQUIRE'),
        hmac_key: ENV.fetch('SOVOS_TEST_HMAC_KEY', 'test_hmac_key')
      )
    end

    before do
      allow(request).to receive(:body).and_return(StringIO.new(cart_data.to_json))
      allow(request).to receive(:raw_post).and_return(cart_data.to_json)
      allow(request).to receive(:content_type).and_return('application/json')
    end

    context 'successful tax calculation with real Sovos API', :vcr do
      it 'returns the calculated tax total from Sovos' do
        VCR.use_cassette('sovos_tax_calculation_success', record: :new_episodes) do
          post :update_cart_tax, body: cart_data.to_json, as: :json
        end

        expect(response).to have_http_status(:ok)

        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('tax_total')
        expect(json_response['tax_total']).to be_a(Numeric)
        expect(json_response['tax_total']).to be > 0
      end
    end

    context 'with invalid address' do
      let(:invalid_cart_data) do
        {
          "cart" => {
            "id" => 9,
            "agreements" => [],
            "amount_total" => "352.17",
            "amount_total_in_currency" => "$352.17",
            "attribution" => nil,
            "available_payment_methods" => [
              {
                "id" => 1,
                "type" => "credit_card",
                "name" => "Credit Card",
                "supported_brands" => [],
                "test" => true,
                "sandbox" => true,
                "bogus_gateway" => false,
                "tokenize_path" => nil,
                "apple_pay" => false,
                "apple_pay_merchant_identifier" => nil
              }
            ],
            "available_shipping_methods" => [],
            "bill_to" => nil,
            "buyer_rep_id" => nil,
            "cart_token" => "xBSwvkPquxngxsKAJMRsUhXZ",
            "company" => {
              "id" => *********,
              "checkout_primary_button_color" => nil,
              "checkout_primary_text_color" => nil,
              "checkout_secondary_button_color" => nil,
              "checkout_secondary_text_color" => nil,
              "collect_phone" => nil,
              "fluid_shop" => nil,
              "logo_url" => "https://cdn.filestackcontent.com/1Rnqia44T2mLcXuFZCTV",
              "name" => "Fluid",
              "primary_domain_hostname" => nil,
              "require_billing_zip" => nil,
              "require_phone" => nil,
              "subdomain" => "fluid"
            },
            "country" => {
              "id" => 214,
              "iso" => "US",
              "name" => "United States",
              "province_required" => false
            },
            "currency_code" => "INVENTEDCURRENCY",
            "currency_symbol" => "$",
            "cv_total" => 0,
            "digital_only" => true,
            "discount_code" => nil,
            "discount_total" => "0.0",
            "discount_total_in_currency" => "$0.00",
            "email" => "",
            "enrollment_fee" => 0,
            "enrollment_fee_in_currency" => "$0.00",
            "enrollment_pack" => nil,
            "enrollment_pack_id" => nil,
            "immutable_items" => false,
            "items" => [
              {
                "id" => 19,
                "allow_subscription" => true,
                "cv" => 0,
                "discount_amount" => "0.0",
                "discount_amount_in_currency" => "$0.00",
                "display_to_customer" => true,
                "enrollment" => false,
                "enrollment_pack_id" => nil,
                "errors" => nil,
                "price" => "50.31",
                "price_in_currency" => "$50.31",
                "product" => {
                  "id" => 179,
                  "cv" => 0,
                  "image_path" => nil,
                  "image_url" => "https://d1r16g5m5p3ba7.cloudfront.net/uploads/product/image/*********/gotime.png",
                  "price" => "50.31",
                  "price_in_currency" => "$50.31",
                  "sku" => "1980110101",
                  "tax" => "0.0",
                  "tax_in_currency" => "$0.00",
                  "title" => "Awesome Copper Gloves 35"
                },
                "product_title" => "Awesome Copper Gloves 35",
                "quantity" => 7,
                "qv" => 0,
                "subscribe_and_save" => nil,
                "subscribe_and_save_for_display" => nil,
                "subscribe_and_save_in_currency" => nil,
                "subscription" => false,
                "subscription_interval" => nil,
                "subscription_interval_unit" => nil,
                "subscription_only" => false,
                "subscription_plan_id" => nil,
                "subscription_plans" => [],
                "subscription_price" => "50.31",
                "subscription_price_in_currency" => "$50.31",
                "subscription_start" => nil,
                "tax" => "0.0",
                "tax_in_currency" => "$0.00",
                "title" => nil,
                "variant" => {
                  "id" => 79,
                  "image_path" => nil,
                  "image_url" => nil,
                  "price" => "50.31",
                  "price_in_currency" => "$50.31",
                  "primary_image" => nil,
                  "sku" => "01KOH3B9TG",
                  "title" => "Default Variant"
                }
              }
            ],
            "language_iso" => "en",
            "payment_method" => nil,
            "phone" => nil,
            "processed" => false,
            "qv_total" => 0,
            "recurring" => [],
            "ship_to" => {
              "id" => *********,
              "address1" => "fake 12",
              "address2" => "asd 123",
              "address3" => nil,
              "city" => "InvalidCity",
              "country_code" => "US",
              "default" => false,
              "first_name" => "Martin",
              "last_name" => "Villalba",
              "name" => "Martin Villalba",
              "postal_code" => "00000",
              "state" => "XX",
              "subdivision_code" => nil
            },
            "shipping_method" => nil,
            "shipping_total" => "Free",
            "shipping_total_for_display" => "Free",
            "shipping_total_in_currency" => "$0.00",
            "state" => "products",
            "sub_total" => "352.17",
            "sub_total_in_currency" => "$352.17",
            "tax_total" => "0.0",
            "tax_total_in_currency" => "$0.00",
            "transaction_fee" => "0.0",
            "transaction_fee_in_currency" => "$0.00",
            "type" => "regular",
            "valid_for_checkout" => false
          },
          "change_type" => "add_items",
          "controller" => "api/v1/webhooks",
          "action" => "cart_updated",
          "webhook" => {
            "cart" => {
              "id" => 9,
              "agreements" => [],
              "amount_total" => "352.17",
              "amount_total_in_currency" => "$352.17",
              "attribution" => nil,
              "available_payment_methods" => [
                {
                  "id" => 1,
                  "type" => "credit_card",
                  "name" => "Credit Card",
                  "supported_brands" => [],
                  "test" => true,
                  "sandbox" => true,
                  "bogus_gateway" => false,
                  "tokenize_path" => nil,
                  "apple_pay" => false,
                  "apple_pay_merchant_identifier" => nil
                }
              ],
              "available_shipping_methods" => [],
              "bill_to" => nil,
              "buyer_rep_id" => nil,
              "cart_token" => "xBSwvkPquxngxsKAJMRsUhXZ",
              "company" => {
                "id" => *********,
                "checkout_primary_button_color" => nil,
                "checkout_primary_text_color" => nil,
                "checkout_secondary_button_color" => nil,
                "checkout_secondary_text_color" => nil,
                "collect_phone" => nil,
                "fluid_shop" => nil,
                "logo_url" => "https://cdn.filestackcontent.com/1Rnqia44T2mLcXuFZCTV",
                "name" => "Fluid",
                "primary_domain_hostname" => nil,
                "require_billing_zip" => nil,
                "require_phone" => nil,
                "subdomain" => "fluid"
              },
              "country" => {
                "id" => 214,
                "iso" => "US",
                "name" => "United States",
                "province_required" => false
              },
              "currency_code" => "USD",
              "currency_symbol" => "$",
              "cv_total" => 0,
              "digital_only" => true,
              "discount_code" => nil,
              "discount_total" => "0.0",
              "discount_total_in_currency" => "$0.00",
              "email" => "",
              "enrollment_fee" => 0,
              "enrollment_fee_in_currency" => "$0.00",
              "enrollment_pack" => nil,
              "enrollment_pack_id" => nil,
              "immutable_items" => false,
              "items" => [
                {
                  "id" => 19,
                  "allow_subscription" => true,
                  "cv" => 0,
                  "discount_amount" => "0.0",
                  "discount_amount_in_currency" => "$0.00",
                  "display_to_customer" => true,
                  "enrollment" => false,
                  "enrollment_pack_id" => nil,
                  "errors" => nil,
                  "price" => "50.31",
                  "price_in_currency" => "$50.31",
                  "product" => {
                    "id" => 179,
                    "cv" => 0,
                    "image_path" => nil,
                    "image_url" => "https://d1r16g5m5p3ba7.cloudfront.net/uploads/product/image/*********/gotime.png",
                    "price" => "50.31",
                    "price_in_currency" => "$50.31",
                    "sku" => "1980110101",
                    "tax" => "0.0",
                    "tax_in_currency" => "$0.00",
                    "title" => "Awesome Copper Gloves 35"
                  },
                  "product_title" => "Awesome Copper Gloves 35",
                  "quantity" => 7,
                  "qv" => 0,
                  "subscribe_and_save" => nil,
                  "subscribe_and_save_for_display" => nil,
                  "subscribe_and_save_in_currency" => nil,
                  "subscription" => false,
                  "subscription_interval" => nil,
                  "subscription_interval_unit" => nil,
                  "subscription_only" => false,
                  "subscription_plan_id" => nil,
                  "subscription_plans" => [],
                  "subscription_price" => "50.31",
                  "subscription_price_in_currency" => "$50.31",
                  "subscription_start" => nil,
                  "tax" => "0.0",
                  "tax_in_currency" => "$0.00",
                  "title" => nil,
                  "variant" => {
                    "id" => 79,
                    "image_path" => nil,
                    "image_url" => nil,
                    "price" => "50.31",
                    "price_in_currency" => "$50.31",
                    "primary_image" => nil,
                    "sku" => "01KOH3B9TG",
                    "title" => "Default Variant"
                  }
                }
              ],
              "language_iso" => "en",
              "payment_method" => nil,
              "phone" => nil,
              "processed" => false,
              "qv_total" => 0,
              "recurring" => [],
              "ship_to" => {
                "id" => *********,
                "address1" => "fake 12",
                "address2" => "asd 123",
                "address3" => nil,
                "city" => "San francisco",
                "country_code" => "US",
                "default" => false,
                "first_name" => "Martin",
                "last_name" => "Villalba",
                "name" => "Martin Villalba",
                "postal_code" => "91060",
                "state" => "California",
                "subdivision_code" => nil
              },
              "shipping_method" => nil,
              "shipping_total" => "Free",
              "shipping_total_for_display" => "Free",
              "shipping_total_in_currency" => "$0.00",
              "state" => "products",
              "sub_total" => "352.17",
              "sub_total_in_currency" => "$352.17",
              "tax_total" => "0.0",
              "tax_total_in_currency" => "$0.00",
              "transaction_fee" => "0.0",
              "transaction_fee_in_currency" => "$0.00",
              "type" => "regular",
              "valid_for_checkout" => false
            },
            "change_type" => "add_items"
          }
        }
      end

      it 'handles Sovos validation errors' do
        VCR.use_cassette('sovos_tax_calculation_invalid_fields', record: :new_episodes) do
          post :update_cart_tax, body: invalid_cart_data.to_json, as: :json
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context 'when Sovos credentials are invalid' do
      let!(:sovos_gtd_setting) do
        create(:sovos_gtd_setting, 
          company: company,
          username: 'invalid_user',
          password: 'invalid_pass',
          organization_identification_code: 'SQUIRE',
          hmac_key: 'test_hmac_key'
        )
      end

      it 'handles authentication errors' do
        VCR.use_cassette('sovos_tax_calculation_auth_error', record: :new_episodes) do
          post :update_cart_tax, body: cart_data.to_json, as: :json

          expect(response).to have_http_status(:unprocessable_entity)

          json_response = JSON.parse(response.body)
          expect(json_response['success']).to eq(false)
          expect(json_response['error']).to be_present
        end
      end
    end
  end
end
