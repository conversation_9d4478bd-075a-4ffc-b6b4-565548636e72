# Order Webhook Integration with Sovos GTD

This document explains how the new order webhook integration works with the existing Sovos GTD services.

## Overview

The system now supports processing order webhooks from Fluid Commerce and sending them to Sovos GTD for tax calculation, while maintaining backward compatibility with the existing cart webhook flow.

## Architecture

```
OrderCreatedJob → OrderService → TransformOrderToSovosFormat → CallSovosGtdApi
```

### Components

1. **OrderCreatedJob** (`app/jobs/order_created_job.rb`)
   - Receives webhook payloads from Fluid Commerce
   - Validates the payload structure
   - Calls OrderService to process the order

2. **OrderService** (`app/services/order_service.rb`)
   - Orchestrates the order processing flow
   - Sets up Sovos integration context
   - Calls the transformation and API services
   - Handles errors and logging

3. **TransformOrderToSovosFormat** (`app/interactors/order_calculator/transform_order_to_sovos_format.rb`)
   - Transforms Fluid order data to Sovos API format
   - Maps order fields to Sovos requirements
   - Handles line items, addresses, and customer information

4. **CallSovosGtdApi** (`app/interactors/order_calculator/call_sovos_gtd_api.rb`)
   - Communicates with Sovos GTD API
   - Handles authentication, requests, and responses
   - Manages caching and error handling

## Webhook Payload Structure

The system expects webhook payloads in this format:

```json
{
  "order": {
    "id": 6404252,
    "order_number": "1002",
    "amount": "87.56",
    "currency_code": "USD",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Smith",
    "items": [
      {
        "id": 14628588,
        "price": "87.56",
        "quantity": 1,
        "sku": "CTZHOME",
        "title": "Citizen Cabin"
      }
    ],
    "ship_to": {
      "address1": "123 Main St",
      "city": "San Francisco",
      "state": "California",
      "country_code": "US",
      "postal_code": "94102"
    },
    "shipping": "0.0",
    "subtotal": "87.56"
  },
  "company_id": *********,
  "event_name": "order_created"
}
```

## Sovos API Payload

The transformation service converts the order data to this Sovos format:

```json
{
  "usrname": "sovos_username",
  "pswrd": "sovos_password",
  "rsltLvl": 1,
  "isAudit": false,
  "tdcReqrd": true,
  "tdmRequired": false,
  "currn": "USD",
  "trnDocNum": "1002",
  "txCalcTp": 1,
  "dlvrAmt": 0.0,
  "lines": [
    {
      "orgCd": "ORG_CODE",
      "lnItmId": 14628588,
      "grossAmt": 87.56,
      "goodSrvCd": "CTZHOME",
      "goodSrvDesc": "Citizen Cabin",
      "qnty": 1.0,
      "trnTp": 1,
      "custVendCd": "<EMAIL>",
      "custVendName": "John Smith",
      "sTCity": "San Francisco",
      "sTStateProv": "CA",
      "sTCountry": "US",
      "sTPstlCd": "94102",
      "sTStNameNum": "123 Main St",
      "bTCity": "San Francisco",
      "bTStateProv": "CA",
      "bTCountry": "US",
      "bTPstlCd": "94102",
      "bTStNameNum": "123 Main St"
    }
  ]
}
```

## Key Features

### Backward Compatibility
- Existing cart webhook flow remains unchanged
- `CallSovosGtdApi` service handles both `cart_data` and `order_data`
- Caching and error handling work for both flows

### Error Handling
- Comprehensive validation of webhook payloads
- Detailed error logging for debugging
- Graceful fallbacks for missing data

### Performance
- Transformation timing metrics
- Caching support for API responses
- Instrumentation for monitoring

### Flexibility
- Supports orders with or without shipping addresses
- Handles various item structures
- Configurable Sovos settings per company

## Usage

### Processing an Order Webhook

```ruby
# The webhook controller automatically triggers this flow
OrderCreatedJob.perform_later(webhook_payload)
```

### Manual Processing

```ruby
# For testing or manual processing
OrderService.handle_order_created(payload)
```

### Direct Transformation

```ruby
# Transform order data to Sovos format
result = OrderCalculator::TransformOrderToSovosFormat.call(
  order_data: order_data,
  sovos_gtd_setting: company.sovos_gtd_setting
)

if result.success?
  sovos_payload = result.sovos_payload
  # Use the transformed payload...
end
```

## Testing

Run the test suite to verify the integration:

```bash
# Run all order calculator tests
rspec spec/interactors/order_calculator/

# Run specific service tests
rspec spec/interactors/order_calculator/transform_order_to_sovos_format_spec.rb
rspec spec/services/order_service_spec.rb
```

## Configuration

Ensure the following environment variables are set:

```bash
SOVOS_API_URL=https://api.sovos.com
```

Company Sovos GTD settings must include:
- `username`
- `password`
- `hmac_key`
- `organization_identification_code`

## Monitoring

The system provides comprehensive logging:

- Order processing lifecycle events
- Sovos API request/response details
- Performance metrics and timing
- Error details and stack traces
- Cache hit/miss information

## Troubleshooting

### Common Issues

1. **Missing Sovos Settings**: Ensure the company has `sovos_gtd_setting` configured
2. **Invalid Payload**: Check webhook payload structure matches expected format
3. **API Authentication**: Verify Sovos credentials are correct
4. **Address Format**: Ensure shipping addresses include required fields

### Debug Mode

Enable detailed logging in development:

```ruby
# In development.rb or staging.rb
config.log_level = :debug
```

### Error Logs

Check Rails logs for detailed error information:

```bash
tail -f log/development.log | grep "OrderService\|SovosAPI"
```

## Future Enhancements

Potential improvements to consider:

1. **Response Transformation**: Transform Sovos responses back to Fluid format
2. **Webhook Retry Logic**: Implement retry mechanisms for failed webhooks
3. **Batch Processing**: Support for processing multiple orders simultaneously
4. **Real-time Updates**: WebSocket integration for live order status updates
5. **Advanced Caching**: Redis-based caching for better performance
