require "test_helper"

class OrderServiceTest < ActiveSupport::TestCase
  fixtures :companies

  def setup
    @company = companies(:acme)
    @payload = {
      "id" => "whe_1234567890abcdef",
      "identifier" => "whe_unique_identifier",
      "name" => "order_created",
      "payload" => {
        "event_name" => "order_created",
        "company_id" => @company.fluid_company_id,
        "resource_name" => "Commerce::Order",
        "resource" => "order",
        "event" => "created",
        "order" => {
          "subtotal" => 99.99,
          "tax" => 8.50,
          "discount" => 10.00,
          "shipping" => 5.99,
          "external_id" => "ext_order_123",
          "metadata" => {
            "source" => "web",
            "campaign" => "summer_sale"
          },
          "order_class" => "customer_order",
          "items" => [
            {
              "id" => 1,
              "product_id" => 100,
              "variant_id" => 200,
              "quantity" => 2,
              "price" => 49.99,
              "total" => 99.98,
              "sku" => "PROD-001",
              "title" => "Premium Widget",
              "variant_title" => "Blue - Large"
            }
          ],
          "ship_to" => {
            "id" => 301,
            "first_name" => "<PERSON>",
            "last_name" => "Doe",
            "address1" => "123 Main St",
            "city" => "New York",
            "state" => "NY",
            "zip" => "10001",
            "country" => "US"
          }
        }
      },
      "timestamp" => "2024-01-01T12:00:00Z"
    }
  end

  test "handles order created successfully" do
    assert_nothing_raised do
      OrderService.handle_order_created(@payload)
    end
  end

  test "raises error when company does not have sovos_gtd_setting" do
    # Remove the sovos_gtd_setting from the company
    @company.sovos_gtd_setting.destroy! if @company.sovos_gtd_setting.present?

    error = assert_raises StandardError do
      OrderService.handle_order_created(@payload)
    end

    assert_includes error.message, "does not have Sovos GTD settings configured"
    assert_includes error.message, @company.name
  end

  test "handles order created with missing company" do
    invalid_payload = @payload.dup
    invalid_payload["payload"]["company_id"] = 999999

    assert_nothing_raised do
      OrderService.handle_order_created(invalid_payload)
    end
  end

  test "handles order created with missing order data" do
    invalid_payload = @payload.dup
    invalid_payload["payload"].delete("order")

    assert_nothing_raised do
      OrderService.handle_order_created(invalid_payload)
    end
  end

  test "finds company by company_id in payload" do
    service = OrderService.new(@payload)
    assert_equal @company, service.send(:find_company)
  end

  test "finds company by fluid_company_id in fallback structure" do
    payload_with_fluid_id = {
      "company" => {
        "fluid_company_id" => @company.fluid_company_id
      },
      "order" => {}
    }
    service = OrderService.new(payload_with_fluid_id)
    assert_equal @company, service.send(:find_company)
  end
end 
