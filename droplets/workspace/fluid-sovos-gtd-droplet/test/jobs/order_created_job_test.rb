require "test_helper"

class OrderCreatedJobTest < ActiveJob::TestCase
  fixtures :companies

  def setup
    @company = companies(:acme)
    @payload = {
      "id" => "whe_1234567890abcdef",
      "identifier" => "whe_unique_identifier",
      "name" => "order_created",
      "payload" => {
        "event_name" => "order_created",
        "company_id" => @company.fluid_company_id,
        "resource_name" => "Commerce::Order",
        "resource" => "order",
        "event" => "created",
        "order" => {
          "subtotal" => 99.99,
          "tax" => 8.50,
          "discount" => 10.00,
          "shipping" => 5.99,
          "external_id" => "ext_order_123",
          "metadata" => {
            "source" => "web",
            "campaign" => "summer_sale"
          },
          "order_class" => "customer_order",
          "items" => [
            {
              "id" => 1,
              "product_id" => 100,
              "variant_id" => 200,
              "quantity" => 2,
              "price" => 49.99,
              "total" => 99.98,
              "sku" => "PROD-001",
              "title" => "Premium Widget",
              "variant_title" => "Blue - Large"
            }
          ],
          "ship_to" => {
            "id" => 301,
            "first_name" => "<PERSON>",
            "last_name" => "Doe",
            "address1" => "123 Main St",
            "city" => "New York",
            "state" => "NY",
            "zip" => "10001",
            "country" => "US"
          }
        }
      },
      "timestamp" => "2024-01-01T12:00:00Z"
    }
  end

  test "processes webhook successfully" do
    assert_nothing_raised do
      OrderCreatedJob.perform_now(@payload)
    end
  end

  test "validates required payload keys" do
    invalid_payload = { "id" => "whe_1234567890abcdef" }

    assert_raises ArgumentError do
      OrderCreatedJob.perform_now(invalid_payload)
    end
  end

  test "validates order key is present in payload" do
    invalid_payload = { 
      "payload" => {
        "event_name" => "order_created",
        "company_id" => @company.fluid_company_id
      }
    }

    assert_raises ArgumentError do
      OrderCreatedJob.perform_now(invalid_payload)
    end
  end

  test "validates company_id is present in payload" do
    invalid_payload = { 
      "payload" => {
        "event_name" => "order_created",
        "order" => {
          "external_id" => "ext_order_123",
          "subtotal" => 99.99
        }
      }
    }

    assert_raises ArgumentError do
      OrderCreatedJob.perform_now(invalid_payload)
    end
  end

  test "calls OrderService with payload" do
    OrderService.expects(:handle_order_created).with(@payload).once
    
    OrderCreatedJob.perform_now(@payload)
  end

  test "propagates error when company lacks sovos_gtd_setting" do
    # Remove the sovos_gtd_setting from the company
    @company.sovos_gtd_setting.destroy! if @company.sovos_gtd_setting.present?

    error = assert_raises StandardError do
      OrderCreatedJob.perform_now(@payload)
    end

    assert_includes error.message, "does not have Sovos GTD settings configured"
  end
end 
