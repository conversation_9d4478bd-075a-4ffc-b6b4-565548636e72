// app/frontend/entrypoints/sovos_gtd_settings.tsx
import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';

interface SovosGtdSetting {
  username: string;
  password: string;
  hmac_key: string;
  organization_identification_code: string;
}

interface FormData extends SovosGtdSetting {}

const SovosGtdSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'credentials' | 'callbacks'>('credentials');
  const [showPassword, setShowPassword] = useState(false);
  const [showHmacKey, setShowHmacKey] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  // Get initial data from Rails
  const rootElement = document.getElementById('sovos-gtd-settings-root');
  const initialData: SovosGtdSetting = rootElement?.dataset.initialData 
    ? JSON.parse(rootElement.dataset.initialData) 
    : {
        username: '',
        password: '',
        hmac_key: '',
        organization_identification_code: ''
      };
  
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') as string;
  const updateUrl = rootElement?.dataset.updateUrl as string;
  
  // Extract DRI from URL parameters or data attribute
  const urlParams = new URLSearchParams(window.location.search);
  const dri = urlParams.get('dri') || rootElement?.dataset.dri;
  
  const [formData, setFormData] = useState<FormData>(initialData);
  const [originalData, setOriginalData] = useState<FormData>(initialData);

  useEffect(() => {
    // Check if form is dirty by comparing current form data with original data
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
    setIsDirty(hasChanges);
  }, [formData, originalData]);

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Build the request body with DRI parameter
      const requestBody: any = { sovos_gtd_setting: formData };
      if (dri) {
        requestBody.dri = dri;
      }

      const response = await fetch(updateUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: data.message || 'Settings saved successfully!' });
        setOriginalData({ ...formData });
        setIsDirty(false);
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to save settings' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const EyeIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
    </svg>
  );

  const EyeOffIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
    </svg>
  );

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Sovos Global Tax Determination Settings</h1>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab('credentials')}
              className={`py-2 px-6 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'credentials'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Credentials
            </button>
            {/* <button
              onClick={() => setActiveTab('callbacks')}
              className={`py-2 px-6 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'callbacks'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Callbacks
            </button> */}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'credentials' ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">API Credentials</h2>
                
                {/* Username */}
                <div className="mb-4">
                  <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                    Username
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter username"
                  />
                </div>

                {/* Password */}
                <div className="mb-4">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                    >
                      {showPassword ? <EyeOffIcon /> : <EyeIcon />}
                    </button>
                  </div>
                </div>

                {/* HMAC Key */}
                <div className="mb-4">
                  <label htmlFor="hmac_key" className="block text-sm font-medium text-gray-700 mb-1">
                    HMAC Key
                  </label>
                  <div className="relative">
                    <input
                      type={showHmacKey ? 'text' : 'password'}
                      id="hmac_key"
                      name="hmac_key"
                      value={formData.hmac_key}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter HMAC key"
                    />
                    <button
                      type="button"
                      onClick={() => setShowHmacKey(!showHmacKey)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                    >
                      {showHmacKey ? <EyeOffIcon /> : <EyeIcon />}
                    </button>
                  </div>
                </div>

                {/* Organization Identification Code */}
                <div className="mb-6">
                  <label htmlFor="organization_identification_code" className="block text-sm font-medium text-gray-700 mb-1">
                    Organization Identification Code
                  </label>
                  <input
                    type="text"
                    id="organization_identification_code"
                    name="organization_identification_code"
                    value={formData.organization_identification_code}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter organization code"
                  />
                </div>

                {/* Submit button and message */}
                <div className="flex items-center justify-between">
                  <button
                    type="submit"
                    disabled={!isDirty || isSubmitting}
                    className={`px-4 py-2 rounded-md font-medium transition-colors ${
                      !isDirty || isSubmitting
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {isSubmitting ? 'Saving...' : 'Save Settings'}
                  </button>

                  {message && (
                    <div className={`text-sm font-medium ${
                      message.type === 'success' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {message.text}
                    </div>
                  )}
                </div>
              </div>
            </form>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-gray-500">Callback configuration coming soon...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Initialize React app
const rootElement = document.getElementById('sovos-gtd-settings-root');
if (rootElement) {
  const root = createRoot(rootElement);
  root.render(<SovosGtdSettings />);
}
