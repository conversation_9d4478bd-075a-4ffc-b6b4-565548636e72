# frozen_string_literal: true

# Cache module for Sovos responses
module SovosCache
  EXPIRATION_TIMES = {
    tax_calculation: 1.hour,
    geo_code: 24.hours,
    fluid_to_sovos_transform: 1.hour
  }.freeze

  class << self
    # Generate a cache key for Sovos data
    def generate_key(cart_data, type: :tax_calculation)
      cart = cart_data.with_indifferent_access
      
      # Build key components based on what affects tax calculation
      key_parts = [
        'sovos',
        type.to_s,
        cart[:id],
        cart[:updated_at]&.to_i,
        # Include key fields that affect tax calculation
        Digest::MD5.hexdigest(extract_tax_relevant_data(cart).to_json)
      ]
      
      cache_key = key_parts.compact.join(':')
      
      # Log cache key generation for debugging
      Rails.logger.debug("Generated cache key for type #{type}: #{cache_key}")
      
      cache_key
    end

    # Store data in cache
    def store(cart_data, response_data, type: :tax_calculation)
      cache_key = generate_key(cart_data, type: type)
      expires_in = EXPIRATION_TIMES[type] || 1.hour
      
      Rails.cache.write(cache_key, response_data, expires_in: expires_in)
      
      Rails.logger.debug("Stored data in cache with key: #{cache_key}")
      
      {
        key: cache_key,
        expires_at: Time.current + expires_in
      }
    end

    # Fetch data from cache
    def fetch(cart_data, type: :tax_calculation, &block)
      cache_key = generate_key(cart_data, type: type)
      
      cached_value = Rails.cache.read(cache_key)
      
      if cached_value.present?
        Rails.logger.debug("Cache hit for key: #{cache_key}")
        {
          cached: true,
          key: cache_key,
          payload: cached_value
        }
      elsif block_given?
        Rails.logger.debug("Cache miss for key: #{cache_key}, executing block")
        # Execute block if no cache hit
        result = yield
        # Only store non-nil results to avoid caching nil values
        if result.present?
          store(cart_data, result, type: type)
        end
        {
          cached: false,
          key: cache_key,
          payload: result
        }
      else
        Rails.logger.debug("Cache miss for key: #{cache_key}, no block provided")
        nil
      end
    end

    # Clear cache for specific cart
    def clear(cart_data)
      # Clear all cache types for this cart
      EXPIRATION_TIMES.keys.each do |type|
        cache_key = generate_key(cart_data, type: type)
        Rails.cache.delete(cache_key)
      end
    end

    private

    # Extract data that affects tax calculation
    def extract_tax_relevant_data(cart)
      {
        items: cart[:items]&.map { |item| 
          {
            sku: item[:sku],
            price: item[:price],
            quantity: item[:quantity]
          }
        },
        ship_to: cart[:ship_to],
        currency_code: cart[:currency_code],
        shipping_total: cart[:shipping_total_in_currency]
      }
    end
  end
end
