# app/lib/canadian_province_normalizer.rb
module CanadianProvinceNormalizer
  PROVINCE_NAME_TO_CODE = {
    "Alberta" => "AB",
    "British Columbia" => "BC",
    "Manitoba" => "MB",
    "New Brunswick" => "NB",
    "Newfoundland and Labrador" => "NL",
    "Northwest Territories" => "NT",
    "Nova Scotia" => "NS",
    "Nunavut" => "NU",
    "Ontario" => "ON",
    "Prince Edward Island" => "PE",
    "Quebec" => "QC",
    "Saskatchewan" => "SK",
    "Yukon" => "YT"
  }.freeze

  def self.to_code(name)
    return if name.blank?

    PROVINCE_NAME_TO_CODE[name.strip.titleize]
  end
end
