# app/lib/australian_state_normalizer.rb
module AustralianStateNormalizer
  STATE_NAME_TO_CODE = {
    "Australian Capital Territory" => "ACT",
    "New South Wales" => "NSW",
    "Northern Territory" => "NT",
    "Queensland" => "QLD",
    "South Australia" => "SA",
    "Tasmania" => "TAS",
    "Victoria" => "VIC",
    "Western Australia" => "WA"
  }.freeze

  def self.to_code(name)
    return if name.blank?

    STATE_NAME_TO_CODE[name.strip.titleize]
  end
end
