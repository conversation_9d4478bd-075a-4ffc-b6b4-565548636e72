# app/lib/state_normalizer.rb
module StateNormalizer
  def self.to_code(state, country_code = nil)
    return if state.blank?

    case country_code&.upcase
    when 'US'
      UsStateNormalizer.to_code(state) if defined?(UsStateNormalizer)
    when 'CA'
      CanadianProvinceNormalizer.to_code(state) if defined?(CanadianProvinceNormalizer)
    when 'AU'
      AustralianStateNormalizer.to_code(state) if defined?(AustralianStateNormalizer)
    else
      # Fallback to US normalizer if no country specified or unknown country
      if defined?(UsStateNormalizer)
        UsStateNormalizer.to_code(state)
      else
        state.to_s.strip.upcase
      end
    end || state.to_s.strip.upcase
  end
end
