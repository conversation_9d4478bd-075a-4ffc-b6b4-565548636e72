<div class="flex flex-col gap-2 justify-center items-center h-screen bg-slate-800">
  <div class="bg-white rounded-lg p-4 w-96">
    <h2 class="text-2xl font-bold text-slate-800 mb-4">Change your password</h2>
    <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>
      <%= f.hidden_field :reset_password_token %>

      <%= f.label :password, "New password", class: "block text-sm/6 font-medium text-slate-800" %>
      <% if @minimum_password_length %>
        <em>(<%= @minimum_password_length %> characters minimum)</em>
      <% end %>
      <%= f.password_field :password, autofocus: true, autocomplete: "new-password", class: "bg-slate-200 rounded-md p-2 w-full" %>

      <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm/6 font-medium text-slate-800" %>
      <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "bg-slate-200 rounded-md p-2 w-full" %>

      <div class="mt-4">
        <%= f.submit "Change", class: "rounded-md bg-slate-600 px-3 py-1 text-white shadow-sm hover:bg-slate-700" %>
      </div>
    <% end %>
  </div>
</div>


