<div class="flex flex-col gap-2 justify-center items-center h-screen bg-slate-800">
  <div class="bg-white rounded-lg p-4 w-96">
    <h2 class="text-2xl font-bold text-slate-800 mb-4">Send me reset instructions</h2>
    <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>
      <%= f.label :email, class: "block text-sm/6 font-medium text-slate-800" %>
      <%= f.email_field :email, autofocus: true, autocomplete: "email", class: "bg-slate-200 rounded-md p-2 w-full" %>

      <div class="mt-4">
        <%= f.submit "Reset", class: "rounded-md bg-slate-600 px-3 py-1 text-white shadow-sm hover:bg-slate-700" %>
      </div>
    <% end %>
  </div>
</div>
