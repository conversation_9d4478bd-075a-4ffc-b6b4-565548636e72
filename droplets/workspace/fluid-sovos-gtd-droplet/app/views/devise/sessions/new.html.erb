<div class="flex flex-col gap-2 justify-center items-center h-screen bg-slate-800">
  <div class="bg-white rounded-lg p-4 w-96">
    <h2 class="text-2xl font-bold text-slate-800 mb-4">Sign in</h2>
    <%= form_for(
          resource,
          as: resource_name,
          url: session_path(resource_name)
        ) do |f| %>
        <%= f.label :email, class: "block text-sm/6 font-medium text-slate-800" %>
        <%= f.email_field :email, autofocus: true, autocomplete: "email", class: "bg-slate-200 rounded-md p-2 w-full" %>

        <%= f.label :password, class: "block text-sm/6 font-medium text-slate-800 mt-2" %>
        <%= f.password_field :password, autocomplete: "current-password", class: "bg-slate-200 rounded-md p-2 w-full" %>

      <% if devise_mapping.rememberable? %>
        <div class="mt-2 flex items-center gap-1">
          <%= f.check_box :remember_me %>
          <%= f.label :remember_me, class: "text-sm/6 font-medium text-slate-800" %>
        </div>
      <% end %>

      <div class="mt-4">
        <%= f.submit "Sign In", class: "rounded-md bg-slate-600 px-3 py-1 text-white shadow-sm hover:bg-slate-700" %>
      </div>
    <% end %>
    <%= link_to "Forgot your password?",
          new_password_path(resource_name),
          class: "block mt-6 text-sm/6 font-medium text-slate-600 underline hover:text-blue-600"
    %>
  </div>
</div>
