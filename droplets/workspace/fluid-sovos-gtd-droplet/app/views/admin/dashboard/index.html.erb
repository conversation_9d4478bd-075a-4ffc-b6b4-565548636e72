<div class="flex items-center justify-between">
  <div class="flex items-center gap-2">
    <h1 class="font-custom text-4xl font-bold text-gray-600">Dashboard</h1>
  </div>
</div>

<% if Setting.droplet.values["uuid"] %>
  <%= button_to "Update Droplet", admin_droplet_path, method: :put, class: "inline-flex items-center text-sm bg-blue-600 hover:bg-orange-600 text-white px-4 py-2 rounded-md mr-2" %>
<% else %>
  <%= button_to "Create Droplet", admin_droplet_path, method: :post, class: "inline-flex items-center text-sm bg-blue-600 hover:bg-orange-600 text-white px-4 py-2 rounded-md mr-2" %>
<% end %>
