<header class="flex h-8 shrink-0 items-center px-8 transition-[width,height] ease-linear w-full">
  <div class="flex-1">
    <% if flash.any? %>
      <div class="flex flex-col gap-2 p-4">
        <% flash.each do |type, message| %>
          <span class="<%= type == 'notice' ? 'text-green-600' : 'text-red-600' %>">
            <%= message %>
          </span>
        <% end %>
      </div>
    <% end %>
  </div>
  <span class="text-sm text-gray-600 mr-4"><%= current_user.email %></span>
  <%= button_to "Sign out",
                destroy_user_session_path,
                method: :delete,
                class: "text-sm text-blue-600 hover:text-orange-600"
  %>
</header>
