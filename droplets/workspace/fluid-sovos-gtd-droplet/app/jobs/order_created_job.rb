class OrderCreatedJob < WebhookEventJob
  # payload - Hash received from the webhook controller.
  # Expected structure (example):
  # {
  #   "order" => {
  #     "id" => 6404252,
  #     "amount" => "87.56",
  #     "bill_to" => null,
  #     "created_at" => "2025-08-12T19:05:22Z",
  #     "currency_code" => "USD",
  #     "discount" => "0.0",
  #     "email" => "<EMAIL>",
  #     "external_id" => null,
  #     "first_name" => "John",
  #     "items" => [
  #       {
  #         "id" => 14628588,
  #         "cv" => 0,
  #         "display_price" => "$87.56 (USD)",
  #         "display_to_customer" => true,
  #         "display_total" => "$87.56 (USD)",
  #         "enrollment_pack_id" => null,
  #         "image_url" => "https://ik.imagekit.io/fluid/s3/************************************3AtE8_undefined.jpg",
  #         "price" => "87.56",
  #         "price_in_currency" => "$87.56",
  #         "product" => {
  #           "id" => 63,
  #           "cv" => 0,
  #           "image_path" => "************************************3AtE8_undefined.jpg",
  #           "image_url" => "https://ik.imagekit.io/fluid/s3/************************************3AtE8_undefined.jpg",
  #           "introduction" => "Amazing pre-built homes and cabins that can operate off the grid.",
  #           "price" => "87.56",
  #           "price_in_currency" => "$87.56",
  #           "sku" => "CTZHOME",
  #           "tax" => "0.0",
  #           "tax_in_currency" => "$0.00",
  #           "title" => "Citizen Cabin"
  #         },
  #         "quantity" => 1,
  #         "qv" => 0,
  #         "refundable_amount" => "87.56",
  #         "sku" => "CTZHOME",
  #         "tax" => "0.0",
  #         "tax_in_currency" => "$0.00",
  #         "title" => "Citizen Cabin",
  #         "total" => "87.56",
  #         "variant" => {
  #           "id" => 2698,
  #           "image_path" => null,
  #           "image_url" => null,
  #           "options" => [],
  #           "price" => "87.56",
  #           "price_in_currency" => "",
  #           "primary_image" => null,
  #           "sku" => "CTZHOME",
  #           "title" => ""
  #         }
  #       }
  #     ],
  #     "last_name" => "Smith",
  #     "metadata" => {},
  #     "note" => null,
  #     "order_class" => null,
  #     "order_number" => "1002",
  #     "order_type" => null,
  #     "phone" => null,
  #     "refundable_amount" => "87.56",
  #     "rep" => {
  #       "id" => null,
  #       "external_id" => null
  #     },
  #     "ship_to" => {
  #       "id" => 11665930,
  #       "address1" => "Test 123",
  #       "address2" => "",
  #       "address3" => null,
  #       "city" => "Sab Francisco",
  #       "country_code" => "US",
  #       "default" => false,
  #       "first_name" => "John",
  #       "last_name" => "Smith",
  #       "name" => "John Smith",
  #       "postal_code" => "94102",
  #       "state" => "California",
  #       "subdivision_code" => "CA"
  #     },
  #     "shipping" => "0.0",
  #     "status" => "awaiting_shipment",
  #     "subtotal" => "87.56",
  #     "tax" => "0.0",
  #     "token" => "fo_5t9lqltkqwm0slqbaw5esu",
  #     "type" => null,
  #     "updated_at" => "2025-08-12T19:05:22Z"
  #   },
  #   "event_name" => "order_created",
  #   "schema_version" => "1.0.0",
  #   "schema_hash" => "5f1d74894c1efa807e8a4d9a8e6703f5e82fa813016e300a0ad930d6e8f636fa",
  #   "company_id" => *********,
  #   "resource_name" => "Commerce::Order",
  #   "resource" => "order",
  #   "event" => "created",
  #   "controller" => "webhooks",
  #   "action" => "create",
  #   "webhook" => {
  #     "resource" => "order",
  #     "event" => "created"
  #   }
  # }
  def process_webhook
    # Validate required keys in payload
    validate_payload_keys("order")
    validate_payload_keys("company_id")
    validate_payload_keys("event_name")
    
    # Call the OrderService to handle the order creation
    OrderService.handle_order_created(get_payload)
  end
end
