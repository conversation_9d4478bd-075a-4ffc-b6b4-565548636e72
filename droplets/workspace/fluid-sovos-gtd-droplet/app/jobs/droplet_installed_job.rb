class DropletInstalledJob < WebhookEventJob
  # payload - Hash received from the webhook controller.
  # Expected structure (example):
  # {
  #   "company" => {
  #     "fluid_shop" => "example.myshopify.com",
  #     "name" => "Example Shop",
  #     "fluid_company_id" => 123,
  #     "company_droplet_uuid" => "uuid",
  #     "authentication_token" => "token",
  #     "webhook_verification_token" => "verify",
  #   }
  # }
  def process_webhook
    # Validate required keys in payload
    validate_payload_keys("company")
    company_attributes = get_payload.fetch("company", {})

    company = Company.find_by(fluid_shop: company_attributes["fluid_shop"]) || Company.new

    company.assign_attributes(company_attributes.slice(
      "fluid_shop",
      "name",
      "fluid_company_id",
      "authentication_token",
      "webhook_verification_token",
      "droplet_installation_uuid"
    ))
    company.company_droplet_uuid = company_attributes.fetch("droplet_uuid")
    company.active = true

    unless company.save
      Rails.logger.error(
        "[DropletInstalledJob] Failed to create company: #{company.errors.full_messages.join(', ')}"
      )
    else
      SovosGtdSetting.create!(company: company) unless company.sovos_gtd_setting.present?
      create_callbacks(company)
      create_webhooks(company)
    end
  end

  private

  def create_callbacks(company)
    client = FluidClient.new(company.authentication_token)

    # Test get definitions and registrations
    #client.callback_definitions.get
    #client.callback_registrations.get

    webhook_payload = {
      definition_name: "update_cart_tax", #<- choose the callback
      url: "#{ENV['SOVOS_DROPLET_HOST_URL']}/callbacks/update_cart_tax",
      timeout_in_seconds: 20,
      active: true
    }

    installed_callback_ids = []

    begin
      response = client.callback_registrations.create(webhook_payload)
      if response && response["callback_registration"]["uuid"]
        installed_callback_ids << response["callback_registration"]["uuid"]
      else
        Rails.logger.warn(
          "[DropletInstalledJob] Callback registered but no UUID returned for: #{callback.name}"
        )
      end

      if installed_callback_ids.any?
        company.update!(installed_callback_ids: installed_callback_ids)
      end
    rescue FluidClient::Error => e
      Rails.logger.error "Failed to create callback: #{e.message}"
    end
  end

  def create_webhooks(company)
    client = FluidClient.new(company.authentication_token)

    # Configuration for webhooks - easy to add new ones
    webhook_configs = [
      {
        resource: "order",
        event: "created"
      },
      {
        resource: "order",
        event: "cancelled"
      },
      {
        resource: "order",
        event: "refunded"
      }
    ]

    installed_webhook_ids = []

    webhook_configs.each do |config|
      webhook_payload = {
        resource: config[:resource],
        url: "#{ENV['SOVOS_DROPLET_HOST_URL']}/webhook",
        active: true,
        auth_token: Setting.fluid_webhook.auth_token,
        event: config[:event],
        http_method: "post"
      }

      begin
        response = client.webhooks.create(webhook_payload)
        if response && response["webhook"]["id"]
          installed_webhook_ids << response["webhook"]["id"]
          Rails.logger.info(
            "[DropletInstalledJob] Successfully created webhook for #{config[:resource]}:#{config[:event]}"
          )
        else
          Rails.logger.warn(
            "[DropletInstalledJob] Webhook registered but no UUID returned for: #{config[:resource]}:#{config[:event]}"
          )
        end
      rescue FluidClient::Error => e
        Rails.logger.error "Failed to create webhook for #{config[:resource]}:#{config[:event]}: #{e.message}"
      end
    end

    if installed_webhook_ids.any?
      company.update!(installed_webhook_ids: installed_webhook_ids)
    end
  end
end
