# frozen_string_literal: true

require "json_schemer"

module OrderCalculator
  # ValidateWebhookRequest validates the incoming webhook request from Fluid
  # and extracts the cart data for further processing.
  #
  # Schema-Driven Validation:
  # When context.request_payload_schema is provided, the interactor will use JSON Schema
  # validation instead of hardcoded field validation. This allows for dynamic validation
  # based on different callback types or client requirements.
  #
  # Fallback Validation:
  # If no schema is provided or schema validation fails, the interactor falls back to
  # hardcoded validation rules:
  # - Checks that the payload contains a cart object
  # - Validates that the cart has required fields (id, currency_code, items)
  # - Validates that items have required fields (id, quantity)
  # - Validates ship_to address if present
  #
  # Input:
  #   - context.request: The HTTP request object
  #   - context.request_payload_schema: Optional JSON Schema for dynamic validation
  #
  # Output:
  #   - context.cart_data: The normalized cart data
  #   - context.validation_method: 'schema' or 'hardcoded' indicating which method was used
  #
  # On failure, it sets context.error with a descriptive error message.
  class ValidateWebhookRequest
    include Interactor

    REQUIRED_CART_FIELDS = [:id, :currency_code].freeze
    REQUIRED_ITEM_FIELDS = [:id, :quantity].freeze
    REQUIRED_ADDRESS_FIELDS = [:city, :state, :postal_code, :country_code].freeze

    def call
      # Extract the payload from the webhook request
      payload = extract_payload
      validate_payload_fields(payload)
    end

    private

    # Validate payload
    # @param payload [Hash] The extracted payload
    def validate_payload_fields(payload)
      # Validate that the payload contains a cart
      unless payload.key?(:cart) || payload.key?("cart")
        context.fail!(error: "Missing cart data in webhook payload")
        return
      end

      # Extract and normalize the cart data
      cart = payload[:cart] || payload["cart"]
      cart = cart.deep_symbolize_keys

      # Validate the cart data
      validate_cart(cart)
      return if context.failure?

      # Store the normalized cart data in the context
      context.cart_data = cart
    end

    # Extract cart data from payload (handles different structures)
    # @param payload [Hash] The payload
    # @return [Hash] The cart data
    def extract_cart_from_payload(payload)
      # Handle different payload structures
      if payload.key?(:cart) || payload.key?("cart")
        payload[:cart] || payload["cart"]
      elsif payload.key?(:payload) && (payload[:payload].key?(:cart) || payload[:payload].key?("cart"))
        payload[:payload][:cart] || payload[:payload]["cart"]
      else
        # Try to find cart in nested structure
        find_cart_in_hash(payload) || {}
      end
    end

    def extract_payload
      # Get the raw request body first
      raw_body = context.request.raw_post
      
      # Try to parse as JSON if it's a JSON request
      if context.request.content_type&.include?('application/json') && raw_body.present?
        begin
          payload = JSON.parse(raw_body)
          Rails.logger.info("Parsed JSON payload from raw body")
          return payload.deep_symbolize_keys
        rescue JSON::ParserError => e
          Rails.logger.warn("Failed to parse JSON from raw body: #{e.message}")
        end
      end
      
      # Fall back to params
      raw_params = context.request.params
      raw_params = raw_params.to_unsafe_h if raw_params.respond_to?(:to_unsafe_h)
      params = raw_params.deep_symbolize_keys

      # Log the raw payload for debugging
      Rails.logger.info("Raw webhook params: #{params.inspect}")

      # The real Fluid webhook structure has cart at the root level
      # Check for cart at root level first
      if params[:cart].present?
        Rails.logger.info("Found cart at root level")
        return params
      end
      
      # Check for nested payload structure (for backward compatibility)
      if params[:payload].present? && params[:payload][:cart].present?
        Rails.logger.info("Using nested payload structure")
        return params[:payload]
      end
      
      # Check for webhook key (as shown in the real payload)
      if params[:webhook].present? && params[:webhook][:cart].present?
        Rails.logger.info("Found cart under webhook key")
        return params[:webhook]
      end
      
      # Try to find cart in any nested structure
      Rails.logger.info("Searching for cart in nested structure")
      cart = find_cart_in_hash(params)
      return { cart: cart } if cart

      # If we can't find a cart, return the original params
      Rails.logger.warn("Could not find cart in payload, using raw params")
      params
    end

    # Recursively search for a cart in a nested hash
    def find_cart_in_hash(hash)
      return nil unless hash.is_a?(Hash)

      # If this hash has a cart key, return its value
      return hash[:cart] if hash[:cart].present?
      return hash["cart"] if hash["cart"].present?

      # Otherwise, search in all nested hashes
      hash.each_value do |value|
        if value.is_a?(Hash)
          result = find_cart_in_hash(value)
          return result if result.present?
        end
      end

      nil
    end

    def validate_cart(cart)
      # Validate required cart fields
      missing_fields = REQUIRED_CART_FIELDS.select { |field| cart[field].nil? }
      unless missing_fields.empty?
        context.fail!(error: "Missing required cart fields: #{missing_fields.join(', ')}")
        return
      end

      # Handle different item field names (items vs lines)
      items = cart[:items] || cart[:lines] || []

      # Validate that items is an array
      unless items.is_a?(Array)
        context.fail!(error: "Cart items must be an array")
        return
      end

      # Normalize items to our expected format
      normalized_items = normalize_items(items)

      # Replace the original items with normalized ones
      cart[:items] = normalized_items

      # Validate items
      validate_items(normalized_items)
      return if context.failure?

      # Handle different shipping address structures
      ship_to = extract_shipping_address(cart)

      # If we found a shipping address, validate it
      if ship_to.present?
        # Store the normalized shipping address
        cart[:ship_to] = ship_to

        validate_address(ship_to)
        return if context.failure?
      end
    end

    def validate_items(items)
      # Items should already be an array at this point
      # If there are no items, that's fine (empty cart)
      return if items.empty?

      items.each_with_index do |item, index|
        missing_fields = REQUIRED_ITEM_FIELDS.select { |field| item[field].nil? }
        unless missing_fields.empty?
          context.fail!(error: "Item at index #{index} is missing required fields: #{missing_fields.join(', ')}")
          return
        end

        # Allow zero quantity items (they'll be filtered out later)
        unless item[:quantity].to_i >= 0
          context.fail!(error: "Item at index #{index} has invalid quantity: #{item[:quantity]}")
          return
        end
      end
    end

    def validate_address(address)
      missing_fields = REQUIRED_ADDRESS_FIELDS.select { |field| address[field].nil? }
      if missing_fields.any?
        context.fail!(error: "Shipping address is missing required fields: #{missing_fields.join(', ')}")
      end
    end

    # Normalize items from Fluid format to our expected format
    def normalize_items(items)
      # Return empty array if items is nil
      return [] if items.nil?

      # Items should already be an array at this point
      items.map do |item|
        item = item.symbolize_keys if item.respond_to?(:symbolize_keys)

        # Handle different field names
        normalized = {
          id: item[:id],
          quantity: item[:quantity] || 0,
          price: item[:price] || item[:amount] || item.dig(:prices, :price),
          sku: item.dig(:product, :sku),
          product_title: item[:product_title] || item.dig(:product, :title),
          variant_id: item[:variant_id] || item.dig(:variant, :id),
        }

        # Add product and variant info if present
        if item[:product].present?
          normalized[:product] = item[:product]
        end
        
        if item[:variant].present?
          normalized[:variant] = item[:variant]
        end

        normalized
      end
    end

    # Extract shipping address from different possible structures
    def extract_shipping_address(cart)
      # Log the cart for debugging
      Rails.logger.info("[DEBUG] Extracting shipping address from cart")

      # Direct ship_to structure (as shown in the real payload)
      if cart[:ship_to].present?
        Rails.logger.info("[DEBUG] Found direct ship_to structure")
        address = cart[:ship_to].dup
        
        # Normalize the address fields
        # The real payload uses 'state' but we might need 'state' to map to state code
        normalized_address = {
          city: address[:city],
          state: address[:state],
          postal_code: address[:postal_code],
          country_code: address[:country_code] || address[:country] || 'US',
          address1: address[:address1],
          first_name: address[:first_name],
          last_name: address[:last_name],
          name: address[:name]
        }
        
        return normalized_address
      end

      # Nested in addresses structure (Fluid standard)
      if cart.dig(:addresses, :ship_to).present?
        Rails.logger.info("[DEBUG] Found nested addresses.ship_to structure")
        return cart.dig(:addresses, :ship_to)
      end

      # Try to build from country_code if available
      if cart[:country_code].present?
        Rails.logger.info("[DEBUG] Building from country_code: #{cart[:country_code]}")
        return {
          country_code: cart[:country_code],
          # Other fields will be missing and fail validation if required
        }
      end

      # No shipping address found
      Rails.logger.info("[DEBUG] No shipping address found in cart")
      nil
    end
  end
end
