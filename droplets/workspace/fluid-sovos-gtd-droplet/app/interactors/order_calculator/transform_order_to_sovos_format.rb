module OrderCalculator
  class TransformOrderToSovosFormat
    include Interactor
    include ActiveSupport::Notifications

    def call
      @sovos_gtd_setting = context.sovos_gtd_setting
      @order_data = context.order_data
      @is_refund_or_cancellation = context.is_refund_or_cancellation || false

      unless @sovos_gtd_setting.present?
        context.fail!(error: "Missing sovos_gtd_setting")
        return
      end

      unless @order_data.present?
        context.fail!(error: "Missing order_data")
        return
      end

      begin
        # Start timing the transformation
        start_time = Time.current

        # Perform the transformation
        transformed_payload = transform_order_to_sovos_format(@order_data)

        # Store the transformed payload in context
        context.sovos_payload = transformed_payload

        # Log performance metrics
        duration = (Time.current - start_time) * 1000 # in milliseconds
        Rails.logger.debug("Transformed order to Sovos format in #{duration.round(2)}ms")
      rescue StandardError => e
        context.fail!(error: "Error transforming order to Sovos format: #{e.message}")
      end
    end

    private

    # Main transformation method that converts order data to Sovos format
    def transform_order_to_sovos_format(order_data)
      # Normalize order data with indifferent access for consistent key access
      order = order_data.with_indifferent_access

      # Build the base payload with required fields
      payload = {}

      # Map basic fields and set defaults
      payload = map_basic_fields(order, payload)

      # Add order configuration
      payload = add_order_configuration(order, payload)

      # Transform line items
      payload[:lines] = transform_line_items(order)

      # Return the complete payload
      payload
    end

    # Map basic fields from order to payload
    def map_basic_fields(order, payload)
      # Set required Sovos API fields
      payload[:usrname] = @sovos_gtd_setting.username
      payload[:pswrd] = @sovos_gtd_setting.password
      payload[:rsltLvl] = 1  # Most granular result level
      payload[:isAudit] = true
      payload[:tdcReqrd] = true  # Tax Decision Codes required
      payload[:tdmRequired] = false  # Transaction Document Messages
      
      # Currency and document info
      payload[:currn] = (order[:currency_code] || "USD").upcase
      
      if @is_refund_or_cancellation
        # For refunds/cancellations, create a new unique document number
        # and link to the original order
        original_order_number = order[:order_number] || order[:id]
        payload[:trnDocNum] = "Cancel-#{original_order_number}-#{Time.current.to_i}"
        payload[:origDocNum] = original_order_number
      else
        # For regular sales, use the original order number
        payload[:trnDocNum] = order[:order_number] || order[:id]
      end
      
      payload
    end

    # Add order configuration details
    def add_order_configuration(order, payload)
      # Tax calculation type - 1 = Standard forward calculation
      payload[:txCalcTp] = 1

      # Add shipping amount at header level if present
      if order[:shipping].present?
        shipping_amount = order[:shipping].to_f
        payload[:dlvrAmt] = shipping_amount if shipping_amount > 0
      end

      payload
    end

    # Transform all line items
    def transform_line_items(order)
      lines = []

      # Add product line items only
      items = order[:items] || []
      items.each_with_index do |item, index|
        lines << transform_line_item(item, index + 1, order)
      end

      lines
    end

    # Transform a single line item to Sovos format
    def transform_line_item(item, line_number, order)
      item = item.with_indifferent_access

      line = {
        orgCd: @sovos_gtd_setting.organization_identification_code,
        # Line item identification
        lnItmId: item[:id] || "line-#{line_number}",

        # Financial amounts
        grossAmt: (item[:price].to_f * item[:quantity].to_i),

        # Product information
        goodSrvCd: item[:sku] || item[:variant_id].to_s,
        goodSrvDesc: item[:title] || item[:name] || "Product #{item[:sku]}",

        # Quantity
        qnty: item[:quantity].to_f,

        # Transaction type - 14 for Return A/R (refund/cancellation), 1 for Sale
        trnTp: @is_refund_or_cancellation ? 14 : 1,

        # Customer information
        custVendCd: order[:email],
        custVendName: "#{order[:first_name]} #{order[:last_name]}"
      }

      # Add debit/credit indicator for refunds/cancellations
      if @is_refund_or_cancellation
        line[:debCredIndr] = 2  # 2 = Credit
      end

      # Add addresses
      line.merge!(format_ship_to_address(order[:ship_to])) if order[:ship_to].present?

      line
    end

    # Format ship to address according to Sovos API requirements
    def format_ship_to_address(address)
      return {} if address.blank?

      address = address.with_indifferent_access

      {
        # Ship To address fields with proper Sovos naming
        sTCity: address[:city],
        sTStateProv: normalize_state(address[:state], address[:country_code]),
        sTCountry: address[:country_code],
        sTPstlCd: address[:postal_code],
        sTStNameNum: address[:address1],

        # Also include Bill To as same as Ship To (common for e-commerce)
        bTCity: address[:city],
        bTStateProv: normalize_state(address[:state], address[:country_code]),
        bTCountry: address[:country_code],
        bTPstlCd: address[:postal_code],
        bTStNameNum: address[:address1]
      }.compact
    end

    # Normalize state to 2-letter code if needed
    def normalize_state(state, country_code = nil)
      return nil if state.blank?

      # Use the unified state normalizer if available
      if defined?(StateNormalizer)
        StateNormalizer.to_code(state, country_code)
      else
        # Fallback to US normalizer if available
        if defined?(UsStateNormalizer)
          UsStateNormalizer.to_code(state) || state.to_s.strip.upcase
        else
          # Simple normalization - assume it's already a code
          state.to_s.strip.upcase
        end
      end
    end

    # Instrument a block of code for performance monitoring
    def instrument(name, payload = {})
      ActiveSupport::Notifications.instrument("#{name}.order_calculator", payload)
    end
  end
end
