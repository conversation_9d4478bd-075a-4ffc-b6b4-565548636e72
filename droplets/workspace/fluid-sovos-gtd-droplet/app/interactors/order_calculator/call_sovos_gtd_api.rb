# frozen_string_literal: true

module OrderCalculator
  # CallSovosGtdApi interactor is responsible for communicating with the Sovos GTD API
  # to calculate order taxes.

  class CallSovosGtdApi
    include Interactor
    include ActiveSupport::Notifications

    # Critical fields that must be present in the payload
    CRITICAL_FIELDS = %w[usrname pswrd lines].freeze

    # Event names for instrumentation
    EVENTS = {
      validate_payload: "sovos.validate_payload",
      api_call: "sovos.api_call",
      validate_response: "sovos.validate_response",
    }.freeze

    # Maximum number of retry attempts for network errors
    MAX_RETRY_ATTEMPTS = 1

    # Sovos API endpoint for tax calculation
    TAX_CALCULATION_ENDPOINT = "/calcTax/doc"

    before :validate_critical_fields

    def call
      # Return early if validation failed in before callback
      return if context.failure?

      context.from_cache = false

      # Check if we have a cached response (unless skip_cache_check is true)
      if !context.skip_cache_check && check_cache
        # We found a cached response, no need to call the API
        return
      end

      make_api_call
    end

    private

    def validate_critical_fields
      unless context.sovos_payload.is_a?(Hash)
        context.fail!(error: "Invalid input: sovos_payload must be a Hash")
        return
      end

      if context.sovos_gtd_setting.present?
        Rails.logger.info("CallSovosGtdApi: sovos_gtd_setting found")
      else
        Rails.logger.warn("CallSovosGtdApi: sovos_gtd_setting missing from context")
      end

      unless context.sovos_gtd_setting.present?
        context.fail!(error: "Missing sovos_gtd_setting")
        return
      end

      normalized_payload = context.sovos_payload.with_indifferent_access
      missing_fields = CRITICAL_FIELDS.select { |field| normalized_payload[field].nil? }

      return if missing_fields.empty?
      context.fail!(error: "Missing critical fields for API call: #{missing_fields.join(', ')}")
    end

    def make_api_call(attempts = 0)
      client = create_client

      identifier = get_identifier

      begin
        log_api_request(identifier)
        response = execute_api_call(client, identifier)

        validate_and_store_response(response)
      rescue SovosApi::Errors::TimeoutError, SovosApi::Errors::ConnectionError => e
        # Network errors - retry with exponential backoff
        attempts += 1
        if attempts <= MAX_RETRY_ATTEMPTS
          Rails.logger.tagged("SovosAPI", "ID=#{identifier}") do
            Rails.logger.warn("Network error, retrying (attempt #{attempts}/#{MAX_RETRY_ATTEMPTS + 1}): #{e.message}")
          end
          sleep(0.5) # Small delay before retry
          make_api_call(attempts) # Recursive call with attempts counter
        else
          handle_error(e, identifier)
        end
      rescue SovosApi::Errors::Error => e
        # Handle all other SovosApi errors (including ServerError, ValidationError, etc.)
        # These are typically API-level errors that shouldn't be retried
        handle_error(e, identifier)
      rescue StandardError => e
        # Handle any other unexpected errors
        handle_error(e, identifier)
      end
    end

    def log_api_request(identifier)
      Rails.logger.tagged("SovosAPI", "ID=#{identifier}") do
        calc_type = context.calculation_type || "quote"
        Rails.logger.info("Calling Sovos GTD API endpoint: #{TAX_CALCULATION_ENDPOINT} with calculation_type: #{calc_type}")

        if Rails.env.development? || Rails.env.staging?
          sanitized_payload = sanitize_payload_for_logging(context.sovos_payload)
          Rails.logger.info("SOVOS REQUEST PAYLOAD: #{sanitized_payload.to_json}")
          Rails.logger.debug("Request payload: #{sanitized_payload.to_json}")

          Rails.logger.tagged("API_REQUEST") do
            Rails.logger.info("Sovos API request to #{TAX_CALCULATION_ENDPOINT}: #{sanitized_payload.to_json[0..200]}...")
          end
        end
      end
    end

    def execute_api_call(client, identifier)
      response = instrument(EVENTS[:api_call], identifier: identifier) do
        client.post(TAX_CALCULATION_ENDPOINT, context.sovos_payload)
      end

      Rails.logger.tagged("SovosAPI", "ID=#{identifier}") do
        Rails.logger.info("Sovos API response received successfully")

        # Log detailed response in non-production environments
        if Rails.env.development? || Rails.env.staging?
          Rails.logger.debug("Response: #{response.to_json}")
        end
      end

      response
    end

    def create_client
      config = build_sovos_config(context.sovos_gtd_setting)

      Rails.logger.info("[SovosApi] Using configuration for organization: #{context.sovos_gtd_setting.organization_identification_code}")
      SovosApi::Client.new(config)
    end

    def build_sovos_config(gtd_setting)
      {
        base_url: ENV['SOVOS_API_URL'],
        hmac_key: gtd_setting.hmac_key,
        timeout: 30, # 30 seconds timeout
        open_timeout: 10, # 10 seconds connection timeout
        username: gtd_setting.username,
        password: gtd_setting.password
      }
    end

    def sanitize_payload_for_logging(payload)
      sanitized = payload.deep_dup

      # Remove or mask sensitive fields
      sanitized["pswrd"] = "****" if sanitized["pswrd"]
      
      # Mask tax IDs if present
      if sanitized["lines"].is_a?(Array)
        sanitized["lines"].each do |line|
          line["sTTaxID"] = "****" if line["sTTaxID"]
          line["bTTaxID"] = "****" if line["bTTaxID"]
        end
      end

      sanitized
    end

    def validate_and_store_response(response)
      identifier = get_identifier
    
      response_result = instrument(EVENTS[:validate_response], calculation_type: context.calculation_type, identifier: identifier) do
        SovosApi::ResponseValidator.call(
          response:,
          calculation_type: context.calculation_type
        )
      end
    
      # Ensure the response validation was successful, fail context if not
      unless ensure_success(response_result, "validate response")
        # Context has already been failed by ensure_success, just return
        return
      end
    
      context.sovos_response = response_result.payload
    
      Rails.logger.info("[DEBUG] Full Sovos response: #{response_result.payload.inspect}")
      
      total_tax = response_result.payload[:txAmt] || "0"
      line_results = response_result.payload[:lnRslts] || []
      
      Rails.logger.info("[DEBUG] Total tax amount: #{total_tax}")
      Rails.logger.info("[DEBUG] Number of line results: #{line_results.length}")
    
      # Only cache successful responses if we have data and caching is enabled
      if has_cacheable_data? && caching_enabled?
        cache_response(response_result.payload)
      end
    end

    def cache_response(response_data)
      identifier = get_identifier

      # Cache successful API response for future use
      cache_result = SovosCache.store(
        get_cache_data,
        response_data,
        type: :tax_calculation
      )

      Rails.logger.tagged("SovosAPI", "ID=#{identifier}") do
        Rails.logger.info("Cached Sovos API response with key: #{cache_result[:key]}")
      end

      # Emit metrics for monitoring
      instrument("sovos_cache.store", {
        identifier: identifier,
        cache_key: cache_result[:key],
        expires_at: cache_result[:expires_at],
      })
    end

    def check_cache
      return false unless caching_enabled?

      return false unless has_cacheable_data?

      identifier = get_identifier

      cache_key = SovosCache.generate_key(get_cache_data, type: :tax_calculation)
      cached_value = Rails.cache.read(cache_key)

      if cached_value.present?
        context.sovos_response = cached_value
        context.from_cache = true

        Rails.logger.tagged("SovosAPI", "ID=#{identifier}") do
          Rails.logger.info("Using cached Sovos API response with key: #{cache_key}")
        end

        # Emit metrics for monitoring
        instrument("sovos_cache.hit", {
          identifier: identifier,
          cache_key: cache_key,
        })

        return true
      end

      context.from_cache = false
      false
    end

    def caching_enabled?
      return true unless defined?(SovosApi.configuration)

      !SovosApi.configuration.respond_to?(:cache_enabled) ||
        SovosApi.configuration.cache_enabled
    end

    # Get identifier for logging (cart_id or order_number)
    def get_identifier
      if context.cart_data.present?
        context.cart_data.with_indifferent_access[:id]
      elsif context.order_data.present?
        context.order_data.with_indifferent_access[:order_number] || context.order_data.with_indifferent_access[:id]
      else
        "unknown"
      end
    end

    # Check if we have data that can be cached
    def has_cacheable_data?
      (context.respond_to?(:cart_data) && context.cart_data.is_a?(Hash)) ||
      (context.respond_to?(:order_data) && context.order_data.is_a?(Hash))
    end

    # Get data for caching (cart_data or order_data)
    def get_cache_data
      context.cart_data || context.order_data
    end

    # Ensure a result is successful, fail the context if not
    def ensure_success(result, operation)
      return true if result.success?
    
      # Get identifier for logging
      identifier = get_identifier
    
      Rails.logger.tagged("SovosAPI", identifier ? "ID=#{identifier}" : nil) do
        Rails.logger.error("Failed to #{operation}: #{result.error}")
      end
    
      context.fail!(error: "Failed to #{operation}: #{result.error}")
      false
    end

    def handle_error(error, identifier = nil)
      case error
      when SovosApi::Errors::ValidationError
        log_and_fail("Validation error", error, identifier)
      when SovosApi::Errors::AuthenticationError
        log_and_fail("Authentication error", error, identifier)
      when SovosApi::Errors::ResourceNotFoundError
        log_and_fail("Resource not found", error, identifier)
      when SovosApi::Errors::TimeoutError, SovosApi::Errors::ConnectionError
        log_and_fail("Connection error", error, identifier)
      when SovosApi::Errors::ServerError
        log_and_fail("Sovos server error", error, identifier, include_response: true)
      when SovosApi::Errors::Error
        log_and_fail("Sovos API error", error, identifier, include_response: true)
      else
        log_and_fail("Unexpected error", error, identifier, include_backtrace: true)
      end
    end

    def log_and_fail(error_type, error, identifier = nil, include_backtrace: false, include_response: false)
      message = "#{error_type}: #{error.message}"

      Rails.logger.tagged("SovosAPI", identifier ? "ID=#{identifier}" : nil) do
        if include_backtrace
          Rails.logger.error("#{message}\n#{error.backtrace.join("\n")}")
        elsif include_response && error.respond_to?(:response)
          Rails.logger.error("#{message}\nResponse: #{error.response.to_json}")
        else
          Rails.logger.error(message)
        end
      end

      context.fail!(error: message)
    end

    # Instrument a block of code for performance monitoring
    def instrument(name, payload = {}, &block)
      ActiveSupport::Notifications.instrument("#{name}.order_calculator", payload, &block)
    end
  end
end
