module OrderCalculator
  class TransformToSovosFormat
    include Interactor
    include ActiveSupport::Notifications

    def call
      context.sovos_gtd_setting = Thread.current[:current_sovos_integration_setting]
      @sovos_gtd_setting = context.sovos_gtd_setting

      if context.sovos_gtd_setting.present?
        Rails.logger.info("Sovos GTD setting found")
      else
        Rails.logger.warn("Sovos GTD setting not found in thread storage")
      end

      # Check if we have a cached transformation
      if check_transformation_cache
        # We found a cached transformation, no need to transform again
        Rails.logger.info("Using cached transformation, sovos_gtd_setting set: #{context.sovos_gtd_setting.present?}")
        return
      end

      # No cache hit, perform the transformation
      begin
        # Start timing the transformation
        start_time = Time.current

        # Perform the transformation
        transformed_payload = transform_cart_to_sovos_format(context.cart_data)

        # Store the transformed payload in context
        context.sovos_payload = transformed_payload
        context.transform_from_cache = false

        # Cache the transformation for future requests
        cache_transformation(transformed_payload)

        # Log performance metrics
        duration = (Time.current - start_time) * 1000 # in milliseconds
        Rails.logger.debug("Transformed cart to Sovos format in #{duration.round(2)}ms")
      rescue StandardError => e
        context.fail!(error: "Error transforming to Sovos format: #{e.message}")
      end
    end

    private

    # Check if we have a cached transformation for this cart
    def check_transformation_cache
      # Skip cache check if caching is disabled
      return false unless caching_enabled?

      # Skip cache if we're forced to recalculate
      return false if context.skip_cache_check

      # Generate cache key for this transformation
      cache_key = SovosCache.generate_key(context.cart_data, type: :fluid_to_sovos_transform)

      # Check if we have a cached transformation
      cached_transform = Rails.cache.read(cache_key)

      if cached_transform.present?
        # Use the cached transformation
        context.sovos_payload = cached_transform
        context.transform_from_cache = true

        # Log cache hit
        Rails.logger.info("Using cached Fluid to Sovos transformation for cart #{context.cart_data[:id]}")

        # Emit metrics for monitoring
        instrument("sovos_cache.transform_hit", {
          cart_id: context.cart_data[:id],
          cache_key:,
        })

        return true
      end

      false
    end

    # Cache the transformation for future requests
    def cache_transformation(transformed_payload)
      # Skip if caching is disabled
      return unless caching_enabled?

      # Generate cache key
      cache_key = SovosCache.generate_key(context.cart_data, type: :fluid_to_sovos_transform)

      # Store in cache with configured TTL
      expiration = SovosCache::EXPIRATION_TIMES[:fluid_to_sovos_transform] || 1.hour
      Rails.cache.write(cache_key, transformed_payload, expires_in: expiration)

      # Log cache operation
      Rails.logger.info("Cached Fluid to Sovos transformation for cart #{context.cart_data[:id]}")

      # Emit metrics for monitoring
      instrument("sovos_cache.transform_store", {
        cart_id: context.cart_data[:id],
        cache_key:,
        expires_at: Time.now + expiration,
      })
    end

    # Check if caching is enabled
    def caching_enabled?
      # Default to true if configuration is not available
      return true unless defined?(SovosApi.configuration)

      !SovosApi.configuration.respond_to?(:cache_enabled) ||
        SovosApi.configuration.cache_enabled
    end

    # Instrument a block of code for performance monitoring
    def instrument(name, payload = {})
      ActiveSupport::Notifications.instrument("#{name}.order_calculator", payload)
    end

    # Main transformation method that converts cart data to Sovos format
    def transform_cart_to_sovos_format(cart_data)
      # Normalize cart data with indifferent access for consistent key access
      cart = cart_data.with_indifferent_access

      # Build the base payload with required fields
      payload = {}

      # Map basic fields and set defaults
      payload = map_basic_fields(cart, payload)

      # Add order configuration
      payload = add_order_configuration(cart, payload)

      # Transform line items
      payload[:lines] = transform_line_items(cart)

      # Return the complete payload
      payload
    end

    # Map basic fields from cart to payload
    def map_basic_fields(cart, payload)
      # Set required Sovos API fields
      payload[:usrname] = @sovos_gtd_setting.username
      payload[:pswrd] = @sovos_gtd_setting.password
      payload[:rsltLvl] = 1  # Most granular result level
      payload[:isAudit] = false
      payload[:tdcReqrd] = true  # Tax Decision Codes required
      payload[:tdmRequired] = false  # Transaction Document Messages
      
      # Currency and document info
      payload[:currn] = (cart[:currency_code] || "USD").upcase
      #payload[:docDt] = Time.current.strftime("%Y-%m-%d")
      payload[:trnDocNum] = cart[:id]
      
      # Transaction source identifier
      #payload[:trnSrc] = "fluid-commerce"
      
      payload
    end

    # Add order configuration details
    def add_order_configuration(cart, payload)
      # Tax calculation type - 1 = Standard forward calculation
      payload[:txCalcTp] = 1

      # Add delivery/shipping amount at header level if present
      # Use shipping_total_in_currency which comes as "$X.XX" format
      if cart[:shipping_total_in_currency].present?
        # Remove currency symbol and convert to float
        shipping_amount = cart[:shipping_total_in_currency].gsub(/[^\d.-]/, '').to_f
        payload[:dlvrAmt] = shipping_amount if shipping_amount > 0
      end

      # Could add more configuration based on cart type if needed
      # For example, if cart[:type] == "wholesale", might use different calculation type

      payload
    end

    # Transform all line items
    def transform_line_items(cart)
      lines = []

      # Add product line items only
      items = cart[:items] || []
      items.each_with_index do |item, index|
        lines << transform_line_item(item, index + 1, cart)
      end

      lines
    end

    # Transform a single line item to Sovos format
    def transform_line_item(item, line_number, cart)
      item = item.with_indifferent_access

      line = {
        orgCd: @sovos_gtd_setting.organization_identification_code,
        # Line item identification
        lnItmId: item[:id] || "line-#{line_number}",

        # Financial amounts
        grossAmt: (item[:price].to_f * item[:quantity].to_i),

        # Product information
        goodSrvCd: item[:sku] || item[:variant_id].to_s,
        goodSrvDesc: item[:name] || "Product #{item[:sku]}",

        # Quantity
        qnty: item[:quantity].to_f,

        # Transaction type - 1 = Sale
        trnTp: 1,

        # Customer information
        custVendCd: cart[:buyer_rep_id],
        custVendName: cart[:buyer_name]

        # Accounting date (defaults to document date)
        #accntDt: Time.current.strftime("%Y-%m-%d")
      }

      line.merge!(format_ship_to_address(cart[:ship_to])) if cart[:ship_to].present?

      line
    end



    # Format ship to address according to Sovos API requirements
    def format_ship_to_address(address)
      return {} if address.blank?

      address = address.with_indifferent_access

      {
        # Ship To address fields with proper Sovos naming
        sTCity: address[:city],
        sTStateProv: normalize_state(address[:state], address[:country_code]),
        sTCountry: address[:country_code],
        sTPstlCd: address[:postal_code],
        sTStNameNum: address[:address1],

        # Also include Bill To as same as Ship To (common for e-commerce)
        bTCity: address[:city],
        bTStateProv: normalize_state(address[:state], address[:country_code]),
        bTCountry: address[:country_code],
        bTPstlCd: address[:postal_code],
        bTStNameNum: address[:address1]
      }.compact
    end

    # Normalize state to 2-letter code if needed
    def normalize_state(state, country_code = nil)
      return nil if state.blank?

      # Use the unified state normalizer if available
      if defined?(StateNormalizer)
        StateNormalizer.to_code(state, country_code)
      else
        # Fallback to US normalizer if available
        if defined?(UsStateNormalizer)
          UsStateNormalizer.to_code(state) || state.to_s.strip.upcase
        else
          # Simple normalization - assume it's already a code
          state.to_s.strip.upcase
        end
      end
    end
  end
end
