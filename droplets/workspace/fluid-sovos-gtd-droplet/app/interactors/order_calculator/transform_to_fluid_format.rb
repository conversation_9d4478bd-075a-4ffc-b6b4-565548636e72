# frozen_string_literal: true

module OrderCalculator
  # TransformToFluidFormat interactor is responsible for transforming Sovos GTD API response
  # into the format required by Fluid.
  #
  # Contract:
  # Input:
  #   - context.sovos_response: Hash with the response from Sovos GTD API
  #   - context.cart_data: Original cart data from Fluid
  # Output:
  #   - context.fluid_response: Hash with the transformed data for Fluid
  #   - context.transform_from_cache: <PERSON><PERSON><PERSON> indicating if transformation was from cache
  # Errors:
  #   - context.fail!(error: "Error message") if transformation fails
  #
  # This interactor includes caching for the transformation to improve performance.
  class TransformToFluidFormat
    include Interactor

    def call
      # Check if we can use a cached transformation
      cache_key = generate_cache_key
      cached_response = Rails.cache.read(cache_key)

      if cached_response.present?
        use_cached_transformation(cached_response, cache_key)
        return
      end

      # No cache hit, perform the transformation
      perform_transformation(cache_key)
    end

    private

    # Use cached transformation
    def use_cached_transformation(cached_response, cache_key)
      context.fluid_response = cached_response
      context.transform_from_cache = true
      Rails.logger.debug("Using cached Sovos to Fluid transformation for key: #{cache_key}")
    end

    # Perform the transformation
    def perform_transformation(cache_key)
      start_time = Time.current

      begin
        transformed_response = transform_sovos_to_fluid_format(context.sovos_response)

        # Store the transformed response in context
        context.fluid_response = transformed_response
        context.transform_from_cache = false

        # Cache the transformation for future requests
        cache_transformation(transformed_response, cache_key)

        # Log performance metrics
        duration = (Time.current - start_time) * 1000 # in milliseconds
        Rails.logger.debug("Transformed Sovos response to Fluid format in #{duration.round(2)}ms")
      rescue StandardError => e
        context.fail!(error: "Error transforming to Fluid format: #{e.message}")
      end
    end

    # Generate a cache key for the transformation
    def generate_cache_key
      # Use SovosCache to generate a consistent key
      SovosCache.generate_key(
        context.cart_data,
        type: :sovos_to_fluid_transform
      )
    end

    # Cache the transformation for future requests
    def cache_transformation(response, key)
      # Cache the transformation with a reasonable TTL
      Rails.cache.write(
        key,
        response,
        expires_in: 30.minutes
      )

      # Publish a notification for monitoring
      ActiveSupport::Notifications.instrument(
        "sovos_cache.transform_stored.order_calculator",
        key: key
      )
    end

    # Main transformation method that converts Sovos response to Fluid format
    def transform_sovos_to_fluid_format(sovos_response)
      # Normalize response with indifferent access for consistent key access
      response = sovos_response.with_indifferent_access

      # Log the full response for debugging
      Rails.logger.info("[DEBUG] Starting transformation of Sovos response: #{response.inspect}")

      # Get original cart data for context
      cart = context.cart_data.with_indifferent_access

      # Extract shipping info from original cart (Sovos doesn't return shipping method details)
      shipping_info = extract_shipping_info_from_cart(cart)

      # Transform line items with tax information
      items = transform_line_items(response[:lnRslts] || [], cart[:items] || [])

      # Calculate totals including tax
      totals = calculate_totals_with_tax(response, cart)

      # Build the response structure
      result = build_response_structure(shipping_info, items, totals)

      # Log the final response
      Rails.logger.info("[DEBUG] Final transformed response: #{result.inspect}")

      result
    end

    # Extract shipping info from original cart data
    def extract_shipping_info_from_cart(cart)
      shipping_method = cart[:shipping_method]
      shipping_address = cart[:ship_to]

      Rails.logger.info("[DEBUG] Extracted shipping_method from cart: #{shipping_method.inspect}")
      Rails.logger.info("[DEBUG] Extracted shipping_address from cart: #{shipping_address.inspect}")

      { 
        method: shipping_method,
        address: shipping_address
      }
    end

    # Calculate all totals including tax from Sovos response
    def calculate_totals_with_tax(response, cart)
      # Get tax amount from Sovos response
      tax_amount = response[:txAmt].to_f || 0.0

      # Get original totals from cart
      subtotal = extract_numeric_value(cart[:sub_total_in_currency] || cart[:sub_total])
      shipping_total = extract_numeric_value(cart[:shipping_total_in_currency] || "0.0")
      discount_total = extract_numeric_value(cart[:discount_total_in_currency] || cart[:discount_total] || "0.0")

      # Calculate final total with tax
      total = subtotal + tax_amount + shipping_total - discount_total

      {
        subtotal: subtotal,
        taxes: tax_amount,
        shipping_total: shipping_total,
        discount: discount_total,
        total: total
      }
    end

    # Extract numeric value from currency string or number
    def extract_numeric_value(value)
      return 0.0 if value.nil?
      return value.to_f if value.is_a?(Numeric)
      
      # Remove currency symbols and convert to float
      value.to_s.gsub(/[^\d.-]/, '').to_f
    end

    # Build the response structure
    def build_response_structure(shipping_info, items, totals)
      {
        success: true,
        data: {
          # Include the totals at the top level for display purposes
          subtotal: totals[:subtotal],
          taxes: totals[:taxes],
          shipping: totals[:shipping_total],
          discount: totals[:discount],
          total: totals[:total],

          # Include the items array with tax information
          items: items,

          # Include shipping_method for compatibility
          shipping_method: shipping_info[:method],

          # Include shipping information in the format expected by Fluid
          shipping_object: {
            id: shipping_info[:method]&.dig(:id) || shipping_info[:method]&.dig("id"),
            price: totals[:shipping_total]
          },

          # Include tax information in the format expected by Fluid
          tax: {
            price: totals[:taxes]
          }
        }
      }
    end

    # Transform line items with tax information from Sovos
    def transform_line_items(line_results, original_items)
      # Create a map of original items by ID for quick lookup
      items_map = original_items.each_with_object({}) do |item, map|
        item = item.with_indifferent_access
        map[item[:id].to_s] = item
      end

      # Transform each line result from Sovos
      line_results.map do |line_result|
        transform_line_item_with_tax(line_result, items_map)
      end
    end

    # Transform a single line item with tax information
    def transform_line_item_with_tax(line_result, items_map)
      line_result = line_result.with_indifferent_access

      # Get the line item ID from Sovos response
      line_id = line_result[:lnId] || line_result[:lnNm]
      
      # Find the original item from cart
      original_item = items_map[line_id.to_s] || {}

      # Extract tax information for this line
      line_tax = line_result[:txAmt].to_f || 0.0
      tax_rate = line_result[:txRate].to_f || 0.0
      gross_amount = line_result[:grossAmt].to_f || 0.0

      # Get original item details
      variant_id = original_item[:variant_id] || original_item.dig(:variant, :id)
      sku = original_item[:sku] || original_item.dig(:product, :sku) || original_item.dig(:variant, :sku)
      quantity = original_item[:quantity] || 1
      price = original_item[:price].to_f || (gross_amount / quantity.to_f rescue 0.0)

      # Build the item with tax information
      {
        variant_id: variant_id,
        sku: sku,
        quantity: quantity,
        price: price,
        subtotal: gross_amount,
        tax: line_tax,
        tax_rate: tax_rate,
        # Include jurisdiction results if needed for detailed tax breakdown
        tax_jurisdictions: extract_jurisdiction_details(line_result[:jurRslts])
      }
    end

    # Extract jurisdiction-level tax details
    def extract_jurisdiction_details(jurisdiction_results)
      return [] unless jurisdiction_results.is_a?(Array)

      jurisdiction_results.map do |jur|
        jur = jur.with_indifferent_access
        {
          name: jur[:txName],
          amount: jur[:txAmt].to_f,
          rate: jur[:txRate].to_f,
          type: map_jurisdiction_type(jur[:txJurUIDJurTp])
        }
      end
    end

    # Map jurisdiction type code to readable string
    def map_jurisdiction_type(type_code)
      case type_code.to_s
      when "1" then "country"
      when "2" then "state"
      when "3" then "county"
      when "4" then "city"
      when "5" then "district"
      else "unknown"
      end
    end
  end
end
