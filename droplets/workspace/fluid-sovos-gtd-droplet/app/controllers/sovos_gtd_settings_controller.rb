# frozen_string_literal: true

class SovosGtdSettingsController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :authenticate_dri, only: [:index]
  before_action :set_sovos_gtd_setting, only: [:index, :update, :load_callbacks]
  before_action :ensure_settings_exist, only: [:update]

  def index
  end

  # New action to load callbacks dynamically
  def load_callbacks
    return unless @sovos_gtd_setting

    callbacks = @sovos_gtd_setting.whitelisted_callbacks

    respond_to do |format|
      format.turbo_stream {
        if callbacks.present?
          # Render each callback individually using your existing partial
          callback_html = callbacks.map do |callback|
            render_to_string(partial: "client_callbacks/client_callback", locals: { callback: },
                             formats: [:html])
          end.join

          render turbo_stream: turbo_stream.replace(
            "callbacks-content",
            "<div class='space-y-4' data-controller='callbacks-settings'>#{callback_html}</div>".html_safe
          )
        else
          # Render empty state
          empty_state_html = "
            <div class='text-center py-8'>
              <div class='text-gray-400 mb-2'>
                <svg class='mx-auto h-12 w-12' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'></path>
                </svg>
              </div>
              <p class='text-gray-500'>No callbacks configured</p>
            </div>
          "

          render turbo_stream: turbo_stream.replace("callbacks-content", empty_state_html.html_safe)
        end
      }
      format.json {
        render json: { callbacks: }
      }
    end
  end

  def update
    @sovos_gtd_setting.update(sovos_gtd_setting_params)

    render json: { message: "Settings updated successfully" }, status: :ok
  rescue StandardError => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  private

  def authenticate_dri
    droplet_installation_uuid = params[:dri]

    unless droplet_installation_uuid.present?
      render json: { error: "Authenticate dri: Authentication droplet_installation_uuid missing" }, status: :unauthorized
      return
    end

    session[:droplet_installation_uuid] = droplet_installation_uuid
  end

  def ensure_settings_exist
    droplet_installation_uuid = session[:droplet_installation_uuid] || params[:dri]
    unless droplet_installation_uuid.present?
      respond_to do |format|
        format.html { redirect_to sovos_gtd_settings_path, alert: "ensure_settings_exist: Session expired. Please try again." }
        format.json { render json: { error: "ensure_settings_exist: Session expired. Please refresh the page and try again." }, status: :unauthorized }
      end
      return
    end

    company = Company.find_by(droplet_installation_uuid: droplet_installation_uuid)

    unless company&.sovos_gtd_setting.present?
      respond_to do |format|
        format.html { redirect_to sovos_gtd_settings_path, alert: "ensure_settings_exist: Settings not found." }
        format.json { render json: { error: "ensure_settings_exist: Settings not found." }, status: :unprocessable_entity }
      end
      return
    end

    true
  end

  def set_sovos_gtd_setting
    @company = Company.find_by(droplet_installation_uuid: session[:droplet_installation_uuid] || params[:dri])

    @sovos_gtd_setting = @company&.sovos_gtd_setting
  end

  def sovos_gtd_setting_params
    params.require(:sovos_gtd_setting).permit(
      :username,
      :password,
      :hmac_key,
      :organization_identification_code
    )
  end
end
