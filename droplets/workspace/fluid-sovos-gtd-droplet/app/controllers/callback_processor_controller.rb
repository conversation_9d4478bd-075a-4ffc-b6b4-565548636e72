# frozen_string_literal: true

  # CallbackProcessorController handles specific callback endpoints for tax calculations.
  #
  # Supported endpoints:
  #   POST /callbacks/update_cart_tax      - Returns { tax_total: amount }
  class CallbackProcessorController < ApplicationController
    # Skip CSRF protection for API requests
    skip_before_action :verify_authenticity_token
    before_action :set_integration_setting_context_from_request, only: [:update_cart_tax]
  
    # Tax calculation endpoint
    # POST /callbacks/update_cart_tax
    def update_cart_tax
      process_cart_calculation("tax")
    end
  
    private
  
    # Process cart calculation using the existing OrderCalculator flow
    # @param calculation_type [String] "tax"
    def process_cart_calculation(calculation_type)
      start_time = Time.current
  
      # Log the request body for debugging
      Rails.logger.info("#{calculation_type.capitalize} calculation request body: #{request.body.read}")
      request.body.rewind # Important to rewind the body after reading
  
      # Use the existing OrderCalculator flow (same as cart_updated webhook)
      result = OrderCalculator::ProcessCartWebhook.call(
        request:,
        skip_cache_check: false # Force skip cache for debugging
      )

      if result.success?
        # Extract only the specific total we need
        response = extract_specific_total(result.fluid_response, calculation_type)
  
        # Log successful calculation
        processing_time = ((Time.current - start_time) * 1000).round(2)
        Rails.logger.info("#{calculation_type.capitalize} calculation successful in #{processing_time}ms")
  
        render json: response, status: :ok
      else
        # Handle errors using the same logic as the existing webhook
        handle_calculation_error(result, calculation_type, start_time)
      end
    rescue StandardError => e
      handle_unexpected_calculation_error(e, calculation_type, start_time)
    end
  
    # Extract the specific total from the full response
    # @param full_response [Hash] The complete response from OrderCalculator
    # @param calculation_type [String] "tax"
    # @return [Hash] The specific response format
    def extract_specific_total(full_response, calculation_type)
      # Log the full response structure for debugging
      Rails.logger.debug("[DEBUG] Full response structure for #{calculation_type}: #{full_response.inspect}")
      case calculation_type
      when "tax"
        # Extract tax total from the transformed response structure
        tax_total = full_response.dig(:data, :tax, :price) ||
                    full_response.dig(:data, :taxes) ||
                    full_response.dig(:tax, :price) ||
                    full_response[:tax_total] ||
                    0.0
  
        Rails.logger.debug("[DEBUG] Extracted tax_total: #{tax_total}")
        { tax_total: }
      else
        { error: "Unknown calculation type: #{calculation_type}" }
      end
    end
  
    # Handle calculation errors (same logic as webhooks_controller)
    def handle_calculation_error(result, calculation_type, start_time)
      processing_time = ((Time.current - start_time) * 1000).round(2)
  
      # Determine the appropriate status code based on the error
      status_code = :unprocessable_entity
  
      # Check for specific error types (same logic as webhooks_controller)
      if result.error.to_s.include?("timed out") || result.error.to_s.include?("timeout")
        status_code = :service_unavailable
        Rails.logger.error("#{calculation_type.capitalize} API Error: Request timed out - #{result.error}")
      elsif result.error.to_s.include?("connection") || result.error.to_s.include?("network")
        status_code = :service_unavailable
        Rails.logger.error("#{calculation_type.capitalize} API Error: Connection issue - #{result.error}")
      else
        Rails.logger.error("#{calculation_type.capitalize} API Error: #{result.error}")
      end
  
      # Ensure we always return a consistent format even for errors
      render json: {
        success: false,
        error: result.error,
        calculation_type:,
        processing_time_ms: processing_time,
      }, status: status_code
    end
  
    # Handle unexpected errors during calculation
    def handle_unexpected_calculation_error(error, calculation_type, start_time)
      processing_time = start_time ? ((Time.current - start_time) * 1000).round(2) : nil
  
      Rails.logger.error("Unexpected error in #{calculation_type} calculation: " \
                          "error=#{error.class.name}: #{error.message}, time=#{processing_time}ms")
      Rails.logger.error(error.backtrace.join("\n")) if error.backtrace
  
      # Don't expose internal error details in production
      error_message = Rails.env.production? ? "Internal server error" : error.message
  
      render json: {
        success: false,
        error: error_message,
        calculation_type:,
        processing_time_ms: processing_time,
      }, status: :internal_server_error
    end
  end
