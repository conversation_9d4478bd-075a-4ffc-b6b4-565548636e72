class WebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :authenticate_webhook_token, unless: :droplet_installed_for_first_time?
  before_action :reject_invalid_droplet

  def create
    event_type = "#{params[:resource]}.#{params[:event]}"
    version = params[:version]

    payload = params.to_unsafe_h.deep_dup

    if EventHandler.route(event_type, payload, version: version)
      # A 202 Accepted indicates that we have accepted the webhook and queued
      # the appropriate background job for processing.
      head :accepted
    else
      head :no_content
    end
  end

private

  def droplet_installed_for_first_time?
    params[:resource] == "droplet" && params[:event] == "installed"
  end

  def authenticate_webhook_token
    company = find_company
    if company.blank?
      render json: { error: "Company not found" }, status: :not_found
    elsif !valid_auth_token?(company)
      render json: { error: "Unauthorized" }, status: :unauthorized
    end
  end

  def valid_auth_token?(company)
    # Check header auth token first, then fall back to params
    auth_header = request.headers["AUTH_TOKEN"] || request.headers["X-Auth-Token"] || request.env["HTTP_AUTH_TOKEN"]
    webhook_auth_token = Setting.fluid_webhook.auth_token

    auth_header.present? && (auth_header == webhook_auth_token || auth_header == company.webhook_verification_token)
  end

  def reject_invalid_droplet
    return true unless params[:company].present?

    if Setting.droplet.uuid != params[:company][:droplet_uuid]
      render json: { error: "Unauthorized" }, status: :unauthorized
      return false
    end

    true
  end

  def find_company
    Company.find_by(company_droplet_uuid: company_params[:company_droplet_uuid]) ||
      Company.find_by(fluid_company_id: company_params[:fluid_company_id])
  end

  def company_params
    # Handle both webhook structures:
    # 1. Old structure: params[:company] hash (for droplet webhooks)
    # 2. New structure: params[:company_id] at root level (for order webhooks)
    if params[:company].present?
      # Old structure - company hash exists
      params.require(:company).permit(:company_droplet_uuid, :droplet_installation_uuid, :fluid_company_id, :webhook_verification_token, :authentication_token)
    else
      # New structure - company_id at root level, create a virtual company hash
      # This allows the existing find_company logic to work without breaking changes
      ActionController::Parameters.new(
        company_droplet_uuid: nil,
        droplet_installation_uuid: nil,
        fluid_company_id: params[:company_id],
        webhook_verification_token: nil,
        authentication_token: nil
      ).permit(:company_droplet_uuid, :droplet_installation_uuid, :fluid_company_id, :webhook_verification_token, :authentication_token)
    end
  end
end
