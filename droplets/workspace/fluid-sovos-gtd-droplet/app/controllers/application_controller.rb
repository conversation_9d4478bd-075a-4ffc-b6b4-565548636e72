class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

protected

  def after_sign_in_path_for(resource)
    admin_dashboard_index_path
  end

  def current_ability
    @current_ability ||= Ability.new(user: current_user)
  end

  # This is used by API controllers to associate activity logs with the correct user
  def extract_integration_setting_from_request
    # Try to parse request body to get company_id
    begin
      request.body.rewind
      body = request.body.read
      request.body.rewind

      if body.present?
        parsed_body = JSON.parse(body)
        company_id = parsed_body.dig("company", "id") || parsed_body.dig("cart", "company", "id")

        if company_id.present?
          company = Company.find_by(fluid_company_id: company_id)
          return unless company.present?
          return SovosGtdSetting.find_by(company: company)
        end
      end
    rescue JSON::ParserError
      # If JSON parsing fails, continue without setting context
    end

    nil
  end

  # Set integration setting context for activity logging
  def set_integration_setting_context_from_request
    integration_setting = extract_integration_setting_from_request
    Thread.current[:current_sovos_integration_setting] = integration_setting if integration_setting
  end
end
