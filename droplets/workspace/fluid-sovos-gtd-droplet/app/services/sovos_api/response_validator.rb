# frozen_string_literal: true

module SovosApi
  # Response validator for Sovos API responses
  class ResponseValidator
    include Interactor

    def call
      response = context.response
      calculation_type = context.calculation_type

      # Validate response structure
      unless response.is_a?(Hash)
        context.fail!(error: "Invalid response format: expected Hash, got #{response.class}")
        return
      end

      # Check for required fields in response
      validate_required_fields(response)
      
      # Validate line results if present
      validate_line_results(response) if response[:lnRslts]

      # Store the validated response
      context.payload = response
    end

    private

    def validate_required_fields(response)
      # Tax amount should be present (even if "0")
      if response[:txAmt].nil?
        context.fail!(error: "Missing required field: txAmt (tax amount)")
        return
      end

      # For committed transactions, we should have a transaction ID
      if context.calculation_type == "commit" && response[:txwTrnDocId].nil?
        Rails.logger.warn "Committed transaction missing txwTrnDocId"
      end
    end

    def validate_line_results(response)
      line_results = response[:lnRslts]
      
      unless line_results.is_a?(Array)
        context.fail!(error: "Invalid line results: expected Array, got #{line_results.class}")
        return
      end

      # Validate each line result
      line_results.each_with_index do |line, index|
        unless line.is_a?(Hash)
          context.fail!(error: "Invalid line result at index #{index}: expected Hash")
          return
        end

        # Each line should have required fields
        if line[:txAmt].nil? || line[:grossAmt].nil?
          context.fail!(error: "Line #{index} missing required fields (txAmt or grossAmt)")
          return
        end
      end
    end
  end
end
