# frozen_string_literal: true

require 'httparty'
require 'openssl'
require 'base64'
require 'json'

module SovosApi
  # Client for communicating with Sovos Global Tax Determination API
  # Uses HMAC-SHA1 authentication as per Sovos requirements
  class Client
    include HTTParty
    
    # Default configuration
    DEFAULT_CONFIG = {
      base_url: 'https://gtduat.sovos.com:443/Twe/api/rest',
      timeout: 30,
      open_timeout: 10,
      headers: {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
      }
    }.freeze

    # Initialize client with configuration
    # @param config [Hash] Configuration options
    # @option config [String] :base_url The base URL for Sovos API
    # @option config [String] :hmac_key The HMAC key for authentication
    # @option config [String] :username The username for authentication
    # @option config [String] :password The password (can be any value for GTD Hosted)
    # @option config [Integer] :timeout Read timeout in seconds
    # @option config [Integer] :open_timeout Connection timeout in seconds
    def initialize(config = {})
      cleaned_config = config.symbolize_keys.compact
      @config = DEFAULT_CONFIG.merge(cleaned_config)
      
      # Validate required configuration
      validate_configuration!
      
      # Set HTTParty configuration
      self.class.base_uri @config[:base_url]
      self.class.default_timeout @config[:timeout]
    end

    # Make a POST request to Sovos API
    # @param endpoint [String] The API endpoint path
    # @param payload [Hash] The request payload
    # @return [Hash] The parsed response
    def post(endpoint, payload)
      make_request(:post, endpoint, payload)
    end

    # Make a GET request to Sovos API
    # @param endpoint [String] The API endpoint path
    # @param params [Hash] Query parameters
    # @return [Hash] The parsed response
    def get(endpoint, params = {})
      make_request(:get, endpoint, nil, params)
    end

    private

    # Validate that required configuration is present
    def validate_configuration!
      if @config[:hmac_key].blank?
        raise SovosApi::Errors::ConfigurationError, "HMAC key is required for Sovos API authentication"
      end

      if @config[:username].blank?
        raise SovosApi::Errors::ConfigurationError, "Username is required for Sovos API authentication"
      end

      if @config[:base_url].blank?
        raise SovosApi::Errors::ConfigurationError, "Base URL is required for Sovos API"
      end
    end

    # Make an HTTP request with HMAC authentication
    # @param method [Symbol] HTTP method (:get, :post, etc.)
    # @param endpoint [String] The API endpoint path
    # @param payload [Hash, nil] The request payload (for POST/PUT)
    # @param params [Hash] Query parameters (for GET)
    # @return [Hash] The parsed response
    def make_request(method, endpoint, payload = nil, params = {})
      # Clean endpoint - remove leading slash if present
      endpoint = endpoint.sub(/^\//, '')
      
      # Generate timestamp for this request
      timestamp = Time.now.utc.iso8601
      
      # Prepare request options
      options = build_request_options(method, endpoint, payload, params, timestamp)
      
      # Log request details
      log_request(method, endpoint, options)
      
      # Make the request - HTTParty will use the endpoint relative to base_uri
      response = self.class.send(method, "/#{endpoint}", options)
      
      # Log response details
      log_response(response)
      
      # Handle the response
      handle_response(response)
    rescue HTTParty::Error => e
      handle_httparty_error(e)
    rescue StandardError => e
      handle_standard_error(e)
    end

    # Build request options including headers and body
    def build_request_options(method, endpoint, payload, params, timestamp)
      options = {
        headers: build_headers(method, endpoint, payload, timestamp),
        timeout: @config[:timeout],
        open_timeout: @config[:open_timeout]
      }

      # Add body for POST/PUT requests
      if [:post, :put, :patch].include?(method) && payload
        options[:body] = payload.to_json
      end

      # Add query parameters for GET requests
      if method == :get && params.any?
        options[:query] = params
      end

      options
    end

    # Build request headers including HMAC authentication
    def build_headers(method, endpoint, payload, timestamp)
      headers = @config[:headers].dup
      
      # Generate HMAC authentication header
      headers['Authorization'] = generate_hmac_header(method, endpoint, payload, timestamp)
      
      # Add Date header (required by Sovos)
      headers['Date'] = timestamp
      
      headers
    end

    # Generate HMAC authentication header using Sovos-specific format
    def generate_hmac_header(method, endpoint, payload, timestamp)
      # Extract username and password from config or payload
      username = @config[:username] || payload&.dig(:usrname) || payload&.dig('usrname')
      password = @config[:password] || payload&.dig(:pswrd) || payload&.dig('pswrd') || 'any_value'
      
      # Create security subject (username + password)
      security_subject = "#{username}#{password}"
      
      # Build the URL path for signing - MUST match Postman format exactly
      # The path must include /Twe/api/rest/ prefix
      endpoint = endpoint.sub(/^\//, '') # Remove leading slash if present
      url_path = "/Twe/api/rest/#{endpoint}"
      
      # Build the string to sign (Sovos-specific format)
      # Format: METHOD + CONTENT_TYPE + TIMESTAMP + URL_PATH + SECURITY_SUBJECT
      string_to_sign = "#{method.to_s.upcase}application/json#{timestamp}#{url_path}#{security_subject}"
      
      # Debug logging
      if defined?(Rails) && (Rails.env.development? || Rails.env.test?)
        Rails.logger.debug "[SovosApi] HMAC Debug:"
        Rails.logger.debug "[SovosApi]   Method: #{method.to_s.upcase}"
        Rails.logger.debug "[SovosApi]   Content-Type: application/json"
        Rails.logger.debug "[SovosApi]   Timestamp: #{timestamp}"
        Rails.logger.debug "[SovosApi]   URL Path: #{url_path}"
        Rails.logger.debug "[SovosApi]   Username: #{username}"
        Rails.logger.debug "[SovosApi]   Security Subject: #{username}#{password ? '[PASSWORD]' : '[NO_PASSWORD]'}"
        Rails.logger.debug "[SovosApi]   String to sign: #{string_to_sign.sub(security_subject, "#{username}[REDACTED]")}"
      end
      
      # Generate HMAC-SHA1 signature
      hmac = OpenSSL::HMAC.digest(
        OpenSSL::Digest.new('sha1'),
        @config[:hmac_key],
        string_to_sign
      )
      
      # Base64 encode the signature
      signature = Base64.strict_encode64(hmac)
      
      # Return in Sovos format: "TAX username:signature"
      auth_header = "TAX #{username}:#{signature}"
      
      Rails.logger.debug "[SovosApi]   Auth header: TAX #{username}:[SIGNATURE]" if defined?(Rails) && (Rails.env.development? || Rails.env.test?)
      
      auth_header
    end

    # Handle the HTTP response
    def handle_response(response)
      case response.code
      when 200..299
        parse_successful_response(response)
      when 400
        raise SovosApi::Errors::ValidationError, "Bad request: #{response.body}"
      when 401
        raise SovosApi::Errors::AuthenticationError, "Authentication failed: #{response.body}"
      when 403
        raise SovosApi::Errors::AuthorizationError, "Access forbidden: #{response.body}"
      when 404
        raise SovosApi::Errors::ResourceNotFoundError, "Resource not found: #{response.body}"
      when 408
        raise SovosApi::Errors::TimeoutError, "Request timeout"
      when 429
        raise SovosApi::Errors::RateLimitError, "Rate limit exceeded: #{response.body}"
      when 500..599
        raise SovosApi::Errors::ServerError, "Server error (#{response.code}): #{response.body}"
      else
        raise SovosApi::Errors::Error, "Unexpected response (#{response.code}): #{response.body}"
      end
    end

    # Parse successful response
    def parse_successful_response(response)
      return {} if response.body.blank?
      
      parsed = JSON.parse(response.body, symbolize_names: true)
      
      # Check for API-level errors in the response
      if parsed[:error] || parsed[:errors]
        error_message = parsed[:error] || parsed[:errors].join(', ')
        raise SovosApi::Errors::Error, "API error: #{error_message}"
      end
      
      parsed
    rescue JSON::ParserError => e
      raise SovosApi::Errors::Error, "Invalid JSON response: #{e.message}"
    end

    # Handle HTTParty-specific errors
    def handle_httparty_error(error)
      case error
      when HTTParty::TimeoutError
        raise SovosApi::Errors::TimeoutError, "Request timed out: #{error.message}"
      when HTTParty::ConnectionError
        raise SovosApi::Errors::ConnectionError, "Connection failed: #{error.message}"
      else
        raise SovosApi::Errors::Error, "HTTP error: #{error.message}"
      end
    end

    # Handle standard errors
    def handle_standard_error(error)
      case error
      when Errno::ECONNREFUSED
        raise SovosApi::Errors::ConnectionError, "Connection refused"
      when Errno::ETIMEDOUT, Net::ReadTimeout, Net::OpenTimeout
        raise SovosApi::Errors::TimeoutError, "Connection timed out"
      when OpenSSL::SSL::SSLError
        raise SovosApi::Errors::ConnectionError, "SSL error: #{error.message}"
      else
        raise SovosApi::Errors::Error, "Unexpected error: #{error.message}"
      end
    end

    # Log request details (for debugging)
    def log_request(method, endpoint, options)
      return unless defined?(Rails) && Rails.logger
      
      Rails.logger.debug "[SovosApi] Request: #{method.upcase} #{endpoint}"
      Rails.logger.debug "[SovosApi] Full URL: #{self.class.base_uri}/#{endpoint}"
      Rails.logger.debug "[SovosApi] Headers: #{sanitize_headers(options[:headers])}"
      Rails.logger.debug "[SovosApi] Body: #{sanitize_body(options[:body])}" if options[:body] && Rails.env.development?
    end

    # Log response details (for debugging)
    def log_response(response)
      return unless defined?(Rails) && Rails.logger
      
      Rails.logger.debug "[SovosApi] Response Code: #{response.code}"
      Rails.logger.debug "[SovosApi] Response Body: #{response.body}" if Rails.env.development?
    end

    # Sanitize headers for logging (remove sensitive data)
    def sanitize_headers(headers)
      sanitized = headers.dup
      sanitized['Authorization'] = '[REDACTED]' if sanitized['Authorization']
      sanitized
    end

    # Sanitize body for logging (remove sensitive data)
    def sanitize_body(body)
      return body unless body.is_a?(String)
      
      begin
        parsed = JSON.parse(body)
        parsed['pswrd'] = '[REDACTED]' if parsed['pswrd']
        parsed.to_json
      rescue JSON::ParserError
        body
      end
    end
  end
end
