# frozen_string_literal: true

module SovosApi
  # Configuration class for Sovos API settings
  class Configuration
    attr_accessor :base_url, :hmac_key, :cache_enabled, :timeout, :open_timeout, :logger

    def initialize
      @base_url = ENV['SOVOS_API_URL'] || 'https://gtduat.sovos.com:443/Twe/api/rest'
      @hmac_key = ''
      @cache_enabled = true
      @timeout = 30
      @open_timeout = 10
      @logger = defined?(Rails) ? Rails.logger : Logger.new(STDOUT)
    end
  end

  class << self
    attr_writer :configuration

    def configuration
      @configuration ||= Configuration.new
    end

    def configure
      yield(configuration)
    end

    # Get configuration for a specific company/organization
    def configuration_for_company(company_id)
      # This is a placeholder - you would typically load this from database
      # For now, return default configuration
      configuration
    end
  end
end
