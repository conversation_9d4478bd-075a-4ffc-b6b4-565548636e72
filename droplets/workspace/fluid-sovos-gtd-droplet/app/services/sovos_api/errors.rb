# frozen_string_literal: true

module SovosApi
  # Module containing all Sovos API error classes
  module Errors
    # Base error class for Sovos API errors
    class Error < StandardError
      attr_reader :response

      def initialize(message, response = nil)
        super(message)
        @response = response
      end
    end

    # Specific error classes
    class ConfigurationError < Error; end
    class ValidationError < Error; end
    class AuthenticationError < Error; end
    class AuthorizationError < Error; end
    class ResourceNotFoundError < Error; end
    class TimeoutError < Error; end
    class ConnectionError < Error; end
    class RateLimitError < Error; end
    class ServerError < Error; end
  end
end
