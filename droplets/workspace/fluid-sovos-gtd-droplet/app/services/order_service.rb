class OrderService
  def self.handle_order_created(payload, is_refund_or_cancellation: false)
    new(payload).handle_order_created(is_refund_or_cancellation)
  end

  def initialize(payload)
    @payload = payload
    @company = find_company
  end

  def handle_order_created(is_refund_or_cancellation = false)
    return unless @company

    # Validate that company has Sovos GTD settings
    unless @company.sovos_gtd_setting.present?
      error_message = "[OrderService] Company #{@company.id} (#{@company.name}) does not have Sovos GTD settings configured"
      Rails.logger.error(error_message)
      raise StandardError, error_message
    end

    Rails.logger.info("[OrderService] Processing order created for company #{@company.id}")

    # Extract order data from the new payload structure
    order_data = @payload.fetch("order", {})
    
    # Log the order creation with actual data from the new structure
    Rails.logger.info("[OrderService] Order created: Order Number: #{order_data["order_number"]} - Subtotal: #{order_data["subtotal"]}")

    # Transform order to Sovos format and call API
    process_order_with_sovos(order_data, is_refund_or_cancellation)

    Rails.logger.info("[OrderService] Successfully processed order created event")
  rescue StandardError => e
    Rails.logger.error("[OrderService] Error processing order created: #{e.message}")
    raise e
  end

  private

  def find_company
    # Extract company_id from the new payload structure
    company_id = @payload["company_id"]

    if company_id
      Company.find_by(fluid_company_id: company_id)
    else
      # Fallback to old structure if needed
      uuid = @payload.dig("company", "company_droplet_uuid")
      fluid_company_id = @payload.dig("company", "fluid_company_id")

      Company.find_by(company_droplet_uuid: uuid) || Company.find_by(fluid_company_id: fluid_company_id)
    end
  end

  def process_order_with_sovos(order_data, is_refund_or_cancellation)
    # Set the current Sovos integration setting in thread storage for the existing services
    Thread.current[:current_sovos_integration_setting] = @company.sovos_gtd_setting

    begin
      # Transform order to Sovos format using the new service
      transform_result = OrderCalculator::TransformOrderToSovosFormat.call(
        order_data: order_data,
        sovos_gtd_setting: @company.sovos_gtd_setting,
        is_refund_or_cancellation: is_refund_or_cancellation
      )

      if transform_result.failure?
        Rails.logger.error("[OrderService] Failed to transform order to Sovos format: #{transform_result.error}")
        return
      end

      # Call Sovos GTD API using the existing service
      api_result = OrderCalculator::CallSovosGtdApi.call(
        sovos_payload: transform_result.sovos_payload,
        sovos_gtd_setting: @company.sovos_gtd_setting,
        order_data: order_data, # Pass order_data instead of cart_data for logging
        calculation_type: "quote"
      )

      if api_result.failure?
        Rails.logger.error("[OrderService] Failed to call Sovos GTD API: #{api_result.error}")
        return
      end

      # Log successful API call
      Rails.logger.info("[OrderService] Successfully called Sovos GTD API for order #{order_data["order_number"]}")
      Rails.logger.info("[OrderService] Sovos response: #{api_result.sovos_response.inspect}")

    ensure
      # Clean up thread storage
      Thread.current[:current_sovos_integration_setting] = nil
    end
  end
end
