# frozen_string_literal: true

# Service for handling Sovos API caching
# This extends the SovosCache module defined in config/initializers/sovos_cache.rb
module SovosCache
  # Generate a cache key for Sovos API requests or transformations
  # This method supports both positional and named parameters for backward compatibility
  # @overload generate_key(cart_payload, type = :order_calculation)
  #   @param cart_payload [Hash] The cart payload sent to Sovos
  #   @param type [Symbol] The type of cache key to generate
  # @overload generate_key(type:, data:)
  #   @param type [Symbol] The type of cache key to generate
  #   @param data [Hash] The data to include in the key
  # @return [String] The generated cache key
  def self.generate_key(*args, **kwargs)
    if kwargs.key?(:type) || kwargs.key?(:data)
      # Named parameters version
      type = kwargs[:type] || :order_calculation
      data = kwargs[:data] || {}
      data_digest = Digest::MD5.hexdigest(data.to_json)
      "sovos_api:#{type}:#{Rails.env}:#{data_digest}"
    else
      # Positional parameters version
      cart_payload = args[0] || {}
      type = args[1] || :order_calculation

      # Process the cart payload to extract relevant data
      # This is similar to what the initializer does
      if cart_payload.respond_to?(:with_indifferent_access)
        cart_payload = cart_payload.with_indifferent_access
      else
        cart_payload = {}
      end

      # Extract items data
      items_data = []
      if cart_payload[:items].is_a?(Array)
        items_data = cart_payload[:items].map do |item|
          item = item.respond_to?(:with_indifferent_access) ? item.with_indifferent_access : item
          [item[:sku] || item[:id], item[:quantity]]
        end.sort
      end

      # Extract shipping method ID
      shipping_method_id = nil
      if cart_payload[:shipping_method].present?
        shipping_method = cart_payload[:shipping_method]
        if shipping_method.is_a?(Hash)
          shipping_method_id = shipping_method[:id]
        elsif shipping_method.is_a?(Integer) || shipping_method.to_s.match?(/^\d+$/)
          shipping_method_id = shipping_method.to_i
        end
      end

      # Create relevant data hash
      relevant_data = {
        items: items_data,
        ship_to: cart_payload[:ship_to],
        shipping_method: shipping_method_id,
        buyer_rep_id: cart_payload[:buyer_rep_id],
        type: cart_payload[:type],
      }

      # Generate digest and key
      digest = Digest::MD5.hexdigest(relevant_data.to_json)
      "sovos_api:#{type}:#{digest}"
    end
  end

  # Clear all Sovos API caches
  # @return [Boolean] True if successful
  def self.clear_all
    if redis_cache?
      # Get all keys matching the pattern
      keys = Rails.cache.redis.keys("sovos_api:*")

      # Delete all matching keys
      Rails.cache.redis.del(*keys) if keys.any?

      # Publish a notification for monitoring
      ActiveSupport::Notifications.instrument(
        "sovos_cache.cleared.all",
        count: keys.size
      )
    else
      # For non-Redis caches, we can't easily clear by pattern
      # Just log a message and return true
      Rails.logger.info("Cache store is not Redis, can't clear by pattern")
      ActiveSupport::Notifications.instrument(
        "sovos_cache.cleared.all",
        count: 0
      )
    end

    true
  rescue StandardError => e
    Rails.logger.error("Failed to clear Sovos API caches: #{e.message}")
    false
  end

  # Clear a specific type of Sovos API cache
  # @param type [Symbol] The type of cache to clear
  # @return [Boolean] True if successful
  def self.clear_type(type)
    if redis_cache?
      # Get all keys matching the pattern
      keys = Rails.cache.redis.keys("sovos_api:#{type}:*")

      # Delete all matching keys
      Rails.cache.redis.del(*keys) if keys.any?

      # Publish a notification for monitoring
      ActiveSupport::Notifications.instrument(
        "sovos_cache.cleared.type",
        type:,
        count: keys.size
      )
    else
      # For non-Redis caches, we can't easily clear by pattern
      # Just log a message and return true
      Rails.logger.info("Cache store is not Redis, can't clear by pattern for type #{type}")
      ActiveSupport::Notifications.instrument(
        "sovos_cache.cleared.type",
        type:,
        count: 0
      )
    end

    true
  rescue StandardError => e
    Rails.logger.error("Failed to clear Sovos API caches for type #{type}: #{e.message}")
    false
  end

  # Check if the cache store is Redis
  # @return [Boolean] True if the cache store is Redis
  def self.redis_cache?
    begin
      Rails.cache.respond_to?(:redis) && Rails.cache.redis.present?
    rescue StandardError => e
      Rails.logger.debug("Error checking if cache is Redis: #{e.message}")
      false
    end
  end

  # NOTE: The enabled? method is defined in config/initializers/sovos_cache.rb
  # This is just a placeholder to avoid confusion
  # def self.enabled?
  #   # By default, caching is enabled in production and staging
  #   # but can be overridden for testing
  #   Rails.env.production? || Rails.env.staging?
  # end
end
