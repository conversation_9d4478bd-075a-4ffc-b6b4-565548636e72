default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: droplet_template_development

test:
  <<: *default
  database: droplet_template_test

production:
  primary: &primary
    <<: *default
    url: <%= ENV["DATABASE_URL"] %>

  cache:
    <<: *primary
    url: <%= ENV["CACHE_DATABASE_URL"] %>
    migrations_paths: db/cache_migrate

  queue:
    <<: *primary
    url: <%= ENV["QUEUE_DATABASE_URL"] %>
    migrations_paths: db/queue_migrate

  cable:
    <<: *primary
    url: <%= ENV["CABLE_DATABASE_URL"] %>
    migrations_paths: db/cable_migrate


