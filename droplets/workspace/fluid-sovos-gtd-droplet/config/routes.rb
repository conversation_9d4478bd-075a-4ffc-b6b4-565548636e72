Rails.application.routes.draw do
  root "home#index"

  devise_for :users

  post "webhook", to: "webhooks#create", as: :webhook

  namespace :admin do
    get "dashboard/index"
    resource :droplet, only: %i[ create update ]
    resources :settings, only: %i[ index edit update ]
    resources :users
  end

  post "callbacks/update_cart_tax", to: "callback_processor#update_cart_tax"

  get "up" => "rails/health#show", as: :rails_health_check

  get "sovos_gtd_settings", to: "sovos_gtd_settings#index"
  patch "sovos_gtd_settings", to: "sovos_gtd_settings#update"
end
