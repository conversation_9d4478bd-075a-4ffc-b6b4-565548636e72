# frozen_string_literal: true

# Load SovosApi classes
require_relative "../../app/services/sovos_api/configuration"
require_relative "../../app/services/sovos_api/errors"
require_relative "../../app/services/sovos_api/client"

# Configure Sovos API
SovosApi.configure do |config|
  config.base_url = ENV['SOVOS_API_URL'] || 'https://gtduat.sovos.com:443/Twe/api/rest'
  config.hmac_key = ''
  config.cache_enabled = !Rails.env.test? # Disable caching in tests
  config.timeout = 30
  config.open_timeout = 10
  config.logger = Rails.logger
end

# Log configuration status
Rails.logger.info "[SovosApi] Initialized with base_url: #{SovosApi.configuration.base_url}"
Rails.logger.info "[SovosApi] HMAC key configured: #{SovosApi.configuration.hmac_key.present?}"
Rails.logger.info "[SovosApi] Cache enabled: #{SovosApi.configuration.cache_enabled}"
