class CreateSovosGtdSettings < ActiveRecord::Migration[8.0]
  def change
    create_table :sovos_gtd_settings do |t|
      t.references :company, null: false, foreign_key: true
      t.string :username, null: false
      t.string :password, null: false
      t.string :hmac_key, null: false
      t.string :organization_identification_code, null: false

      t.timestamps
    end

    add_index :sovos_gtd_settings, [:company_id, :username], unique: true
  end
end
