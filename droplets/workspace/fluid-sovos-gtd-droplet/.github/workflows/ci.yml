name: CI

env:
  RUBY_VERSION: '3.4.2'
  NODE_VERSION: '23.8.0'
  YARN_VERSION: '4.7.0'

on:
  pull_request:
  push:
    branches: [ main ]

jobs:
  system-setup:
    runs-on: ubuntu-latest
    outputs:
      ruby-cache-key: ${{ steps.ruby-cache-key.outputs.value }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Install system dependencies
        uses: awalsh128/cache-apt-pkgs-action@latest
        with:
          packages: unixodbc unixodbc-dev freetds-dev freetds-bin tdsodbc libcurl4-openssl-dev libpq-dev
          version: 1.0

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ env.RUBY_VERSION }}
          bundler-cache: true

      - name: Generate Ruby cache key
        id: ruby-cache-key
        run: echo "value=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT

      - name: Cache Ruby dependencies
        uses: actions/cache/save@v4
        with:
          path: |
            vendor/bundle
            .bundle
            tmp/cache
          key: ${{ runner.os }}-ruby-${{ steps.ruby-cache-key.outputs.value }}

  rails-security-scan:
    runs-on: ubuntu-latest
    needs: [system-setup]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ env.RUBY_VERSION }}
          bundler-cache: true

      - name: Restore Ruby dependencies
        uses: actions/cache/restore@v4
        with:
          path: |
            vendor/bundle
            .bundle
            tmp/cache
          key: ${{ runner.os }}-ruby-${{ needs.system-setup.outputs.ruby-cache-key }}
          fail-on-cache-miss: true

      - name: Scan for common Rails security vulnerabilities using static analysis
        run: bin/brakeman --no-pager

  rubocop:
    runs-on: ubuntu-latest
    needs: [system-setup]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ env.RUBY_VERSION }}
          bundler-cache: true

      - name: Restore Ruby dependencies
        uses: actions/cache/restore@v4
        with:
          path: |
            vendor/bundle
            .bundle
            tmp/cache
          key: ${{ runner.os }}-ruby-${{ needs.system-setup.outputs.ruby-cache-key }}
          fail-on-cache-miss: true

      - name: Lint code for consistent style
        run: bin/rubocop -f github

  tests:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    needs: [system-setup]

    services:
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: your_test_db_name
        ports:
          - 5432:5432
        options: --health-cmd="pg_isready" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis
        ports:
          - 6379:6379
        options: --health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install packages
        run: sudo apt-get update && sudo apt-get install --no-install-recommends -y build-essential git libpq-dev node-gyp pkg-config python-is-python3

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ env.RUBY_VERSION }}
          bundler-cache: true

      - name: Restore Ruby dependencies
        uses: actions/cache/restore@v4
        with:
          path: |
            vendor/bundle
            .bundle
            tmp/cache
          key: ${{ runner.os }}-ruby-${{ needs.system-setup.outputs.ruby-cache-key }}
          fail-on-cache-miss: true

      - name: Set up nodejs
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Enable corepack
        run: corepack enable

      - name: Prepare yarn
        run: corepack prepare yarn@${{ env.YARN_VERSION }} --activate

      - name: Install JavaScript dependencies
        run: yarn install --immutable

      - name: Build assets
        run: yarn build

      - name: Run JavaScript tests
        run: yarn test

      - name: Run Rails tests
        env:
          RAILS_ENV: test
          DATABASE_URL: postgres://postgres:postgres@localhost:5432
          REDIS_URL: redis://localhost:6379/0
        run: bin/rails db:test:prepare test test:system

      - name: Keep screenshots from failed system tests
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: screenshots
          path: ${{ github.workspace }}/tmp/screenshots
          if-no-files-found: ignore

      - name: Cache build artifacts
        uses: actions/cache@v4
        with:
          path: |
            public/assets
            tmp/cache/assets
            tmp/cache/webpacker
          key: ${{ runner.os }}-build-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-build-
