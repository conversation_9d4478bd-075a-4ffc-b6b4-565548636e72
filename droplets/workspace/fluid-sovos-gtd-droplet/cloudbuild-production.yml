# google cloud build
# gcloud beta builds submit --config cloudbuild-production.yml --region=us-west3 --substitutions=COMMIT_SHA=$(git rev-parse --short HEAD),_TIMESTAMP=$(date +%Y%m%d%H%M%S) --project=fluid-417204 .
steps:
  - name: gcr.io/cloud-builders/docker
    args:
      [
        "build",
        "--no-cache",
        "-t",
        "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA",
        ".",
        "-f",
        "docker/Dockerfile",
      ]
    id: build

  - name: gcr.io/cloud-builders/docker
    args:
      [
        "tag",
        "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA",
        "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:latest",
      ]
    id: Tag

  - name: gcr.io/cloud-builders/docker
    args:
      [
        "push",
        "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA",
      ]
    id: push commit sha

  - name: gcr.io/cloud-builders/docker
    args:
      [
        "push",
        "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:latest",
      ]
    id: push latest

  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk:slim"
    args:
      [
        "run",
        "jobs",
        "update",
        "${_APP_NAME}-migrations",
        "--image=us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA",
        "--region=us-west3",
        "--execute-now",
        "--wait",
      ]
    id: Migrate
    entrypoint: gcloud

  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk:slim"
    args:
      [
        "run",
        "services",
        "update",
        "${_APP_NAME}",
        "--platform=managed",
        "--image=us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA",
        "--region=us-west3",
        "--revision-suffix=${COMMIT_SHA}-${_TIMESTAMP}",
      ]
    id: Deploy Production
    entrypoint: gcloud

  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk:slim"
    args:
      [
        "compute",
        "instances",
        "update-container",
        "${_APP_NAME}-jobs-console",
        "--zone=us-west3-b",
        "--container-image=us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA",
        "--quiet",
      ]
    id: Update Container Jobs Production
    entrypoint: gcloud

substitutions:
  _APP_NAME: "fluid-droplet-sovos"
  _APP_REPOSITORY: "fluid-droplets"

images:
  - "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:$COMMIT_SHA"
  - "us-west3-docker.pkg.dev/${PROJECT_ID}/${_APP_REPOSITORY}/${_APP_NAME}-rails/web:latest"

options:
  substitutionOption: ALLOW_LOOSE
  logging: CLOUD_LOGGING_ONLY
  machineType: E2_HIGHCPU_8
