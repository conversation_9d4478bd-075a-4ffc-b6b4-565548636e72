default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: fluid-droplet-dynamic-bundle_development

test:
  <<: *default
  database: fluid-droplet-dynamic-bundle_test

production:
  primary:
    <<: *default
    url: <%= ENV['DATABASE_URL'] %>
  queue:
    <<: *default
    url: <%= ENV['DATABASE_URL'] %>
  cache:
    <<: *default
    url: <%= ENV['DATABASE_URL'] %>


production:
  primary: &primary_production
    <<: *default
    database: fluid-droplet-dynamic-bundle_production
    url: <%= ENV['DATABASE_URL'] %>
  cache:
    <<: *primary_production
    database: fluid-droplet-dynamic-bundle_production_cache
    migrations_paths: db/cache_migrate
  queue:
    <<: *primary_production
    database: fluid-droplet-dynamic-bundle_production_queue
    migrations_paths: db/queue_migrate
  cable:
    <<: *primary_production
    database: fluid-droplet-dynamic-bundle_production_cable
    migrations_paths: db/cable_migrate