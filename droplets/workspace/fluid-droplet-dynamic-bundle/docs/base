Dynamic Bundle Droplet Specification
Bundle is a product that contains child products
Overview
The Dynamic bundle Droplet is a custom solution that enables the creation and management of configurable product bundles within Fluid's platform. This droplet creates bundle shells in Fluid while managing bundle configuration through a separate admin interface, allowing customers to select items from predefined categories while maintaining bundle-level pricing and tax handling.
Key Stakeholders
Client: <PERSON><PERSON> (primary client requiring this functionality)
Platform: Fluid
Business Requirements
Yoli requires dynamic bundle functionality across their entire platform, not limited to enrollment flows
The transformation bundle is Yoli's flagship product requiring up to 12 categories
Solution must be general enough to support other Fluid clients beyond Yoli
Most dynamic bundles contain fewer than 10 categories but we should not restrict this
Functional Requirements
1. Dynamic bundle Definition
1.1 Bundle Structure
Each dynamic bundle has its own unique SKU
bundles contain multiple selectable categories
Categories can contain products from different SKUs and variants
bundles can include both:
Dynamic items: Customer-selectable from categories
Static items: This would just be if a category only had one item available not fundamentally different
1.2 Pricing Model
Pricing and tax are handled at the bundle level in current fluid admin creating a dynamic bundle from the droplet would create a bundle in fluid that the user could then go to in order to manage pricing and other things not specific to the dynamic nature of the bundle
2. Admin Interface Requirements
2.1 Bundle Management
Create new dynamic bundles
Define categories within bundles
Assign products to categories
Set default items within categories (Top sorted product would be default) so need sort order in the generated meta data
Activate/deactivate categories for promotional purposes
2.2 Ordering Controls
Control display order of categories within bundles
Control display order of items within categories
Categories are defined per bundle (not reusable across bundles in initial implementation)
Future consideration: Clone functionality for category reuse
2.3 Product Assignment
Ability to add all variants of a product to a category
Ability to select specific variants for inclusion
Product search functionality within the droplet interface
We have existing functions in fluid admin for regular bundles we can probably use some of that code
3. Technical Implementation
3.1 Architecture
Custom droplet solution creating bundle shells in Fluid's platform (Bundles with no child items)
Leverages existing Fluid APIs for bundle management
Exports metadata objects to bundles for theme/front-end access (Should contain all data about the dynamic bundle)
Integrates with existing infrastructure without core platform changes
3.2 API Integration
Uses Fluid's existing APIs to add child items to bundles
Maintains bundle pricing and configuration through API calls
Supports dynamic addition of items based on customer selection
3.3 Data Structure
Bundle hierarchy: Parent bundle → Child items
Metadata attached to bundles for configuration storage
4. Front-End Requirements (Not Droplet Team)
4.1 Product Page Display
Render dynamic bundles on product pages using existing theme capabilities
Display categories and selectable items within each category
Show default selections where configured
4.2 Cart Display (Handled by Core no action needed from Studios)
Clear bundle hierarchy visualization
Parent bundle shown with child items nested beneath
Individual items clearly separated
Subscribe and save status indicators
Clear value markings for each item
4.3 Mobile Support (Probably a future req need to discuss with Mobile Team)
Full functionality required for mobile applications
Transformation bundle (flagship product) must be available in app
Consistent experience across web and mobile platforms
5. Order Processing (Data Team discussion needs to happen)
5.1 Order Structure
Orders pass to Exigo with parent SKU and child items
Categories not included in final order data
Metadata appended post-checkout for proper integration
5.2 Integration Format
Proper formatting for Exego system compatibility
Bundle structure maintained through order process
Development Approach
Phase 1: Proof of Concept
Jake Bliss to provide POC front-end materials (Version 1 Done) 
https://claude.ai/artifacts/37854e88-2fb6-41d9-adee-82130869cb63
Mockups for Jason and Brandi to review
Iterative feedback before backend implementation
Phase 2: Backend Implementation (Droplet Team)
Build custom droplet with admin interface
Implement API integrations
Create metadata export functionality
The data for the bundle itself will be a json object stored in the meta data of a fluid product. This way the droplet should not rely on any external data storage at all. At the bottom of this doc is a sample JSON Object to get started.
Phase 3: Front-End Integration (Studios Team)
Theme updates for bundle display
Cart display modifications
Mobile app integration
Constraints & Considerations
Current Limitations
Categories are bundle-specific (not reusable in initial version)
Must work within existing Fluid platform constraints
Future Enhancements (Not needed for launch)
Reusable category templates
Clone functionality for categories
Success Criteria
Dynamic bundles function across entire Yoli platform
Transformation bundle fully operational with up to 12 categories
Clean cart display with clear bundle hierarchy
Successful order processing to Exigo
Mobile app compatibility
Solution general enough for other Fluid clients
Open Questions/Items for Follow-up
Detailed mobile app integration specifications
Sample JSON Object

{
  "sku": "TRANS-BUNDLE-001",
  "fluidProductId": "12345",
  "bundleName": "Transformation Bundle",
  "description": "Our flagship transformation bundle with customizable wellness products",
  "status": "active",
  "createdAt": "2025-08-05T10:00:00Z",
  "updatedAt": "2025-08-05T14:30:00Z",
  "categories": [
    {
      "categoryId": "cat-1",
      "categoryName": "Protein Powders",
      "displayOrder": 0,
      "selectionQuantity": 2,
      "products": [
        {
          "productId": "prod1",
          "variantTitle": "Whey Protein - Vanilla",
          "variantSku": "WHEY-VAN-001",
          "displayOrder": 0,
          "isDefault": true
        },
        {
          "productId": "prod1",
          "variantTitle": "Whey Protein - Chocolate",
          "variantSku": "WHEY-CHO-001",
          "displayOrder": 1,
          "isDefault": false
        },
        {
          "productId": "prod1",
          "variantTitle": "Whey Protein - Strawberry",
          "variantSku": "WHEY-STR-001",
          "displayOrder": 2,
          "isDefault": false
        },
        {
          "productId": "prod2",
          "variantTitle": "Plant Protein - Berry",
          "variantSku": "PLANT-BER-001",
          "displayOrder": 3,
          "isDefault": false
        },
        {
          "productId": "prod2",
          "variantTitle": "Plant Protein - Vanilla",
          "variantSku": "PLANT-VAN-001",
          "displayOrder": 4,
          "isDefault": false
        }
      ]
    },
    {
      "categoryId": "cat-2",
      "categoryName": "Pre-Workout",
      "displayOrder": 1,
      "selectionQuantity": 1,
      "products": [
        {
          "productId": "prod3",
          "variantTitle": "Energy Boost - Citrus",
          "variantSku": "ENRG-CIT-001",
          "displayOrder": 0,
          "isDefault": true
        },
        {
          "productId": "prod3",
          "variantTitle": "Energy Boost - Berry Blast",
          "variantSku": "ENRG-BER-001",
          "displayOrder": 1,
          "isDefault": false
        },
        {
          "productId": "prod4",
          "variantTitle": "Focus Formula - Unflavored",
          "variantSku": "FOCUS-UNF-001",
          "displayOrder": 2,
          "isDefault": false
        }
      ]
    },
    {
      "categoryId": "cat-3",
      "categoryName": "Recovery",
      "displayOrder": 2,
      "selectionQuantity": 1,
      "products": [
        {
          "productId": "prod5",
          "variantTitle": "BCAA Complex - Lemon Lime",
          "variantSku": "BCAA-LEM-001",
          "displayOrder": 0,
          "isDefault": true
        },
        {
          "productId": "prod5",
          "variantTitle": "BCAA Complex - Orange",
          "variantSku": "BCAA-ORA-001",
          "displayOrder": 1,
          "isDefault": false
        },
        {
          "productId": "prod6",
          "variantTitle": "Recovery Plus - Mixed Berry",
          "variantSku": "RECV-MIX-001",
          "displayOrder": 2,
          "isDefault": false
        }
      ]
    },
    {
      "categoryId": "cat-4",
      "categoryName": "Vitamins",
      "displayOrder": 3,
      "selectionQuantity": 1,
      "products": [
        {
          "productId": "prod7",
          "variantTitle": "Daily Multi - Standard",
          "variantSku": "MULTI-STD-001",
          "displayOrder": 0,
          "isDefault": true
        },
        {
          "productId": "prod7",
          "variantTitle": "Daily Multi - Women's Formula",
          "variantSku": "MULTI-WOM-001",
          "displayOrder": 1,
          "isDefault": false
        },
        {
          "productId": "prod7",
          "variantTitle": "Daily Multi - Men's Formula",
          "variantSku": "MULTI-MEN-001",
          "displayOrder": 2,
          "isDefault": false
        }
      ]
    },
    {
      "categoryId": "cat-5",
      "categoryName": "Shaker Bottle",
      "displayOrder": 4,
      "selectionQuantity": 1,
      "products": [
        {
          "productId": "prod8",
          "variantTitle": "Premium Shaker Bottle",
          "variantSku": "SHAKE-001",
          "displayOrder": 0,
          "isDefault": true
        }
      ]
    },
    {
      "categoryId": "cat-6",
      "categoryName": "Welcome Guide",
      "displayOrder": 5,
      "selectionQuantity": 1,
      "products": [
        {
          "productId": "prod9",
          "variantTitle": "Transformation Welcome Guide",
          "variantSku": "GUIDE-001",
          "displayOrder": 0,
          "isDefault": true
        }
      ]
    }
  ]
}


