<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Kit Admin - Droplet POC</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f7;
        }

        .admin-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .admin-header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Tab Navigation */
        .tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-nav {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
        }

        .tab-button {
            padding: 15px 30px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background: #f5f5f5;
        }

        .tab-button.active {
            color: #3498db;
            border-bottom-color: #3498db;
        }

        .tab-content {
            padding: 30px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Kit List View */
        .kit-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .kit-table {
            width: 100%;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .kit-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #666;
            border-bottom: 2px solid #e0e0e0;
        }

        .kit-table td {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .kit-table tr:hover {
            background: #f8f9fa;
        }

        .status-active {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }

        .status-inactive {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }

        /* Kit Editor */
        .kit-editor {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        /* Categories Section */
        .categories-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #e0e0e0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .category-item {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .quantity-input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }

        .quantity-input-group label {
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            margin: 0;
        }

        .quantity-input-group input[type="number"] {
            width: 60px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
        }

        .category-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .category-actions {
            display: flex;
            gap: 10px;
        }

        .btn-icon {
            background: none;
            border: 1px solid #ddd;
            width: 36px;
            height: 36px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .btn-icon:hover {
            background: #f5f5f5;
            border-color: #999;
        }

        .category-options {
            background: #f0f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        /* Products in Category */
        .products-section {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }

        .product-search {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .product-search input {
            flex: 1;
        }

        .selected-products {
            margin-top: 15px;
        }

        .product-list-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 10px 15px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .product-list-item.dragging {
            opacity: 0.5;
        }

        .product-list-item:hover {
            background: #f8f9fa;
        }

        .product-list-item:first-child {
            border-left: 3px solid #27ae60;
        }

        .product-list-item:first-child::after {
            content: "DEFAULT";
            font-size: 10px;
            color: #27ae60;
            font-weight: bold;
            margin-left: 10px;
            padding: 2px 8px;
            background: #e8f5e9;
            border-radius: 10px;
        }

        .product-info-section {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .product-tag {
            background: #3498db;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .product-tag .remove {
            cursor: pointer;
            font-weight: bold;
        }

        .product-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Move handles */
        .move-handle {
            cursor: move;
            color: #999;
            margin-right: 10px;
            user-select: none;
        }

        .category-item.dragging {
            opacity: 0.5;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h2 {
            font-size: 24px;
            color: #2c3e50;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        /* Product Search Results */
        .search-results {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-top: 10px;
        }

        .search-result-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .search-result-item:hover {
            background: #f8f9fa;
        }

        .search-result-item.selected {
            background: #e3f2fd;
        }

        .product-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-details {
            display: flex;
            align-items: flex-start;
        }

        .product-name {
            font-weight: 600;
            color: #333;
        }

        .product-sku {
            font-size: 12px;
            color: #999;
        }

        .variant-list {
            margin-left: 20px;
            margin-top: 5px;
        }

        .variant-item {
            padding: 5px 10px;
            font-size: 14px;
            color: #666;
        }

        .variant-checkbox {
            margin-right: 8px;
        }
    </style>
</head>

<body>
    <header class="admin-header">
        <div class="container">
            <h1>Dynamic Bundle Configuration - Admin Droplet</h1>
        </div>
    </header>

    <div class="container">
        <div class="tabs">
            <div class="tab-nav">
                <button class="tab-button active" onclick="switchTab('kits')">Dynamic Bundles</button>
                <button class="tab-button" onclick="switchTab('create')">Create New Bundle</button>
            </div>

            <!-- Bundles List Tab -->
            <div id="kits-tab" class="tab-content active">
                <div class="kit-list-header">
                    <h2>Manage Dynamic Bundles</h2>
                    <button class="btn-primary" onclick="switchTab('create')">+ Create New Bundle</button>
                </div>

                <table class="kit-table">
                    <thead>
                        <tr>
                            <th>Bundle Name</th>
                            <th>SKU</th>
                            <th>Categories</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Transformation Bundle</td>
                            <td>TRANS-BUNDLE-001</td>
                            <td>12 categories</td>
                            <td><span class="status-active">Active</span></td>
                            <td>
                                <button class="btn-secondary" onclick="editKit('TRANS-BUNDLE-001')">Edit</button>
                                <button class="btn-secondary" onclick="openFluidProduct('TRANS-BUNDLE-001')"
                                    title="Manage pricing and settings in Fluid">
                                    <span style="margin-right: 5px;">🔗</span>Fluid Product
                                </button>
                                <button class="btn-danger">Deactivate</button>
                            </td>
                        </tr>
                        <tr>
                            <td>Starter Bundle</td>
                            <td>START-BUNDLE-001</td>
                            <td>5 categories</td>
                            <td><span class="status-active">Active</span></td>
                            <td>
                                <button class="btn-secondary" onclick="editKit('START-BUNDLE-001')">Edit</button>
                                <button class="btn-secondary" onclick="openFluidProduct('START-BUNDLE-001')"
                                    title="Manage pricing and settings in Fluid">
                                    <span style="margin-right: 5px;">🔗</span>Fluid Product
                                </button>
                                <button class="btn-danger">Deactivate</button>
                            </td>
                        </tr>
                        <tr>
                            <td>Holiday Special Bundle</td>
                            <td>HOLIDAY-BUNDLE-001</td>
                            <td>8 categories</td>
                            <td><span class="status-inactive">Inactive</span></td>
                            <td>
                                <button class="btn-secondary" onclick="editKit('HOLIDAY-BUNDLE-001')">Edit</button>
                                <button class="btn-secondary" onclick="openFluidProduct('HOLIDAY-BUNDLE-001')"
                                    title="Manage pricing and settings in Fluid">
                                    <span style="margin-right: 5px;">🔗</span>Fluid Product
                                </button>
                                <button class="btn-primary">Activate</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Create/Edit Bundle Tab -->
            <div id="create-tab" class="tab-content">
                <div class="kit-editor">
                    <h2 id="editor-title">Create New Dynamic Bundle</h2>

                    <div class="form-group">
                        <label for="kit-name">Bundle Name</label>
                        <input type="text" id="kit-name" class="form-control" placeholder="e.g., Transformation Bundle">
                    </div>

                    <div class="form-group">
                        <label for="kit-sku">Bundle SKU</label>
                        <input type="text" id="kit-sku" class="form-control" placeholder="e.g., TRANS-BUNDLE-001">
                    </div>

                    <div class="form-group">
                        <label for="kit-description">Description</label>
                        <textarea id="kit-description" class="form-control" rows="3"
                            placeholder="Describe this dynamic bundle..."></textarea>
                    </div>

                    <div class="categories-section">
                        <div class="section-header">
                            <h3>Categories</h3>
                            <button class="btn-primary" onclick="addCategory()">+ Add Category</button>
                        </div>

                        <div id="categories-container">
                            <!-- Categories will be dynamically added here -->
                        </div>
                    </div>

                    <div style="margin-top: 30px; display: flex; gap: 10px;">
                        <button class="btn-primary" onclick="saveKit()">Save Bundle</button>
                        <button class="btn-secondary" onclick="switchTab('kits')">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Search Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add Products to Category</h2>
                <button class="close-modal" onclick="closeModal()">&times;</button>
            </div>

            <div class="form-group">
                <label>Search Products</label>
                <input type="text" id="product-search-input" class="form-control" placeholder="Search by name or SKU..."
                    oninput="searchProducts()">
            </div>

            <div class="search-results" id="search-results">
                <!-- Search results will appear here -->
            </div>

            <div style="margin-top: 20px; display: flex; gap: 10px;">
                <button class="btn-primary" onclick="addSelectedProducts()">Add Selected Products</button>
                <button class="btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        let currentCategoryId = null;
        let categoryCount = 0;
        let selectedProducts = new Set();

        // Sample product data (would come from API)
        const availableProducts = [
            { id: 'prod1', name: 'Whey Protein', sku: 'WHEY-001', variants: ['Vanilla', 'Chocolate', 'Strawberry'] },
            { id: 'prod2', name: 'Plant Protein', sku: 'PLANT-001', variants: ['Vanilla', 'Berry', 'Unflavored'] },
            { id: 'prod3', name: 'Energy Boost', sku: 'ENRG-001', variants: ['Citrus', 'Berry Blast', 'Tropical'] },
            { id: 'prod4', name: 'BCAA Complex', sku: 'BCAA-001', variants: ['Lemon Lime', 'Orange', 'Grape'] },
            { id: 'prod5', name: 'Daily Multi', sku: 'MULTI-001', variants: ['Standard', "Women's Formula", "Men's Formula"] },
            { id: 'prod6', name: 'Recovery Plus', sku: 'RECV-001', variants: ['Mixed Berry', 'Unflavored'] },
            { id: 'prod7', name: 'Focus Formula', sku: 'FOCUS-001', variants: ['Unflavored'] },
            { id: 'prod8', name: 'Omega-3', sku: 'OMEGA-001', variants: ['Softgels', 'Liquid'] },
            { id: 'prod9', name: 'Creatine', sku: 'CREAT-001', variants: ['Unflavored', 'Fruit Punch'] },
            { id: 'prod10', name: 'Glutamine', sku: 'GLUT-001', variants: ['Unflavored'] }
        ];

        // Category data structure
        const categories = {};

        function switchTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(`${tabName}-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        function addCategory() {
            categoryCount++;
            const categoryId = `cat-${categoryCount}`;

            categories[categoryId] = {
                name: '',
                required: true,
                products: []
            };

            const categoryHtml = `
                <div class="category-item" id="${categoryId}" draggable="true">
                    <div class="category-header">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <span class="move-handle">☰</span>
                            <input type="text" class="form-control" style="width: 300px;" 
                                   placeholder="Category Name (e.g., Protein Powders)" 
                                   onchange="updateCategoryName('${categoryId}', this.value)">
                            <div class="quantity-input-group">
                                <label>Quantity to select:</label>
                                <input type="number" 
                                       min="1" max="10" value="1"
                                       onchange="updateCategoryQuantity('${categoryId}', this.value)"
                                       title="Number of products customer can select from this category">
                            </div>
                        </div>
                        <div class="category-actions">
                            <button class="btn-icon" onclick="toggleCategory('${categoryId}')" title="Expand/Collapse">
                                <span>▼</span>
                            </button>
                            <button class="btn-icon" onclick="removeCategory('${categoryId}')" title="Remove Category">
                                <span>×</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="products-section">
                        <div class="product-search">
                            <button class="btn-primary" onclick="openProductModal('${categoryId}')">
                                + Add Products
                            </button>
                        </div>
                        <div class="selected-products" id="products-${categoryId}">
                            <div style="margin-bottom: 10px; padding: 8px 12px; background: #e3f2fd; border-radius: 5px; border-left: 3px solid #2196f3;">
                                <span style="font-size: 14px; color: #1976d2; font-weight: 500;">
                                    📋 Customer can select <span id="quantity-display-${categoryId}">1</span> product(s) from this category
                                </span>
                            </div>
                            <p style="color: #999;">No products added yet</p>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('categories-container').insertAdjacentHTML('beforeend', categoryHtml);
            setupDragAndDrop();
        }

        function removeCategory(categoryId) {
            if (confirm('Are you sure you want to remove this category?')) {
                document.getElementById(categoryId).remove();
                delete categories[categoryId];
            }
        }

        function updateCategoryName(categoryId, name) {
            categories[categoryId].name = name;
        }

        function updateCategoryQuantity(categoryId, quantity) {
            categories[categoryId].selectionQuantity = parseInt(quantity) || 1;
            // Update the display
            const quantityDisplay = document.getElementById(`quantity-display-${categoryId}`);
            if (quantityDisplay) {
                quantityDisplay.textContent = quantity;
            }
        }

        function openProductModal(categoryId) {
            currentCategoryId = categoryId;
            selectedProducts.clear();
            document.getElementById('product-modal').classList.add('active');
            document.getElementById('product-search-input').value = '';
            searchProducts();
        }

        function closeModal() {
            document.getElementById('product-modal').classList.remove('active');
            currentCategoryId = null;
            selectedProducts.clear();
        }

        function searchProducts() {
            const searchTerm = document.getElementById('product-search-input').value.toLowerCase();
            const resultsContainer = document.getElementById('search-results');

            const filtered = availableProducts.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.sku.toLowerCase().includes(searchTerm)
            );

            resultsContainer.innerHTML = filtered.map(product => `
                <div class="search-result-item">
                    <div class="product-info">
                        <div class="product-details">
                            <input type="checkbox" id="select-${product.id}" 
                                   onchange="toggleAllVariants('${product.id}', this.checked)"
                                   style="margin-right: 8px;">
                            <div style="display: inline-block;">
                                <div class="product-name">${product.name}</div>
                                <div class="product-sku">SKU: ${product.sku}</div>
                            </div>
                        </div>
                    </div>
                    <div class="variant-list">
                        ${product.variants.map((variant, index) => `
                            <div class="variant-item">
                                <input type="checkbox" class="variant-checkbox" 
                                       id="variant-${product.id}-${index}"
                                       data-product="${product.id}" 
                                       data-variant="${variant}"
                                       onchange="updateProductCheckbox('${product.id}')">
                                <label for="variant-${product.id}-${index}">${variant}</label>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        function toggleAllVariants(productId, checked) {
            // Select/deselect all variants for this product
            const product = availableProducts.find(p => p.id === productId);
            product.variants.forEach((variant, index) => {
                const variantCheckbox = document.getElementById(`variant-${productId}-${index}`);
                if (variantCheckbox) {
                    variantCheckbox.checked = checked;
                }
            });
        }

        function updateProductCheckbox(productId) {
            // Update the product checkbox based on variant selections
            const product = availableProducts.find(p => p.id === productId);
            const productCheckbox = document.getElementById(`select-${productId}`);

            let checkedCount = 0;
            product.variants.forEach((variant, index) => {
                const variantCheckbox = document.getElementById(`variant-${productId}-${index}`);
                if (variantCheckbox && variantCheckbox.checked) {
                    checkedCount++;
                }
            });

            // Update product checkbox state
            if (productCheckbox) {
                productCheckbox.checked = checkedCount === product.variants.length;
                productCheckbox.indeterminate = checkedCount > 0 && checkedCount < product.variants.length;
            }
        }

        function addSelectedProducts() {
            if (!currentCategoryId) return;

            const selectedItems = [];

            // Get all checked variants
            document.querySelectorAll('.variant-checkbox:checked').forEach(checkbox => {
                const productId = checkbox.dataset.product;
                const variant = checkbox.dataset.variant;
                const product = availableProducts.find(p => p.id === productId);

                selectedItems.push({
                    productId: productId,
                    productName: product.name,
                    sku: product.sku,
                    variant: variant
                });
            });

            // Update category products
            categories[currentCategoryId].products = [...categories[currentCategoryId].products, ...selectedItems];

            // Update UI
            renderCategoryProducts(currentCategoryId);
            closeModal();
        }

        function renderCategoryProducts(categoryId) {
            const productsContainer = document.getElementById(`products-${categoryId}`);
            const products = categories[categoryId].products;

            if (products.length > 0) {
                productsContainer.innerHTML = `
                    <div style="margin-bottom: 10px; padding: 8px 12px; background: #e3f2fd; border-radius: 5px; border-left: 3px solid #2196f3;">
                        <span style="font-size: 14px; color: #1976d2; font-weight: 500;">
                            📋 Customer can select <span id="quantity-display-${categoryId}">${categories[categoryId].selectionQuantity || 1}</span> product(s) from this category
                        </span>
                    </div>
                    <div style="margin-bottom: 10px; color: #666; font-size: 14px;">
                        <em>First item in the list will be the default selection</em>
                    </div>
                    ${products.map((item, index) => `
                        <div class="product-list-item" draggable="true" data-category="${categoryId}" data-index="${index}">
                            <div class="product-info-section">
                                <span class="move-handle">☰</span>
                                <span>${item.productName} - ${item.variant}</span>
                                <span style="color: #999; font-size: 12px;">SKU: ${item.sku}</span>
                            </div>
                            <div class="product-actions">
                                <button class="btn-icon" onclick="removeProduct('${categoryId}', ${index})" title="Remove Product">
                                    <span>×</span>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                `;

                // Set up drag and drop for products
                setupProductDragAndDrop(categoryId);
            } else {
                productsContainer.innerHTML = '<p style="color: #999;">No products added yet</p>';
            }
        }

        function removeProduct(categoryId, productIndex) {
            categories[categoryId].products.splice(productIndex, 1);
            renderCategoryProducts(categoryId);
        }

        function setupProductDragAndDrop(categoryId) {
            const container = document.getElementById(`products-${categoryId}`);
            const items = container.querySelectorAll('.product-list-item');

            items.forEach(item => {
                item.addEventListener('dragstart', handleProductDragStart);
                item.addEventListener('dragend', handleProductDragEnd);
                item.addEventListener('dragover', handleProductDragOver);
                item.addEventListener('drop', handleProductDrop);
            });
        }

        let draggedProduct = null;
        let draggedProductCategory = null;
        let draggedProductIndex = null;

        function handleProductDragStart(e) {
            draggedProduct = this;
            draggedProductCategory = this.dataset.category;
            draggedProductIndex = parseInt(this.dataset.index);
            this.classList.add('dragging');
        }

        function handleProductDragEnd(e) {
            this.classList.remove('dragging');
        }

        function handleProductDragOver(e) {
            e.preventDefault();
            const container = e.currentTarget.parentElement;
            const afterElement = getDragAfterElementProduct(container, e.clientY);

            if (afterElement == null) {
                container.appendChild(draggedProduct);
            } else {
                container.insertBefore(draggedProduct, afterElement);
            }
        }

        function handleProductDrop(e) {
            e.preventDefault();
            e.stopPropagation();

            // Get new order from DOM
            const container = document.getElementById(`products-${draggedProductCategory}`);
            const newOrder = Array.from(container.querySelectorAll('.product-list-item')).map((item, index) => {
                return parseInt(item.dataset.index);
            });

            // Reorder the products array based on new order
            const reorderedProducts = newOrder.map(oldIndex => categories[draggedProductCategory].products[oldIndex]);
            categories[draggedProductCategory].products = reorderedProducts;

            // Re-render to update indices
            renderCategoryProducts(draggedProductCategory);
        }

        function getDragAfterElementProduct(container, y) {
            const draggableElements = [...container.querySelectorAll('.product-list-item:not(.dragging)')];

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;

                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        function setupDragAndDrop() {
            const items = document.querySelectorAll('.category-item');

            items.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);
                item.addEventListener('dragover', handleDragOver);
                item.addEventListener('drop', handleDrop);
            });
        }

        let draggedElement = null;

        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            const afterElement = getDragAfterElement(e.currentTarget.parentElement, e.clientY);
            if (afterElement == null) {
                e.currentTarget.parentElement.appendChild(draggedElement);
            } else {
                e.currentTarget.parentElement.insertBefore(draggedElement, afterElement);
            }
        }

        function handleDrop(e) {
            e.preventDefault();
        }

        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.category-item:not(.dragging)')];

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;

                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        function saveKit() {
            const bundleData = {
                name: document.getElementById('kit-name').value,
                sku: document.getElementById('kit-sku').value,
                description: document.getElementById('kit-description').value,
                categories: Object.entries(categories).map(([id, data]) => ({
                    id: id,
                    name: data.name,
                    selectionQuantity: data.selectionQuantity,
                    products: data.products,
                    order: Array.from(document.querySelectorAll('.category-item')).findIndex(el => el.id === id)
                }))
            };

            console.log('Saving bundle configuration:', bundleData);
            alert('Bundle configuration saved! Check console for data structure.');

            // In real implementation, this would send to API
            // Then redirect back to bundle list
            switchTab('kits');
        }

        function editKit(sku) {
            document.getElementById('editor-title').textContent = 'Edit Dynamic Bundle';
            switchTab('create');

            // In real implementation, would load bundle data from API
            if (sku === 'TRANS-BUNDLE-001') {
                document.getElementById('kit-name').value = 'Transformation Bundle';
                document.getElementById('kit-sku').value = 'TRANS-BUNDLE-001';
                document.getElementById('kit-description').value = 'Our flagship transformation bundle with customizable options.';

                // Add some sample categories with products
                for (let i = 0; i < 3; i++) {
                    addCategory();
                    const categoryId = `cat-${i + 1}`;

                    // Add some sample products to show ordering functionality
                    if (i === 0) {
                        categories[categoryId].name = 'Protein Powders';
                        categories[categoryId].selectionQuantity = 2;
                        categories[categoryId].products = [
                            { productId: 'prod1', productName: 'Whey Protein', sku: 'WHEY-001', variant: 'Vanilla' },
                            { productId: 'prod1', productName: 'Whey Protein', sku: 'WHEY-001', variant: 'Chocolate' },
                            { productId: 'prod2', productName: 'Plant Protein', sku: 'PLANT-001', variant: 'Berry' }
                        ];
                        document.querySelector(`#${categoryId} input[type="text"]`).value = 'Protein Powders';
                        document.querySelector(`#${categoryId} input[type="number"]`).value = '2';
                        renderCategoryProducts(categoryId);
                    } else if (i === 1) {
                        categories[categoryId].name = 'Energy Supplements';
                        categories[categoryId].selectionQuantity = 1;
                        categories[categoryId].products = [
                            { productId: 'prod3', productName: 'Energy Boost', sku: 'ENRG-001', variant: 'Citrus' },
                            { productId: 'prod3', productName: 'Energy Boost', sku: 'ENRG-001', variant: 'Berry Blast' }
                        ];
                        document.querySelector(`#${categoryId} input[type="text"]`).value = 'Energy Supplements';
                        document.querySelector(`#${categoryId} input[type="number"]`).value = '1';
                        renderCategoryProducts(categoryId);
                    } else if (i === 2) {
                        categories[categoryId].name = 'Recovery Products';
                        categories[categoryId].selectionQuantity = 3;
                        categories[categoryId].products = [
                            { productId: 'prod6', productName: 'Recovery Plus', sku: 'RECV-001', variant: 'Mixed Berry' },
                            { productId: 'prod7', productName: 'Focus Formula', sku: 'FOCUS-001', variant: 'Unflavored' },
                            { productId: 'prod8', productName: 'Omega-3', sku: 'OMEGA-001', variant: 'Softgels' }
                        ];
                        document.querySelector(`#${categoryId} input[type="text"]`).value = 'Recovery Products';
                        document.querySelector(`#${categoryId} input[type="number"]`).value = '3';
                        renderCategoryProducts(categoryId);
                    }
                }
            }
        }

        function openFluidProduct(sku) {
            // In real implementation, this would open the Fluid product management page
            // For now, we'll just show an alert
            console.log(`Opening Fluid product page for SKU: ${sku}`);
            alert(`This would open the Fluid product management page for:\n\nSKU: ${sku}\n\nWhere you can manage:\n- Pricing\n- Tax settings\n- Inventory\n- Other product settings`);

            // Example of what the real implementation might look like:
            // window.open(`/fluid/products/${sku}/edit`, '_blank');
        }

        // Add modal close on outside click
        window.onclick = function (event) {
            const modal = document.getElementById('product-modal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>

</html>