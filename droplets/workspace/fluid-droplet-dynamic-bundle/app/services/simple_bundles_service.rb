# frozen_string_literal: true

# Simple bundles service that bypasses the Setting model issues
class SimpleBundlesService
  def self.call(action:, **args)
    new(**args).call(action)
  end

  def initialize(company: nil, page: 1, per_page: 20)
    @company = company
    @page = page
    @per_page = per_page
  end

  def call(action)
    case action
    when :list
      list_bundles
    else
      { success: false, error: "Invalid action: #{action}" }
    end
  rescue => e
    Rails.logger.error("SimpleBundlesService error: #{e.class} - #{e.message}")
    Rails.logger.error("Backtrace: #{e.backtrace.first(5).join("\n")}")
    { success: false, error: "Failed to #{action} bundles: #{e.message}" }
  end

  private

  def list_bundles
    Rails.logger.info("SimpleBundlesService - Listing bundles - page: #{@page}, per_page: #{@per_page}")
    
    # Create FluidClient
    client = FluidClient.new(@company&.authentication_token || ENV["FLUID_API_TOKEN"])
    
    # Make request
    response = client.get("/api/company/v1/bundles", query: { page: @page, per_page: @per_page })
    
    # Return success response
    {
      success: true,
      data: {
        bundles: response.parsed_response || [],
        pagination: {
          page: @page,
          per_page: @per_page,
          total: 0
        }
      }
    }
  end
end
