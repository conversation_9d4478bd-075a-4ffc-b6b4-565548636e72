# frozen_string_literal: true

class FluidClient
  include HTTParty

  attr_reader :authentication_token, :base_url

  def initialize(authentication_token = nil, base_url: nil)
    Rails.logger.info("FluidClient.initialize - Starting initialization...")

    @authentication_token = authentication_token || default_authentication_token
    Rails.logger.info("FluidClient.initialize - Token: #{@authentication_token ? @authentication_token[0..10] + '...' : 'nil'}")

    @base_url = base_url || default_base_url
    Rails.logger.info("FluidClient.initialize - Base URL: #{@base_url}")

    Rails.logger.info("FluidClient.initialize - Setting base_uri...")
    self.class.base_uri(@base_url)

    headers = { "Content-Type" => "application/json" }
    headers["Authorization"] = "Bearer #{@authentication_token}" if @authentication_token
    Rails.logger.info("FluidClient.initialize - Headers: #{headers.inspect}")

    Rails.logger.info("FluidClient.initialize - Setting headers...")
    self.class.headers(headers)

    Rails.logger.info("FluidClient.initialize - Initialization complete!")
  end

  def get(path, options = {})
    Rails.logger.info("FluidClient.get - path: #{path}, options: #{options.inspect}")
    Rails.logger.info("FluidClient.get - base_uri: #{self.class.base_uri}")
    Rails.logger.info("FluidClient.get - headers: #{self.class.headers.inspect}")

    handle_response do
      Rails.logger.info("FluidClient.get - Making HTTParty request...")
      response = self.class.get(path, options)
      Rails.logger.info("FluidClient.get - HTTParty response received: #{response.code}")
      response
    end
  end

  def post(path, options = {})
    handle_response do
      self.class.post(path, options)
    end
  end

  def put(path, options = {})
    handle_response do
      self.class.put(path, options)
    end
  end

  def delete(path, options = {})
    handle_response do
      self.class.delete(path, options)
    end
  end

  private

  def default_authentication_token
    ENV["FLUID_API_TOKEN"]
  end

  def default_base_url
    # Try FLUID_API_URL first (used by Exigo droplet)
    url = ENV["FLUID_API_URL"]

    # Fallback to constructed URL from subdomain (Avalara pattern)
    if url.blank? && ENV["FLUID_COMPANY_SUBDOMAIN"].present?
      url = "https://#{ENV['FLUID_COMPANY_SUBDOMAIN']}.fluid.app"
    end

    # Final fallback to localhost for development
    url.presence || "http://localhost:3000"
  end

  def handle_response
    response = yield
    
    if response.success?
      response
    else
      Rails.logger.error("FluidClient error: #{response.code} - #{response.message}")
      Rails.logger.error("Response body: #{response.body}")
      raise StandardError, "Request failed: #{response.code} - #{response.message}"
    end
  rescue => e
    Rails.logger.error("FluidClient exception: #{e.message}")
    raise e
  end
end
