# frozen_string_literal: true

module Fluid
  # Service for interacting with Fluid Categories API
  # Handles category management for organizing products in dynamic bundles
  #
  # @example List categories
  #   result = CategoriesService.call(action: :list)
  #   if result.success?
  #     categories = result.data[:categories]
  #   end
  #
  # @example Create category
  #   result = CategoriesService.call(
  #     action: :create,
  #     name: "Protein Powders",
  #     description: "High-quality protein supplements"
  #   )
  #
  # @example Find category
  #   result = CategoriesService.call(action: :find, category_id: "cat-123")
  #
  class CategoriesService < BaseService
    # Initialize the service with action and parameters
    # @param action [Symbol] the action to perform (:list, :find, :create, :update, :delete)
    # @param category_id [String] category ID (for :find, :update, :delete actions)
    # @param name [String] category name (for :create, :update actions)
    # @param description [String] category description (for :create, :update actions)
    # @param parent_id [String] parent category ID for hierarchical categories
    # @param page [Integer] page number for pagination (default: 1)
    # @param per_page [Integer] items per page (default: 50)
    def initialize(action:, category_id: nil, name: nil, description: nil, parent_id: nil, page: 1, per_page: 50)
      @action = action.to_sym
      @category_id = category_id
      @name = name
      @description = description
      @parent_id = parent_id
      @page = page
      @per_page = per_page
    end

    # Execute the categories service action
    # @return [ServiceResult] the result of the operation
    def call
      case @action
      when :list
        list_categories
      when :find
        find_category
      when :create
        create_category
      when :update
        update_category
      when :delete
        delete_category
      else
        failure("Invalid action: #{@action}. Supported actions: :list, :find, :create, :update, :delete")
      end
    end

    private

    # List all categories with pagination
    # @return [ServiceResult] categories list
    def list_categories
      Rails.logger.info("Listing categories - page: #{@page}, per_page: #{@per_page}")
      
      result = get("/api/company/v1/categories", params: list_params)
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_categories_response(result.data)
      success(transformed_data)
    end

    # Find a specific category by ID
    # @return [ServiceResult] category details
    def find_category
      return failure("Category ID is required") if @category_id.blank?

      Rails.logger.info("Finding category with ID: #{@category_id}")
      
      result = get("/api/company/v1/categories/#{@category_id}")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_category_response(result.data)
      success(transformed_data)
    end

    # Create a new category
    # @return [ServiceResult] created category
    def create_category
      return failure("Category name is required") if @name.blank?

      Rails.logger.info("Creating category: #{@name}")
      
      result = post("/api/company/v1/categories", body: create_params)
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_category_response(result.data)
      success(transformed_data)
    end

    # Update an existing category
    # @return [ServiceResult] updated category
    def update_category
      return failure("Category ID is required") if @category_id.blank?
      return failure("Category name is required") if @name.blank?

      Rails.logger.info("Updating category ID: #{@category_id}")
      
      result = put("/api/company/v1/categories/#{@category_id}", body: update_params)
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_category_response(result.data)
      success(transformed_data)
    end

    # Delete a category
    # @return [ServiceResult] deletion result
    def delete_category
      return failure("Category ID is required") if @category_id.blank?

      Rails.logger.info("Deleting category ID: #{@category_id}")
      
      result = delete("/api/company/v1/categories/#{@category_id}")
      
      return result if result.failure?
      
      success({ deleted: true, category_id: @category_id })
    end

    # Build list parameters
    # @return [Hash] list parameters
    def list_params
      params = {
        page: @page,
        per_page: @per_page,
        include_products_count: true
      }
      
      params[:parent_id] = @parent_id if @parent_id.present?
      params
    end

    # Build create parameters
    # @return [Hash] create parameters
    def create_params
      params = {
        name: @name,
        description: @description
      }
      
      params[:parent_id] = @parent_id if @parent_id.present?
      params
    end

    # Build update parameters
    # @return [Hash] update parameters
    def update_params
      params = {
        name: @name
      }
      
      params[:description] = @description if @description.present?
      params[:parent_id] = @parent_id if @parent_id.present?
      params
    end

    # Transform categories API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_categories_response(response_data)
      {
        categories: response_data.dig("data") || [],
        pagination: {
          current_page: response_data.dig("meta", "current_page") || @page,
          per_page: response_data.dig("meta", "per_page") || @per_page,
          total_pages: response_data.dig("meta", "total_pages") || 1,
          total_count: response_data.dig("meta", "total_count") || 0
        }
      }
    end

    # Transform single category API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_category_response(response_data)
      {
        category: response_data.dig("data") || response_data
      }
    end
  end
end
