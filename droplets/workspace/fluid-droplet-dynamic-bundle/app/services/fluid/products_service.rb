# frozen_string_literal: true

module Fluid
  # Service for interacting with Fluid Products API
  # Handles product search, retrieval, and variant management for dynamic bundles
  #
  # @example Search products
  #   result = ProductsService.call(action: :search, query: "protein powder")
  #   if result.success?
  #     products = result.data[:products]
  #   end
  #
  # @example Get product details
  #   result = ProductsService.call(action: :find, product_id: "12345")
  #
  # @example Get product variants
  #   result = ProductsService.call(action: :variants, product_id: "12345")
  #
  class ProductsService < BaseService
    # Initialize the service with action and parameters
    # @param action [Symbol] the action to perform (:search, :find, :variants, :list)
    # @param query [String] search query for products (for :search action)
    # @param product_id [String] product ID (for :find, :variants actions)
    # @param page [Integer] page number for pagination (default: 1)
    # @param per_page [Integer] items per page (default: 20)
    def initialize(action:, query: nil, product_id: nil, page: 1, per_page: 20)
      @action = action.to_sym
      @query = query
      @product_id = product_id
      @page = page
      @per_page = per_page
    end

    # Execute the products service action
    # @return [ServiceResult] the result of the operation
    def call
      case @action
      when :search
        search_products
      when :find
        find_product
      when :variants
        get_product_variants
      when :list
        list_products
      else
        failure("Invalid action: #{@action}. Supported actions: :search, :find, :variants, :list")
      end
    end

    private

    # Search for products by query
    # @return [ServiceResult] search results
    def search_products
      return failure("Query is required for search") if @query.blank?

      Rails.logger.info("Searching products with query: #{@query}")
      
      result = get("/api/company/v1/products", params: search_params)
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_products_response(result.data)
      success(transformed_data)
    end

    # Find a specific product by ID
    # @return [ServiceResult] product details
    def find_product
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("Finding product with ID: #{@product_id}")
      
      result = get("/api/company/v1/products/#{@product_id}")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_product_response(result.data)
      success(transformed_data)
    end

    # Get variants for a specific product
    # @return [ServiceResult] product variants
    def get_product_variants
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("Getting variants for product ID: #{@product_id}")
      
      result = get("/api/company/v1/products/#{@product_id}/variants")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_variants_response(result.data)
      success(transformed_data)
    end

    # List all products with pagination
    # @return [ServiceResult] paginated products list
    def list_products
      Rails.logger.info("Listing products - page: #{@page}, per_page: #{@per_page}")
      
      result = get("/api/company/v1/products", params: list_params)
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_products_response(result.data)
      success(transformed_data)
    end

    # Build search parameters
    # @return [Hash] search parameters
    def search_params
      {
        q: @query,
        page: @page,
        per_page: @per_page,
        include_variants: true,
        status: "active"
      }
    end

    # Build list parameters
    # @return [Hash] list parameters
    def list_params
      {
        page: @page,
        per_page: @per_page,
        include_variants: true,
        status: "active"
      }
    end

    # Transform products API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_products_response(response_data)
      {
        products: response_data.dig("data") || [],
        pagination: {
          current_page: response_data.dig("meta", "current_page") || @page,
          per_page: response_data.dig("meta", "per_page") || @per_page,
          total_pages: response_data.dig("meta", "total_pages") || 1,
          total_count: response_data.dig("meta", "total_count") || 0
        }
      }
    end

    # Transform single product API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_product_response(response_data)
      {
        product: response_data.dig("data") || response_data
      }
    end

    # Transform variants API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_variants_response(response_data)
      {
        variants: response_data.dig("data") || [],
        product_id: @product_id
      }
    end
  end
end
