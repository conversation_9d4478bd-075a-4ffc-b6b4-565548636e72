# frozen_string_literal: true

module Fluid
  # Service for managing metadata export to Fluid products
  # Handles JSON metadata storage for dynamic bundle configuration
  #
  # @example Export bundle metadata
  #   bundle_config = {
  #     sku: "TRANS-BUNDLE-001",
  #     bundleName: "Transformation Bundle",
  #     categories: [...]
  #   }
  #   result = MetadataService.call(
  #     action: :export,
  #     product_id: "12345",
  #     metadata: bundle_config
  #   )
  #
  # @example Get bundle metadata
  #   result = MetadataService.call(
  #     action: :get,
  #     product_id: "12345"
  #   )
  #
  class MetadataService < BaseService
    # Metadata key for dynamic bundle configuration
    DYNAMIC_BUNDLE_KEY = "dynamic_bundle_config"

    # Initialize the service with action and parameters
    # @param action [Symbol] the action to perform (:export, :get, :update, :delete)
    # @param product_id [String] Fluid product ID to attach metadata to
    # @param metadata [Hash] metadata to export (for :export, :update actions)
    # @param validate [<PERSON><PERSON><PERSON>] whether to validate metadata structure (default: true)
    def initialize(action:, product_id:, metadata: {}, validate: true)
      @action = action.to_sym
      @product_id = product_id
      @metadata = metadata
      @validate = validate
    end

    # Execute the metadata service action
    # @return [ServiceResult] the result of the operation
    def call
      case @action
      when :export
        export_metadata
      when :get
        get_metadata
      when :update
        update_metadata
      when :delete
        delete_metadata
      else
        failure("Invalid action: #{@action}. Supported actions: :export, :get, :update, :delete")
      end
    end

    private

    # Export metadata to Fluid product
    # @return [ServiceResult] export result
    def export_metadata
      return failure("Product ID is required") if @product_id.blank?
      return failure("Metadata is required") if @metadata.blank?

      # Validate metadata structure if requested
      if @validate
        validation_result = validate_metadata_structure
        return validation_result if validation_result.failure?
      end

      Rails.logger.info("Exporting metadata to product ID: #{@product_id}")
      
      result = post("/api/company/v1/products/#{@product_id}/metadata", body: export_params)
      
      return result if result.failure?
      
      success({
        exported: true,
        product_id: @product_id,
        metadata_key: DYNAMIC_BUNDLE_KEY,
        metadata: @metadata
      })
    end

    # Get metadata from Fluid product
    # @return [ServiceResult] metadata result
    def get_metadata
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("Getting metadata from product ID: #{@product_id}")
      
      result = get("/api/company/v1/products/#{@product_id}/metadata/#{DYNAMIC_BUNDLE_KEY}")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_metadata_response(result.data)
      success(transformed_data)
    end

    # Update existing metadata
    # @return [ServiceResult] update result
    def update_metadata
      return failure("Product ID is required") if @product_id.blank?
      return failure("Metadata is required") if @metadata.blank?

      # Validate metadata structure if requested
      if @validate
        validation_result = validate_metadata_structure
        return validation_result if validation_result.failure?
      end

      Rails.logger.info("Updating metadata for product ID: #{@product_id}")
      
      result = put("/api/company/v1/products/#{@product_id}/metadata/#{DYNAMIC_BUNDLE_KEY}", body: update_params)
      
      return result if result.failure?
      
      success({
        updated: true,
        product_id: @product_id,
        metadata_key: DYNAMIC_BUNDLE_KEY,
        metadata: @metadata
      })
    end

    # Delete metadata from Fluid product
    # @return [ServiceResult] deletion result
    def delete_metadata
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("Deleting metadata from product ID: #{@product_id}")
      
      result = delete("/api/company/v1/products/#{@product_id}/metadata/#{DYNAMIC_BUNDLE_KEY}")
      
      return result if result.failure?
      
      success({
        deleted: true,
        product_id: @product_id,
        metadata_key: DYNAMIC_BUNDLE_KEY
      })
    end

    # Build export parameters
    # @return [Hash] export parameters
    def export_params
      {
        key: DYNAMIC_BUNDLE_KEY,
        value: @metadata,
        type: "json"
      }
    end

    # Build update parameters
    # @return [Hash] update parameters
    def update_params
      {
        value: @metadata,
        type: "json"
      }
    end

    # Transform metadata API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_metadata_response(response_data)
      {
        product_id: @product_id,
        metadata_key: DYNAMIC_BUNDLE_KEY,
        metadata: response_data.dig("data", "value") || response_data.dig("value") || {}
      }
    end

    # Validate metadata structure against expected schema
    # @return [ServiceResult] validation result
    def validate_metadata_structure
      required_fields = %w[sku bundleName categories]
      
      missing_fields = required_fields.select { |field| @metadata[field].blank? && @metadata[field.to_sym].blank? }
      
      if missing_fields.any?
        return failure("Missing required metadata fields: #{missing_fields.join(', ')}")
      end

      # Validate categories structure
      categories = @metadata[:categories] || @metadata["categories"]
      if categories.blank? || !categories.is_a?(Array)
        return failure("Categories must be a non-empty array")
      end

      # Validate each category structure
      categories.each_with_index do |category, index|
        category_validation = validate_category_structure(category, index)
        return category_validation if category_validation.failure?
      end

      success({ valid: true })
    end

    # Validate individual category structure
    # @param category [Hash] category data
    # @param index [Integer] category index for error reporting
    # @return [ServiceResult] validation result
    def validate_category_structure(category, index)
      required_category_fields = %w[categoryId categoryName displayOrder selectionQuantity products]
      
      missing_fields = required_category_fields.select do |field|
        category[field].blank? && category[field.to_sym].blank?
      end
      
      if missing_fields.any?
        return failure("Category #{index}: Missing required fields: #{missing_fields.join(', ')}")
      end

      # Validate products array
      products = category[:products] || category["products"]
      if products.blank? || !products.is_a?(Array)
        return failure("Category #{index}: Products must be a non-empty array")
      end

      success({ valid: true })
    end
  end
end
