# frozen_string_literal: true

# Base class for all service objects in the Dynamic Bundle Droplet
# Provides a consistent interface and syntactic sugar for service calls
#
# @example Basic usage
#   class MyService < ApplicationService
#     def initialize(param1, param2:)
#       @param1 = param1
#       @param2 = param2
#     end
#
#     def call
#       # Service logic here
#       ServiceResult.success(result_data)
#     rescue => e
#       ServiceResult.failure(e.message)
#     end
#   end
#
#   # Usage with syntactic sugar
#   result = MyService.call("value", param2: "another_value")
#
class ApplicationService
  # Syntactic sugar for service instantiation and execution
  # @param args [Array] positional arguments for service initialization
  # @param kwargs [Hash] keyword arguments for service initialization
  # @return [ServiceResult] the result of the service operation
  def self.call(*args, **kwargs)
    new(*args, **kwargs).call
  end

  # Abstract method that must be implemented by subclasses
  # @return [ServiceResult] the result of the service operation
  # @raise [NotImplementedError] if not implemented by subclass
  def call
    raise NotImplementedError, "#{self.class} must implement #call method"
  end

  private

  # Helper method to create successful results
  # @param data [Object] the success data
  # @return [ServiceResult] successful result
  def success(data = nil)
    ServiceResult.success(data)
  end

  # Helper method to create failure results
  # @param error [String, Exception, Hash] the error
  # @return [ServiceResult] failure result
  def failure(error)
    ServiceResult.failure(error)
  end

  # Helper method to handle exceptions and convert to ServiceResult
  # @yield the block to execute
  # @return [ServiceResult] success or failure result
  def with_error_handling
    yield
  rescue => e
    Rails.logger.error("#{self.class} error: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n")) if Rails.env.development?
    failure(e.message)
  end
end
