# frozen_string_literal: true

# Service for generating and exporting bundle configuration as JSON metadata
# Transforms bundle data into Fluid-compatible JSON format for frontend consumption
class BundleMetadataExportService < ApplicationService
  def initialize(bundle_id)
    @bundle_id = bundle_id
    @bundle_data = nil
    @categories_data = []
    @validation_errors = []
  end

  def call
    with_error_handling do
      load_bundle_data
      validate_bundle_configuration
      
      return failure("Bundle configuration is invalid: #{@validation_errors.join(', ')}") if @validation_errors.any?
      
      metadata = generate_metadata_json
      export_result = export_to_fluid(metadata)
      
      if export_result.success?
        success({
          bundle_id: @bundle_id,
          metadata: metadata,
          export_status: 'success',
          exported_at: Time.current.iso8601,
          validation_errors: []
        })
      else
        failure("Export failed: #{export_result.error}")
      end
    end
  end

  private

  # Load bundle and categories data
  def load_bundle_data
    # Check if API token is configured
    if ENV['FLUID_API_TOKEN'].blank?
      raise StandardError, "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
    end

    # Load from Fluid API
    result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
    if result.success?
      @bundle_data = result.data[:bundle]
      @categories_data = @bundle_data.dig('metadata', 'categories') || []
    else
      raise StandardError, "Failed to load bundle data: #{result.error}"
    end
  end

  # Validate bundle configuration before export
  def validate_bundle_configuration
    @validation_errors = []

    # Validate bundle basic info
    @validation_errors << "Bundle name is required" if @bundle_data['name'].blank?
    @validation_errors << "Bundle SKU is required" if @bundle_data['sku'].blank?

    # Validate categories
    if @categories_data.empty?
      @validation_errors << "At least one category is required"
    else
      @categories_data.each_with_index do |category, index|
        validate_category(category, index)
      end
    end

    # Validate overall bundle logic
    validate_bundle_logic
  end

  # Validate individual category
  def validate_category(category, index)
    category_name = category['categoryName'] || "Category #{index + 1}"
    
    @validation_errors << "#{category_name}: Category name is required" if category['categoryName'].blank?
    @validation_errors << "#{category_name}: Selection quantity must be greater than 0" if (category['selectionQuantity'] || 0) <= 0
    
    products = category['products'] || []
    if products.empty?
      @validation_errors << "#{category_name}: At least one product must be assigned"
    else
      # Check if selection quantity is reasonable
      selection_qty = category['selectionQuantity'] || 1
      if products.length < selection_qty
        @validation_errors << "#{category_name}: Not enough products (#{products.length}) for required selection (#{selection_qty})"
      end
      
      # Validate products
      products.each_with_index do |product, prod_index|
        validate_product(product, category_name, prod_index)
      end
      
      # Check for default product
      default_products = products.select { |p| p['isDefault'] }
      if default_products.empty?
        @validation_errors << "#{category_name}: At least one product should be marked as default"
      elsif default_products.length > selection_qty
        @validation_errors << "#{category_name}: Too many default products (#{default_products.length}) for selection quantity (#{selection_qty})"
      end
    end
  end

  # Validate individual product
  def validate_product(product, category_name, index)
    product_name = product['variantTitle'] || "Product #{index + 1}"
    
    @validation_errors << "#{category_name} - #{product_name}: Product ID is required" if product['productId'].blank?
    @validation_errors << "#{category_name} - #{product_name}: Variant ID is required" if product['variantId'].blank?
    @validation_errors << "#{category_name} - #{product_name}: Variant title is required" if product['variantTitle'].blank?
    @validation_errors << "#{category_name} - #{product_name}: Variant SKU is required" if product['variantSku'].blank?
  end

  # Validate overall bundle logic
  def validate_bundle_logic
    # Check for duplicate category names
    category_names = @categories_data.map { |cat| cat['categoryName'] }.compact
    duplicates = category_names.group_by(&:itself).select { |_, v| v.size > 1 }.keys
    duplicates.each do |name|
      @validation_errors << "Duplicate category name: #{name}"
    end

    # Check display order consistency
    display_orders = @categories_data.map { |cat| cat['displayOrder'] }.compact.sort
    expected_orders = (0...display_orders.length).to_a
    unless display_orders == expected_orders
      @validation_errors << "Category display orders are not sequential (expected: #{expected_orders}, got: #{display_orders})"
    end
  end

  # Generate the metadata JSON structure
  def generate_metadata_json
    {
      sku: @bundle_data['sku'],
      fluidProductId: @bundle_data['id'],
      bundleName: @bundle_data['name'],
      description: @bundle_data['description'] || '',
      status: @bundle_data['status'] || 'active',
      exportedAt: Time.current.iso8601,
      version: '1.0',
      categories: transform_categories_for_export
    }
  end

  # Transform categories data for export
  def transform_categories_for_export
    @categories_data.sort_by { |cat| cat['displayOrder'] || 0 }.map do |category|
      {
        categoryId: category['categoryId'],
        categoryName: category['categoryName'],
        displayOrder: category['displayOrder'] || 0,
        selectionQuantity: category['selectionQuantity'] || 1,
        products: transform_products_for_export(category['products'] || [])
      }
    end
  end

  # Transform products data for export
  def transform_products_for_export(products)
    products.sort_by { |prod| prod['displayOrder'] || 0 }.map do |product|
      {
        productId: product['productId'],
        variantId: product['variantId'],
        variantTitle: product['variantTitle'],
        variantSku: product['variantSku'],
        displayOrder: product['displayOrder'] || 0,
        isDefault: product['isDefault'] || false,
        price: product['price']
      }.compact
    end
  end

  # Export metadata to Fluid product
  def export_to_fluid(metadata)
    # Use Fluid MetadataService to export
    Fluid::MetadataService.call(
      action: :export,
      product_id: @bundle_data['id'],
      metadata: metadata
    )
  end

end
    {
      'id' => @bundle_id,
      'name' => 'Transformation Bundle',
      'sku' => 'TRANS-BUNDLE-001',
      'description' => 'Complete wellness transformation package with customizable products',
      'status' => 'active'
    }
  end

  # Mock categories data for demo
  def mock_categories_data
    [
      {
        'categoryId' => 'cat-1',
        'categoryName' => 'Protein Powders',
        'displayOrder' => 0,
        'selectionQuantity' => 2,
        'products' => [
          {
            'productId' => 'prod1',
            'variantId' => 'var1',
            'variantTitle' => 'Whey Protein - Vanilla',
            'variantSku' => 'WHEY-VAN-001',
            'displayOrder' => 0,
            'isDefault' => true,
            'price' => 49.99
          },
          {
            'productId' => 'prod2',
            'variantId' => 'var2',
            'variantTitle' => 'Whey Protein - Chocolate',
            'variantSku' => 'WHEY-CHO-001',
            'displayOrder' => 1,
            'isDefault' => false,
            'price' => 49.99
          },
          {
            'productId' => 'prod3',
            'variantId' => 'var3',
            'variantTitle' => 'Plant Protein - Berry',
            'variantSku' => 'PLANT-BER-001',
            'displayOrder' => 2,
            'isDefault' => false,
            'price' => 54.99
          }
        ]
      },
      {
        'categoryId' => 'cat-2',
        'categoryName' => 'Pre-Workout',
        'displayOrder' => 1,
        'selectionQuantity' => 1,
        'products' => [
          {
            'productId' => 'prod4',
            'variantId' => 'var4',
            'variantTitle' => 'Energy Boost - Citrus',
            'variantSku' => 'ENERGY-CIT-001',
            'displayOrder' => 0,
            'isDefault' => true,
            'price' => 34.99
          },
          {
            'productId' => 'prod5',
            'variantId' => 'var5',
            'variantTitle' => 'Energy Boost - Berry',
            'variantSku' => 'ENERGY-BER-001',
            'displayOrder' => 1,
            'isDefault' => false,
            'price' => 34.99
          }
        ]
      },
      {
        'categoryId' => 'cat-3',
        'categoryName' => 'Recovery Supplements',
        'displayOrder' => 2,
        'selectionQuantity' => 1,
        'products' => [
          {
            'productId' => 'prod6',
            'variantId' => 'var6',
            'variantTitle' => 'BCAA - Tropical',
            'variantSku' => 'BCAA-TROP-001',
            'displayOrder' => 0,
            'isDefault' => true,
            'price' => 39.99
          }
        ]
      }
    ]
  end
end
