# frozen_string_literal: true

# Service for managing bundle metadata operations
# Handles CRUD operations on bundle categories and products stored in metadata
class BundleMetadataService < ApplicationService
  def initialize(bundle_id:, action:, company: nil, **params)
    @bundle_id = bundle_id
    @action = action.to_sym
    @params = params
    @bundle_data = nil
    @company = company
  end

  def call
    with_error_handling do
      load_bundle_data
      
      case @action
      when :add_category
        add_category
      when :update_category
        update_category
      when :delete_category
        delete_category
      when :move_category_up
        move_category_up
      when :move_category_down
        move_category_down
      when :assign_product
        assign_product
      when :unassign_product
        unassign_product
      when :set_default_product
        set_default_product
      when :move_product_up
        move_product_up
      when :move_product_down
        move_product_down
      else
        failure("Unknown action: #{@action}")
      end
    end
  end

  private

  # Load bundle data from Fluid API
  def load_bundle_data
    if ENV['FLUID_API_TOKEN'].blank?
      raise StandardError, "Fluid API token not configured"
    end

    result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
    if result.success?
      @bundle_data = result.data[:bundle]
    else
      raise StandardError, "Failed to load bundle: #{result.error}"
    end
  end

  # Add a new category to bundle metadata
  def add_category
    category_data = @params[:category_data]
    return failure("Category data is required") if category_data.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Add the new category
    categories << category_data
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        category: category_data,
        bundle_id: @bundle_id,
        categories_count: categories.length
      })
    else
      failure("Failed to add category: #{update_result.error}")
    end
  end

  # Update an existing category
  def update_category
    category_id = @params[:category_id]
    category_data = @params[:category_data]
    
    return failure("Category ID is required") if category_id.blank?
    return failure("Category data is required") if category_data.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find and update the category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    categories[category_index] = category_data
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        category: category_data,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to update category: #{update_result.error}")
    end
  end

  # Delete a category
  def delete_category
    category_id = @params[:category_id]
    return failure("Category ID is required") if category_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find and remove the category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    removed_category = categories.delete_at(category_index)
    
    # Reorder remaining categories
    categories.each_with_index do |cat, index|
      cat['displayOrder'] = index
    end
    
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        deleted_category: removed_category,
        bundle_id: @bundle_id,
        categories_count: categories.length
      })
    else
      failure("Failed to delete category: #{update_result.error}")
    end
  end

  # Move category up in display order
  def move_category_up
    category_id = @params[:category_id]
    return failure("Category ID is required") if category_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?
    return failure("Category is already first") if category_index == 0

    # Swap with previous category
    categories[category_index], categories[category_index - 1] = categories[category_index - 1], categories[category_index]
    
    # Update display orders
    categories.each_with_index do |cat, index|
      cat['displayOrder'] = index
    end
    
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        category_id: category_id,
        new_position: category_index - 1,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to move category: #{update_result.error}")
    end
  end

  # Move category down in display order
  def move_category_down
    category_id = @params[:category_id]
    return failure("Category ID is required") if category_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?
    return failure("Category is already last") if category_index == categories.length - 1

    # Swap with next category
    categories[category_index], categories[category_index + 1] = categories[category_index + 1], categories[category_index]
    
    # Update display orders
    categories.each_with_index do |cat, index|
      cat['displayOrder'] = index
    end
    
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        category_id: category_id,
        new_position: category_index + 1,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to move category: #{update_result.error}")
    end
  end

  # Assign product to category
  def assign_product
    category_id = @params[:category_id]
    product_data = @params[:product_data]
    
    return failure("Category ID is required") if category_id.blank?
    return failure("Product data is required") if product_data.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    # Add product to category
    category = categories[category_index]
    category['products'] ||= []
    
    # Set display order
    product_data['displayOrder'] = category['products'].length
    
    category['products'] << product_data
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        product: product_data,
        category_id: category_id,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to assign product: #{update_result.error}")
    end
  end

  # Unassign product from category
  def unassign_product
    category_id = @params[:category_id]
    product_id = @params[:product_id]
    
    return failure("Category ID is required") if category_id.blank?
    return failure("Product ID is required") if product_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    # Find and remove product
    category = categories[category_index]
    products = category['products'] || []
    
    product_index = products.find_index { |prod| prod['productId'] == product_id }
    return failure("Product not found in category") if product_index.nil?

    removed_product = products.delete_at(product_index)
    
    # Reorder remaining products
    products.each_with_index do |prod, index|
      prod['displayOrder'] = index
    end
    
    category['products'] = products
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        removed_product: removed_product,
        category_id: category_id,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to unassign product: #{update_result.error}")
    end
  end

  # Set product as default in category
  def set_default_product
    category_id = @params[:category_id]
    product_id = @params[:product_id]
    
    return failure("Category ID is required") if category_id.blank?
    return failure("Product ID is required") if product_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    # Update products in category
    category = categories[category_index]
    products = category['products'] || []
    
    # Clear all defaults and set new one
    products.each do |prod|
      prod['isDefault'] = (prod['productId'] == product_id)
    end
    
    category['products'] = products
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        product_id: product_id,
        category_id: category_id,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to set default product: #{update_result.error}")
    end
  end

  # Move product up in category
  def move_product_up
    category_id = @params[:category_id]
    product_id = @params[:product_id]
    
    return failure("Category ID is required") if category_id.blank?
    return failure("Product ID is required") if product_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    # Find product and move up
    category = categories[category_index]
    products = category['products'] || []
    
    product_index = products.find_index { |prod| prod['productId'] == product_id }
    return failure("Product not found") if product_index.nil?
    return failure("Product is already first") if product_index == 0

    # Swap with previous product
    products[product_index], products[product_index - 1] = products[product_index - 1], products[product_index]
    
    # Update display orders
    products.each_with_index do |prod, index|
      prod['displayOrder'] = index
    end
    
    category['products'] = products
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        product_id: product_id,
        category_id: category_id,
        new_position: product_index - 1,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to move product: #{update_result.error}")
    end
  end

  # Move product down in category
  def move_product_down
    category_id = @params[:category_id]
    product_id = @params[:product_id]
    
    return failure("Category ID is required") if category_id.blank?
    return failure("Product ID is required") if product_id.blank?

    metadata = @bundle_data['metadata'] || {}
    categories = metadata['categories'] || []
    
    # Find category
    category_index = categories.find_index { |cat| cat['categoryId'] == category_id }
    return failure("Category not found") if category_index.nil?

    # Find product and move down
    category = categories[category_index]
    products = category['products'] || []
    
    product_index = products.find_index { |prod| prod['productId'] == product_id }
    return failure("Product not found") if product_index.nil?
    return failure("Product is already last") if product_index == products.length - 1

    # Swap with next product
    products[product_index], products[product_index + 1] = products[product_index + 1], products[product_index]
    
    # Update display orders
    products.each_with_index do |prod, index|
      prod['displayOrder'] = index
    end
    
    category['products'] = products
    metadata['categories'] = categories

    # Update bundle metadata
    update_result = update_bundle_metadata(metadata)
    if update_result.success?
      success({
        product_id: product_id,
        category_id: category_id,
        new_position: product_index + 1,
        bundle_id: @bundle_id
      })
    else
      failure("Failed to move product: #{update_result.error}")
    end
  end

  # Update bundle metadata via Fluid API
  def update_bundle_metadata(metadata)
    Fluid::BundlesService.call(
      action: :update,
      bundle_id: @bundle_id,
      metadata: metadata,
      company: @company
    )
  end
end
