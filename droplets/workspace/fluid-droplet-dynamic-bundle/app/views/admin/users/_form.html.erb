<div class="bg-white rounded-lg p-4 border border-gray-100">
  <%= form_with model: [:admin, @user] do |form| %>
    <div class="flex items-center gap-4 mb-4">
      <div class="shrink-0 w-48">
        <%= form.label :email, "Email" %>
      </div>
      <div class="w-full">
        <%= form.email_field :email, class: "w-full p-2 border border-gray-300 rounded-md" %>
      </div>
    </div>
    <div class="flex items-center gap-4 mb-4">
      <div class="shrink-0 w-48">
        <%= form.label :password, "Password" %>
      </div>
      <div class="w-full">
        <%= form.password_field :password, class: "w-full p-2 border border-gray-300 rounded-md" %>
      </div>
    </div>
    <div class="flex items-center gap-4 mb-4">
      <div class="shrink-0 w-48">
        <%= form.label :password_confirmation, "Confirm Password" %>
      </div>
      <div class="w-full">
        <%= form.password_field :password_confirmation, class: "w-full p-2 border border-gray-300 rounded-md" %>
      </div>
    </div>
    <div class="flex items-center gap-4 mb-4">
      <div class="shrink-0 w-48">
        <%= form.label :permission_sets, "Permission Sets" %>
      </div>
      <div class="w-full">
        <%= form.select :permission_sets, @permission_sets, {}, {
          multiple: true,
          include_hidden: false,
          class: "w-full p-2 border border-gray-300 rounded-md"
        } %>
      </div>
    </div>
    <%= form.submit (@user.persisted? ? "Update" : "Create"), class: "inline-flex items-center text-sm bg-blue-600 hover:bg-orange-600 text-white px-4 py-2 rounded-md mr-2" %>
    <%= link_to "Cancel", admin_users_path, class: "inline-flex items-center text-sm bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md" %>
  <% end %>
</div>
