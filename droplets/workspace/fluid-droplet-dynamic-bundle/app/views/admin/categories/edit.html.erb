<% content_for :title, "Edit Category - #{@category['categoryName']}" %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h2>Edit Category: <%= @category['categoryName'] %></h2>
    <p class="text-muted mb-0">
      Bundle: <strong><%= @bundle['name'] %></strong> • SKU: <code><%= @bundle['sku'] %></code>
    </p>
  </div>
  
  <div class="header-actions">
    <%= link_to admin_bundle_categories_path(@bundle_id), class: "btn btn-secondary" do %>
      ← Back to Categories
    <% end %>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <%= form_with model: [:admin, @bundle_id, @category], 
        url: admin_bundle_category_path(@bundle_id, @category['categoryId']),
        method: :patch,
        local: true, 
        html: { class: "category-form" } do |form| %>
      
      <div class="form-group">
        <%= form.label :categoryName, "Category Name", class: "form-label" %>
        <%= form.text_field :categoryName, 
            class: "form-control", 
            placeholder: "e.g., Protein Powders, Pre-Workout, Recovery",
            required: true,
            value: @category['categoryName'] %>
        <div class="form-text">
          Choose a descriptive name that customers will easily understand.
        </div>
      </div>

      <div class="form-group">
        <%= form.label :selectionQuantity, "Selection Quantity", class: "form-label" %>
        <div class="quantity-input-group">
          <%= form.number_field :selectionQuantity, 
              class: "form-control quantity-input", 
              min: 1,
              max: 10,
              required: true,
              value: @category['selectionQuantity'] %>
          <div class="quantity-controls">
            <button type="button" class="quantity-btn" onclick="adjustQuantity(1)">+</button>
            <button type="button" class="quantity-btn" onclick="adjustQuantity(-1)">-</button>
          </div>
        </div>
        <div class="form-text">
          How many items must customers select from this category? (1-10)
        </div>
      </div>

      <div class="form-actions">
        <%= form.submit "Update Category", class: "btn btn-primary btn-lg" %>
        <%= link_to "Cancel", admin_bundle_categories_path(@bundle_id), class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
  
  <div class="col-md-4">
    <div class="info-panel">
      <h4>📝 Editing Category</h4>
      <p>
        You can update the category name and selection quantity. Changes will be 
        reflected immediately in the bundle configuration.
      </p>
      
      <div class="current-info">
        <h5>Current Information:</h5>
        <div class="info-item">
          <label>Category ID:</label>
          <code><%= @category['categoryId'] %></code>
        </div>
        
        <div class="info-item">
          <label>Display Order:</label>
          <%= (@category['displayOrder'] || 0) + 1 %>
        </div>
        
        <div class="info-item">
          <label>Products Assigned:</label>
          <%= @category['products']&.length || 0 %> products
        </div>
        
        <% if @category['products']&.any? %>
          <div class="info-item">
            <label>Default Product:</label>
            <% default_product = @category['products'].find { |p| p['isDefault'] } %>
            <% if default_product %>
              <%= default_product['variantTitle'] %>
            <% else %>
              <em>None set</em>
            <% end %>
          </div>
        <% end %>
      </div>
      
      <% if @category['products']&.any? %>
        <div class="products-preview">
          <h5>Assigned Products:</h5>
          <div class="products-list">
            <% @category['products'].each do |product| %>
              <div class="product-item">
                <span class="product-name"><%= product['variantTitle'] %></span>
                <% if product['isDefault'] %>
                  <span class="default-badge">Default</span>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
      
      <div class="warning-box">
        <h6>⚠️ Important Notes</h6>
        <ul>
          <li>Changing the selection quantity affects customer choices</li>
          <li>Make sure you have enough products assigned for the selection quantity</li>
          <li>Category ID cannot be changed to maintain data integrity</li>
          <li>Changes are applied immediately to active bundles</li>
        </ul>
      </div>
      
      <div class="quick-actions">
        <h6>Quick Actions:</h6>
        <div class="action-buttons">
          <button class="action-btn" onclick="manageProducts()">
            📦 Manage Products
          </button>
          <button class="action-btn" onclick="reorderCategory()">
            🔄 Change Order
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .row {
    display: flex;
    gap: 30px;
  }
  
  .col-md-8 {
    flex: 0 0 66.666667%;
  }
  
  .col-md-4 {
    flex: 0 0 33.333333%;
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .category-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .form-group {
    margin-bottom: 24px;
  }
  
  .form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
  }
  
  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
  }
  
  .form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  .form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 6px;
  }
  
  .quantity-input-group {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .quantity-input {
    width: 120px;
    text-align: center;
    font-weight: 600;
    font-size: 16px;
  }
  
  .quantity-controls {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
  }
  
  .quantity-btn {
    width: 32px;
    height: 24px;
    border: 1px solid #ced4da;
    background: white;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.2s ease;
  }
  
  .quantity-btn:first-child {
    border-radius: 4px 4px 0 0;
    border-bottom: none;
  }
  
  .quantity-btn:last-child {
    border-radius: 0 0 4px 4px;
  }
  
  .quantity-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
  
  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
  }
  
  .info-panel {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    position: sticky;
    top: 20px;
  }
  
  .info-panel h4 {
    color: #495057;
    margin-bottom: 12px;
    font-size: 16px;
  }
  
  .info-panel h5 {
    color: #495057;
    margin: 20px 0 10px 0;
    font-size: 14px;
  }
  
  .info-panel h6 {
    color: #495057;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  .info-panel p {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
  }
  
  .current-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin: 16px 0;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .info-item:last-child {
    margin-bottom: 0;
  }
  
  .info-item label {
    font-weight: 500;
    color: #495057;
  }
  
  .products-preview {
    margin: 16px 0;
  }
  
  .products-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
  }
  
  .product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
  }
  
  .product-name {
    color: #495057;
  }
  
  .default-badge {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
  }
  
  .warning-box {
    background: #fff3cd;
    padding: 16px;
    border-radius: 6px;
    margin: 16px 0;
    border-left: 4px solid #ffc107;
  }
  
  .warning-box ul {
    font-size: 13px;
    color: #856404;
    margin: 8px 0 0 0;
    padding-left: 20px;
  }
  
  .warning-box ul li {
    margin-bottom: 4px;
  }
  
  .quick-actions {
    margin-top: 16px;
  }
  
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
  }
  
  .action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
  }
  
  .action-btn:hover {
    background: #e9ecef;
    border-color: #007bff;
  }
  
  code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const quantityInput = document.querySelector('.quantity-input');
  const form = document.querySelector('.category-form');
  
  // Quantity adjustment functions
  window.adjustQuantity = function(delta) {
    const currentValue = parseInt(quantityInput.value) || 1;
    const newValue = Math.max(1, Math.min(10, currentValue + delta));
    quantityInput.value = newValue;
  };
  
  // Form validation
  if (form) {
    form.addEventListener('submit', function(e) {
      const categoryName = document.querySelector('input[name="category[categoryName]"]').value.trim();
      const selectionQuantity = parseInt(quantityInput.value);
      
      if (!categoryName) {
        alert('Please enter a category name.');
        document.querySelector('input[name="category[categoryName]"]').focus();
        e.preventDefault();
        return false;
      }
      
      if (categoryName.length < 2) {
        alert('Category name must be at least 2 characters long.');
        document.querySelector('input[name="category[categoryName]"]').focus();
        e.preventDefault();
        return false;
      }
      
      if (selectionQuantity < 1 || selectionQuantity > 10) {
        alert('Selection quantity must be between 1 and 10.');
        quantityInput.focus();
        e.preventDefault();
        return false;
      }
    });
  }
  
  // Warn about unsaved changes
  let formChanged = false;
  const formInputs = form.querySelectorAll('input, textarea, select');
  const originalValues = {};
  
  formInputs.forEach((input, index) => {
    originalValues[index] = input.value;
    
    input.addEventListener('input', function() {
      formChanged = (this.value !== originalValues[index]);
    });
  });
  
  // Warn when leaving page with unsaved changes
  window.addEventListener('beforeunload', function(e) {
    if (formChanged) {
      e.preventDefault();
      e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      return e.returnValue;
    }
  });
  
  // Don't warn when submitting form
  form.addEventListener('submit', function() {
    formChanged = false;
  });
});

function manageProducts() {
  alert('Product management will be implemented in the next ticket!');
}

function reorderCategory() {
  alert('Category reordering can be done from the categories list page using the up/down arrows.');
}
</script>
