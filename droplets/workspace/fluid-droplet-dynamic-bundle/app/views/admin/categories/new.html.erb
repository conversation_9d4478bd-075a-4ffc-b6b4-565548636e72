<% content_for :title, "Add Category - #{@bundle['name']}" %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h2>Add New Category</h2>
    <p class="text-muted mb-0">
      Bundle: <strong><%= @bundle['name'] %></strong> • SKU: <code><%= @bundle['sku'] %></code>
    </p>
  </div>
  
  <div class="header-actions">
    <%= link_to admin_bundle_categories_path(@bundle_id), class: "btn btn-secondary" do %>
      ← Back to Categories
    <% end %>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <%= form_with model: [:admin, @bundle_id, @category], 
        url: admin_bundle_categories_path(@bundle_id),
        local: true, 
        html: { class: "category-form" } do |form| %>
      
      <div class="form-group">
        <%= form.label :categoryName, "Category Name", class: "form-label" %>
        <%= form.text_field :categoryName, 
            class: "form-control", 
            placeholder: "e.g., Protein Powders, Pre-Workout, Recovery",
            required: true,
            value: @category[:categoryName] %>
        <div class="form-text">
          Choose a descriptive name that customers will easily understand.
        </div>
      </div>

      <div class="form-group">
        <%= form.label :selectionQuantity, "Selection Quantity", class: "form-label" %>
        <div class="quantity-input-group">
          <%= form.number_field :selectionQuantity, 
              class: "form-control quantity-input", 
              min: 1,
              max: 10,
              required: true,
              value: @category[:selectionQuantity] %>
          <div class="quantity-controls">
            <button type="button" class="quantity-btn" onclick="adjustQuantity(1)">+</button>
            <button type="button" class="quantity-btn" onclick="adjustQuantity(-1)">-</button>
          </div>
        </div>
        <div class="form-text">
          How many items must customers select from this category? (1-10)
        </div>
      </div>

      <div class="form-actions">
        <%= form.submit "Create Category", class: "btn btn-primary btn-lg" %>
        <%= link_to "Cancel", admin_bundle_categories_path(@bundle_id), class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
  
  <div class="col-md-4">
    <div class="info-panel">
      <h4>🏷️ Category Guidelines</h4>
      <p>
        Categories help organize products within your bundle. Each category represents 
        a group of related products that customers can choose from.
      </p>
      
      <div class="guidelines-list">
        <h5>Best Practices:</h5>
        <ul>
          <li><strong>Clear Names:</strong> Use descriptive names like "Protein Powders" instead of "Category 1"</li>
          <li><strong>Logical Grouping:</strong> Group similar or complementary products together</li>
          <li><strong>Selection Quantity:</strong> Consider how many items make sense for each category</li>
          <li><strong>Customer Experience:</strong> Think about how customers will navigate and understand the choices</li>
        </ul>
      </div>
      
      <div class="example-box">
        <h6>Example Categories:</h6>
        <div class="example-categories">
          <div class="example-category">
            <div class="example-header">
              <strong>🥤 Protein Powders</strong>
              <span class="example-quantity">Select 2</span>
            </div>
            <div class="example-description">
              Main protein source for the bundle
            </div>
          </div>
          
          <div class="example-category">
            <div class="example-header">
              <strong>⚡ Pre-Workout</strong>
              <span class="example-quantity">Select 1</span>
            </div>
            <div class="example-description">
              Energy boost for workouts
            </div>
          </div>
          
          <div class="example-category">
            <div class="example-header">
              <strong>🏃 Recovery</strong>
              <span class="example-quantity">Select 1</span>
            </div>
            <div class="example-description">
              Post-workout recovery supplements
            </div>
          </div>
        </div>
      </div>
      
      <div class="next-steps">
        <h6>After Creating:</h6>
        <ol>
          <li>Assign products to this category</li>
          <li>Set default selections</li>
          <li>Arrange category order</li>
          <li>Test customer experience</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<style>
  .row {
    display: flex;
    gap: 30px;
  }
  
  .col-md-8 {
    flex: 0 0 66.666667%;
  }
  
  .col-md-4 {
    flex: 0 0 33.333333%;
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .category-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .form-group {
    margin-bottom: 24px;
  }
  
  .form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
  }
  
  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
  }
  
  .form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  .form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 6px;
  }
  
  .quantity-input-group {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .quantity-input {
    width: 120px;
    text-align: center;
    font-weight: 600;
    font-size: 16px;
  }
  
  .quantity-controls {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
  }
  
  .quantity-btn {
    width: 32px;
    height: 24px;
    border: 1px solid #ced4da;
    background: white;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.2s ease;
  }
  
  .quantity-btn:first-child {
    border-radius: 4px 4px 0 0;
    border-bottom: none;
  }
  
  .quantity-btn:last-child {
    border-radius: 0 0 4px 4px;
  }
  
  .quantity-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
  
  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
  }
  
  .info-panel {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    position: sticky;
    top: 20px;
  }
  
  .info-panel h4 {
    color: #495057;
    margin-bottom: 12px;
    font-size: 16px;
  }
  
  .info-panel h5 {
    color: #495057;
    margin: 20px 0 10px 0;
    font-size: 14px;
  }
  
  .info-panel h6 {
    color: #495057;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  .info-panel p {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
  }
  
  .guidelines-list ul {
    font-size: 13px;
    color: #6c757d;
    padding-left: 20px;
    margin: 8px 0;
  }
  
  .guidelines-list li {
    margin-bottom: 8px;
    line-height: 1.4;
  }
  
  .example-box {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin: 16px 0;
    border-left: 4px solid #007bff;
  }
  
  .example-categories {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 12px;
  }
  
  .example-category {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }
  
  .example-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .example-quantity {
    font-size: 11px;
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
  }
  
  .example-description {
    font-size: 12px;
    color: #6c757d;
  }
  
  .next-steps ol {
    font-size: 13px;
    color: #6c757d;
    padding-left: 20px;
    margin: 8px 0 0 0;
  }
  
  .next-steps li {
    margin-bottom: 4px;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const quantityInput = document.querySelector('.quantity-input');
  const form = document.querySelector('.category-form');
  
  // Quantity adjustment functions
  window.adjustQuantity = function(delta) {
    const currentValue = parseInt(quantityInput.value) || 1;
    const newValue = Math.max(1, Math.min(10, currentValue + delta));
    quantityInput.value = newValue;
  };
  
  // Form validation
  if (form) {
    form.addEventListener('submit', function(e) {
      const categoryName = document.querySelector('input[name="category[categoryName]"]').value.trim();
      const selectionQuantity = parseInt(quantityInput.value);
      
      if (!categoryName) {
        alert('Please enter a category name.');
        document.querySelector('input[name="category[categoryName]"]').focus();
        e.preventDefault();
        return false;
      }
      
      if (categoryName.length < 2) {
        alert('Category name must be at least 2 characters long.');
        document.querySelector('input[name="category[categoryName]"]').focus();
        e.preventDefault();
        return false;
      }
      
      if (selectionQuantity < 1 || selectionQuantity > 10) {
        alert('Selection quantity must be between 1 and 10.');
        quantityInput.focus();
        e.preventDefault();
        return false;
      }
    });
  }
  
  // Auto-suggest emojis based on category name
  const nameInput = document.querySelector('input[name="category[categoryName]"]');
  if (nameInput) {
    nameInput.addEventListener('input', function() {
      const value = this.value.toLowerCase();
      let suggestion = '';
      
      if (value.includes('protein')) suggestion = '🥤 ';
      else if (value.includes('pre') || value.includes('energy')) suggestion = '⚡ ';
      else if (value.includes('recovery') || value.includes('post')) suggestion = '🏃 ';
      else if (value.includes('vitamin') || value.includes('supplement')) suggestion = '💊 ';
      else if (value.includes('snack') || value.includes('bar')) suggestion = '🍫 ';
      
      if (suggestion && !this.value.includes(suggestion.trim())) {
        // Don't auto-add, just show as placeholder suggestion
        this.setAttribute('data-suggestion', suggestion);
      }
    });
  }
});
</script>
