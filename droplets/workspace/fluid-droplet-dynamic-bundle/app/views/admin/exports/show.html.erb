<% content_for :title, "Export Metadata - #{@bundle['name']}" %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h2>Export Bundle Metadata</h2>
    <p class="text-muted mb-0">
      Bundle: <strong><%= @bundle['name'] %></strong> • 
      SKU: <code><%= @bundle['sku'] %></code> • 
      JSON Size: <strong><%= number_to_human_size(@json_size) %></strong>
    </p>
  </div>
  
  <div class="header-actions">
    <%= link_to admin_bundle_path(@bundle_id), class: "btn btn-secondary" do %>
      ← Back to Bundle
    <% end %>
  </div>
</div>

<div class="export-dashboard">
  <!-- Status Panel -->
  <div class="status-panel">
    <div class="status-header">
      <h3>📊 Export Status</h3>
      <div class="status-indicator <%= @is_valid ? 'valid' : 'invalid' %>">
        <%= @is_valid ? '✅ Ready to Export' : '❌ Validation Errors' %>
      </div>
    </div>
    
    <div class="status-content">
      <% if @is_valid %>
        <div class="success-message">
          <div class="success-icon">🎉</div>
          <h4>Bundle Configuration Complete!</h4>
          <p>Your bundle is properly configured and ready for export to Fluid.</p>
          
          <div class="export-stats">
            <div class="stat-item">
              <span class="stat-label">Categories:</span>
              <span class="stat-value"><%= @metadata_json[:categories]&.length || 0 %></span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Products:</span>
              <span class="stat-value"><%= @metadata_json[:categories]&.sum { |cat| cat[:products]&.length || 0 } || 0 %></span>
            </div>
            <div class="stat-item">
              <span class="stat-label">JSON Size:</span>
              <span class="stat-value"><%= number_to_human_size(@json_size) %></span>
            </div>
          </div>
          
          <div class="export-actions">
            <%= form_with url: admin_bundle_export_path(@bundle_id), 
                method: :post, 
                local: true, 
                html: { class: "export-form" } do |form| %>
              <%= form.submit "🚀 Export to Fluid", 
                  class: "btn btn-primary btn-lg export-btn",
                  data: { confirm: "Export bundle metadata to Fluid? This will update the product configuration." } %>
            <% end %>
            
            <%= link_to download_admin_bundle_export_path(@bundle_id), 
                class: "btn btn-outline-primary btn-lg" do %>
              📥 Download JSON
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="error-message">
          <div class="error-icon">⚠️</div>
          <h4>Configuration Issues Found</h4>
          <p>Please fix the following issues before exporting:</p>
          
          <div class="validation-errors">
            <% @validation_errors.each do |error| %>
              <div class="error-item">
                <span class="error-bullet">•</span>
                <span class="error-text"><%= error %></span>
              </div>
            <% end %>
          </div>
          
          <div class="fix-actions">
            <%= link_to admin_bundle_categories_path(@bundle_id), 
                class: "btn btn-warning" do %>
              🔧 Fix Categories
            <% end %>
            
            <button class="btn btn-outline-secondary" onclick="refreshValidation()">
              🔄 Re-validate
            </button>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  
  <!-- JSON Preview Panel -->
  <div class="preview-panel">
    <div class="preview-header">
      <h3>📋 JSON Preview</h3>
      <div class="preview-controls">
        <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard()">
          📋 Copy JSON
        </button>
        <button class="btn btn-sm btn-outline-secondary" onclick="toggleJsonFormat()">
          🔄 Toggle Format
        </button>
      </div>
    </div>
    
    <div class="json-container">
      <pre class="json-preview" id="jsonPreview"><code class="language-json"><%= @formatted_json %></code></pre>
    </div>
  </div>
</div>

<!-- Export History -->
<div class="history-section">
  <h3>📈 Export History</h3>
  
  <div class="history-table">
    <% if @export_history.any? %>
      <table class="table">
        <thead>
          <tr>
            <th>Date</th>
            <th>Status</th>
            <th>Size</th>
            <th>Categories</th>
            <th>Products</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          <% @export_history.each do |export| %>
            <tr class="<%= export[:status] == 'success' ? 'success-row' : 'error-row' %>">
              <td><%= time_ago_in_words(export[:exported_at]) %> ago</td>
              <td>
                <span class="status-badge <%= export[:status] %>">
                  <%= export[:status] == 'success' ? '✅' : '❌' %> <%= export[:status].capitalize %>
                </span>
              </td>
              <td><%= export[:json_size] > 0 ? number_to_human_size(export[:json_size]) : '-' %></td>
              <td><%= export[:categories_count] || '-' %></td>
              <td><%= export[:products_count] || '-' %></td>
              <td>
                <% if export[:error] %>
                  <span class="error-note"><%= truncate(export[:error], length: 50) %></span>
                <% else %>
                  <span class="success-note">v<%= export[:version] %></span>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="empty-history">
        <div class="empty-icon">📈</div>
        <h4>No Export History</h4>
        <p>This bundle hasn't been exported yet. Complete the configuration and export to see history here.</p>
      </div>
    <% end %>
  </div>
</div>

<style>
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .export-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
  }
  
  .status-panel,
  .preview-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
  }
  
  .status-header,
  .preview-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .status-header h3,
  .preview-header h3 {
    margin: 0;
    font-size: 18px;
    color: #495057;
  }
  
  .status-indicator {
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
  }
  
  .status-indicator.valid {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  .status-indicator.invalid {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  .status-content {
    padding: 24px;
  }
  
  .success-message,
  .error-message {
    text-align: center;
  }
  
  .success-icon,
  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .success-message h4,
  .error-message h4 {
    color: #495057;
    margin-bottom: 8px;
  }
  
  .success-message p,
  .error-message p {
    color: #6c757d;
    margin-bottom: 20px;
  }
  
  .export-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .stat-item {
    text-align: center;
  }
  
  .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
  }
  
  .stat-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #495057;
  }
  
  .export-actions,
  .fix-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 20px;
  }
  
  .validation-errors {
    text-align: left;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 16px;
    margin: 16px 0;
  }
  
  .error-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
  }
  
  .error-item:last-child {
    margin-bottom: 0;
  }
  
  .error-bullet {
    color: #dc3545;
    margin-right: 8px;
    font-weight: bold;
  }
  
  .error-text {
    color: #721c24;
    font-size: 14px;
  }
  
  .preview-controls {
    display: flex;
    gap: 8px;
  }
  
  .json-container {
    max-height: 500px;
    overflow-y: auto;
  }
  
  .json-preview {
    margin: 0;
    padding: 20px;
    background: #f8f9fa;
    border: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
  
  .history-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 24px;
  }
  
  .history-section h3 {
    margin: 0 0 20px 0;
    color: #495057;
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
  }
  
  .table th {
    background: #f8f9fa;
    padding: 12px;
    text-align: left;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #495057;
  }
  
  .table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
  }
  
  .success-row {
    background: #f8fff9;
  }
  
  .error-row {
    background: #fff8f8;
  }
  
  .status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
  }
  
  .status-badge.success {
    background: #d4edda;
    color: #155724;
  }
  
  .status-badge.failed {
    background: #f8d7da;
    color: #721c24;
  }
  
  .error-note {
    color: #dc3545;
    font-size: 12px;
  }
  
  .success-note {
    color: #28a745;
    font-size: 12px;
  }
  
  .empty-history {
    text-align: center;
    padding: 40px;
    color: #6c757d;
  }
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
  
  @media (max-width: 768px) {
    .export-dashboard {
      grid-template-columns: 1fr;
    }
    
    .export-actions,
    .fix-actions {
      flex-direction: column;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Copy JSON to clipboard
  window.copyToClipboard = function() {
    const jsonText = document.getElementById('jsonPreview').textContent;
    navigator.clipboard.writeText(jsonText).then(function() {
      showToast('JSON copied to clipboard!', 'success');
    }).catch(function() {
      showToast('Failed to copy JSON', 'error');
    });
  };
  
  // Toggle JSON format (compact/pretty)
  let isCompact = false;
  window.toggleJsonFormat = function() {
    const jsonPreview = document.getElementById('jsonPreview');
    const jsonData = <%= raw @metadata_json.to_json %>;
    
    if (isCompact) {
      jsonPreview.innerHTML = '<code class="language-json">' + JSON.stringify(jsonData, null, 2) + '</code>';
      isCompact = false;
    } else {
      jsonPreview.innerHTML = '<code class="language-json">' + JSON.stringify(jsonData) + '</code>';
      isCompact = true;
    }
  };
  
  // Refresh validation
  window.refreshValidation = function() {
    showToast('Re-validating bundle configuration...', 'info');
    
    fetch('<%= validate_admin_bundle_export_path(@bundle_id) %>', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast(data.message, data.is_valid ? 'success' : 'warning');
        if (data.is_valid) {
          setTimeout(() => location.reload(), 1500);
        }
      } else {
        showToast('Validation failed: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showToast('Validation request failed', 'error');
    });
  };
  
  // Simple toast notification
  function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      transition: all 0.3s ease;
    `;
    
    switch(type) {
      case 'success': toast.style.backgroundColor = '#28a745'; break;
      case 'error': toast.style.backgroundColor = '#dc3545'; break;
      case 'warning': toast.style.backgroundColor = '#ffc107'; toast.style.color = '#212529'; break;
      case 'info': toast.style.backgroundColor = '#17a2b8'; break;
    }
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }
});
</script>
