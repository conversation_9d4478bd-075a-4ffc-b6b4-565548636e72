<% content_for :title, "Edit #{@bundle['name']}" %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Edit Bundle: <%= @bundle['name'] %></h2>
  <div class="btn-group">
    <%= link_to "← Back to Bundle", admin_bundle_path(@bundle['id']), class: "btn btn-secondary" %>
    <%= link_to "View Bundle", admin_bundle_path(@bundle['id']), class: "btn btn-outline-primary" %>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <%= form_with model: [:admin, @bundle], local: true, html: { class: "bundle-form" } do |form| %>
      
      <div class="form-group">
        <%= form.label :name, class: "form-label" %>
        <%= form.text_field :name, 
            class: "form-control", 
            placeholder: "e.g., Transformation Bundle",
            required: true,
            value: @bundle['name'] %>
        <div class="form-text">
          This will be the display name for your bundle that customers will see.
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">SKU</label>
        <input type="text" 
               class="form-control" 
               value="<%= @bundle['sku'] %>"
               disabled>
        <div class="form-text">
          <strong>Note:</strong> SKU cannot be changed after bundle creation to maintain data integrity.
        </div>
      </div>

      <div class="form-group">
        <%= form.label :description, class: "form-label" %>
        <%= form.text_area :description, 
            class: "form-control", 
            rows: 4,
            placeholder: "Describe what this bundle contains and its benefits...",
            value: @bundle['description'] %>
        <div class="form-text">
          Optional description that will help customers understand this bundle.
        </div>
      </div>

      <div class="form-actions">
        <%= form.submit "Update Bundle", class: "btn btn-primary btn-lg" %>
        <%= link_to "Cancel", admin_bundle_path(@bundle['id']), class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
  
  <div class="col-md-4">
    <div class="info-panel">
      <h4>📝 Editing Bundle</h4>
      <p>
        You can update the bundle name and description. The SKU cannot be changed 
        to maintain consistency with existing orders and configurations.
      </p>
      
      <div class="current-info">
        <h5>Current Information:</h5>
        <div class="info-item">
          <label>SKU:</label>
          <code><%= @bundle['sku'] %></code>
        </div>
        
        <% if @bundle['created_at'] %>
          <div class="info-item">
            <label>Created:</label>
            <%= Time.parse(@bundle['created_at']).strftime("%B %d, %Y") %>
          </div>
        <% end %>
        
        <% if @bundle['status'] %>
          <div class="info-item">
            <label>Status:</label>
            <% if @bundle['status'] == 'active' %>
              <span class="badge badge-success">Active</span>
            <% else %>
              <span class="badge badge-secondary">Inactive</span>
            <% end %>
          </div>
        <% end %>
        
        <% categories_count = @bundle.dig('metadata', 'categories')&.length || 0 %>
        <div class="info-item">
          <label>Categories:</label>
          <%= categories_count %> configured
        </div>
      </div>
      
      <div class="warning-box">
        <h6>⚠️ Important Notes</h6>
        <ul>
          <li>Changes to the bundle name will be reflected immediately</li>
          <li>Description changes help customers understand the bundle better</li>
          <li>Category and product assignments are managed separately</li>
          <li>Active bundles may be visible to customers</li>
        </ul>
      </div>
    </div>
  </div>
</div>

<style>
  .row {
    display: flex;
    gap: 30px;
  }
  
  .col-md-8 {
    flex: 0 0 66.666667%;
  }
  
  .col-md-4 {
    flex: 0 0 33.333333%;
  }
  
  .bundle-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
  }
  
  .info-panel {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    position: sticky;
    top: 20px;
  }
  
  .info-panel h4 {
    color: #495057;
    margin-bottom: 12px;
    font-size: 16px;
  }
  
  .info-panel h5 {
    color: #495057;
    margin: 20px 0 10px 0;
    font-size: 14px;
  }
  
  .info-panel h6 {
    color: #495057;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  .info-panel p {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
  }
  
  .current-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin: 16px 0;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .info-item:last-child {
    margin-bottom: 0;
  }
  
  .info-item label {
    font-weight: 500;
    color: #495057;
  }
  
  .warning-box {
    background: #fff3cd;
    padding: 16px;
    border-radius: 6px;
    margin-top: 16px;
    border-left: 4px solid #ffc107;
  }
  
  .warning-box ul {
    font-size: 13px;
    color: #856404;
    margin: 8px 0 0 0;
    padding-left: 20px;
  }
  
  .warning-box ul li {
    margin-bottom: 4px;
  }
  
  .badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
  }
  
  .badge-success {
    background-color: #d4edda;
    color: #155724;
  }
  
  .badge-secondary {
    background-color: #e2e3e5;
    color: #383d41;
  }
  
  code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
  
  input:disabled {
    background-color: #e9ecef;
    opacity: 1;
    cursor: not-allowed;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.querySelector('.bundle-form');
  const nameField = document.querySelector('input[name="bundle[name]"]');
  
  // Form validation
  if (form) {
    form.addEventListener('submit', function(e) {
      const name = nameField.value.trim();
      
      if (!name) {
        alert('Please enter a bundle name.');
        nameField.focus();
        e.preventDefault();
        return false;
      }
      
      if (name.length < 3) {
        alert('Bundle name must be at least 3 characters long.');
        nameField.focus();
        e.preventDefault();
        return false;
      }
    });
  }
  
  // Warn about unsaved changes
  let formChanged = false;
  const formInputs = form.querySelectorAll('input, textarea');
  
  formInputs.forEach(input => {
    const originalValue = input.value;
    
    input.addEventListener('input', function() {
      formChanged = (this.value !== originalValue);
    });
  });
  
  // Warn when leaving page with unsaved changes
  window.addEventListener('beforeunload', function(e) {
    if (formChanged) {
      e.preventDefault();
      e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      return e.returnValue;
    }
  });
  
  // Don't warn when submitting form
  form.addEventListener('submit', function() {
    formChanged = false;
  });
});
</script>
