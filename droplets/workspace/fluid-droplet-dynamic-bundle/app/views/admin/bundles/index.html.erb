<% content_for :title, "Dynamic Bundles" %>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h2>Dynamic Bundles</h2>
  <%= link_to "Create New Bundle", new_admin_bundle_path, class: "btn btn-primary" %>
</div>

<% if @total_count > 0 %>
  <p class="text-muted">
    Showing <%= @bundles.length %> of <%= @total_count %> bundles
    <% if @pagination[:current_page] && @pagination[:total_pages] %>
      (Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %>)
    <% end %>
  </p>

  <table class="table">
    <thead>
      <tr>
        <th>Name</th>
        <th>SKU</th>
        <th>Description</th>
        <th>Status</th>
        <th>Categories</th>
        <th>Created</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% @bundles.each do |bundle| %>
        <tr>
          <td>
            <strong><%= bundle['name'] %></strong>
          </td>
          <td>
            <code><%= bundle['sku'] %></code>
          </td>
          <td>
            <%= truncate(bundle['description'], length: 60) %>
          </td>
          <td>
            <% if bundle['status'] == 'active' %>
              <span class="badge badge-success">Active</span>
            <% else %>
              <span class="badge badge-secondary">Inactive</span>
            <% end %>
          </td>
          <td>
            <% categories_count = bundle.dig('metadata', 'categories')&.length || 0 %>
            <%= categories_count %> categories
          </td>
          <td>
            <% if bundle['created_at'] %>
              <%= time_ago_in_words(Time.parse(bundle['created_at'])) %> ago
            <% else %>
              -
            <% end %>
          </td>
          <td>
            <div class="action-icons">
              <%= link_to admin_bundle_path(bundle['id']),
                  class: "action-icon view-icon",
                  title: "View bundle details" do %>
                👁️
              <% end %>

              <%= link_to edit_admin_bundle_path(bundle['id']),
                  class: "action-icon edit-icon",
                  title: "Edit bundle" do %>
                ✏️
              <% end %>

              <% if bundle['status'] == 'active' %>
                <%= link_to toggle_status_admin_bundle_path(bundle['id']),
                    method: :patch,
                    class: "action-icon deactivate-icon",
                    title: "Deactivate bundle",
                    data: {
                      confirm: "Are you sure you want to deactivate this bundle?",
                      turbo_method: :patch
                    } do %>
                  ⏸️
                <% end %>
              <% else %>
                <%= link_to toggle_status_admin_bundle_path(bundle['id']),
                    method: :patch,
                    class: "action-icon activate-icon",
                    title: "Activate bundle",
                    data: { turbo_method: :patch } do %>
                  ▶️
                <% end %>
              <% end %>

              <%= link_to admin_bundle_path(bundle['id']),
                  method: :delete,
                  class: "action-icon delete-icon",
                  title: "Delete bundle",
                  data: {
                    confirm: "Are you sure you want to delete this bundle? This action cannot be undone.",
                    turbo_method: :delete
                  } do %>
                🗑️
              <% end %>
            </div>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <!-- Pagination -->
  <% if @pagination[:total_pages] && @pagination[:total_pages] > 1 %>
    <nav class="mt-4">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <% if @pagination[:current_page] > 1 %>
            <%= link_to "← Previous", admin_bundles_path(page: @pagination[:current_page] - 1), 
                class: "btn btn-secondary" %>
          <% end %>
        </div>
        
        <div>
          Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %>
        </div>
        
        <div>
          <% if @pagination[:current_page] < @pagination[:total_pages] %>
            <%= link_to "Next →", admin_bundles_path(page: @pagination[:current_page] + 1), 
                class: "btn btn-secondary" %>
          <% end %>
        </div>
      </div>
    </nav>
  <% end %>

<% else %>
  <!-- Empty State -->
  <div class="text-center" style="padding: 60px 20px;">
    <div style="font-size: 48px; color: #ccc; margin-bottom: 20px;">📦</div>
    <h3>No Dynamic Bundles Yet</h3>
    <p class="text-muted mb-4">
      Create your first dynamic bundle to get started with configurable product bundles.
    </p>
    <%= link_to "Create Your First Bundle", new_admin_bundle_path, 
        class: "btn btn-primary btn-lg" %>
  </div>
<% end %>

<style>
  .badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
  }
  
  .badge-success {
    background-color: #d4edda;
    color: #155724;
  }
  
  .badge-secondary {
    background-color: #e2e3e5;
    color: #383d41;
  }
  
  .badge-warning {
    background-color: #fff3cd;
    color: #856404;
  }
  
  .action-icons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
  }

  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid transparent;
  }

  .action-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .view-icon {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }

  .view-icon:hover {
    background-color: #e9ecef;
    border-color: #6c757d;
  }

  .edit-icon {
    background-color: #e3f2fd;
    border-color: #bbdefb;
  }

  .edit-icon:hover {
    background-color: #bbdefb;
    border-color: #2196f3;
  }

  .activate-icon {
    background-color: #e8f5e8;
    border-color: #c8e6c9;
  }

  .activate-icon:hover {
    background-color: #c8e6c9;
    border-color: #4caf50;
  }

  .deactivate-icon {
    background-color: #fff3e0;
    border-color: #ffcc02;
  }

  .deactivate-icon:hover {
    background-color: #ffcc02;
    border-color: #ff9800;
  }

  .delete-icon {
    background-color: #ffebee;
    border-color: #ffcdd2;
  }

  .delete-icon:hover {
    background-color: #ffcdd2;
    border-color: #f44336;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle delete confirmations with better UX
  const deleteLinks = document.querySelectorAll('a[data-turbo-method="delete"]');
  
  deleteLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const bundleName = this.closest('tr').querySelector('strong').textContent;
      const confirmMessage = `Are you sure you want to delete "${bundleName}"?\n\nThis action cannot be undone and will remove all associated categories and products.`;
      
      if (!confirm(confirmMessage)) {
        e.preventDefault();
        return false;
      }
    });
  });
  
  // Handle status toggle confirmations
  const statusLinks = document.querySelectorAll('a[data-turbo-method="patch"]');
  
  statusLinks.forEach(link => {
    if (link.textContent.includes('Deactivate')) {
      link.addEventListener('click', function(e) {
        const bundleName = this.closest('tr').querySelector('strong').textContent;
        const confirmMessage = `Are you sure you want to deactivate "${bundleName}"?\n\nThis will make the bundle unavailable for customers.`;
        
        if (!confirm(confirmMessage)) {
          e.preventDefault();
          return false;
        }
      });
    }
  });
});
</script>
