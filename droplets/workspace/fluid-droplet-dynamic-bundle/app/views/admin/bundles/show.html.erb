<% content_for :title, @bundle_data['name'] %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h2><%= @bundle_data['name'] %></h2>
    <p class="text-muted mb-0">
      SKU: <code><%= @bundle_data['sku'] %></code>
      <% if @bundle_data['status'] %>
        • Status: 
        <% if @bundle_data['status'] == 'active' %>
          <span class="badge badge-success">Active</span>
        <% else %>
          <span class="badge badge-secondary">Inactive</span>
        <% end %>
      <% end %>
    </p>
  </div>
  
  <div class="header-actions">
    <%= link_to admin_bundles_path, class: "btn btn-secondary" do %>
      ← Back to Bundles
    <% end %>

    <div class="action-icons">
      <%= link_to edit_admin_bundle_path(@bundle_data['id']),
          class: "action-icon edit-icon",
          title: "Edit bundle" do %>
        ✏️ Edit
      <% end %>

      <% if @bundle_data['status'] == 'active' %>
        <%= link_to toggle_status_admin_bundle_path(@bundle_data['id']),
            method: :patch,
            class: "action-icon deactivate-icon",
            title: "Deactivate bundle",
            data: {
              confirm: "Are you sure you want to deactivate this bundle?",
              turbo_method: :patch
            } do %>
          ⏸️ Deactivate
        <% end %>
      <% else %>
        <%= link_to toggle_status_admin_bundle_path(@bundle_data['id']),
            method: :patch,
            class: "action-icon activate-icon",
            title: "Activate bundle",
            data: { turbo_method: :patch } do %>
          ▶️ Activate
        <% end %>
      <% end %>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <!-- Bundle Information -->
    <div class="info-card">
      <h3>Bundle Information</h3>
      
      <div class="info-grid">
        <div class="info-item">
          <label>Name</label>
          <div><%= @bundle_data['name'] %></div>
        </div>
        
        <div class="info-item">
          <label>SKU</label>
          <div><code><%= @bundle_data['sku'] %></code></div>
        </div>
        
        <div class="info-item">
          <label>Description</label>
          <div>
            <% if @bundle_data['description'].present? %>
              <%= simple_format(@bundle_data['description']) %>
            <% else %>
              <em class="text-muted">No description provided</em>
            <% end %>
          </div>
        </div>
        
        <div class="info-item">
          <label>Type</label>
          <div>
            <%= @bundle_data['type']&.humanize || 'Dynamic Bundle' %>
          </div>
        </div>
        
        <% if @bundle_data['created_at'] %>
          <div class="info-item">
            <label>Created</label>
            <div>
              <%= Time.parse(@bundle_data['created_at']).strftime("%B %d, %Y at %I:%M %p") %>
              (<%= time_ago_in_words(Time.parse(@bundle_data['created_at'])) %> ago)
            </div>
          </div>
        <% end %>
        
        <% if @bundle_data['updated_at'] %>
          <div class="info-item">
            <label>Last Updated</label>
            <div>
              <%= Time.parse(@bundle_data['updated_at']).strftime("%B %d, %Y at %I:%M %p") %>
              (<%= time_ago_in_words(Time.parse(@bundle_data['updated_at'])) %> ago)
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Categories Section -->
    <div class="info-card">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>Categories</h3>
        <button class="btn btn-primary btn-sm" onclick="addCategory()">
          + Add Category
        </button>
      </div>
      
      <% categories = @bundle_data.dig('metadata', 'categories') || [] %>
      <% if categories.any? %>
        <div class="categories-list">
          <% categories.each_with_index do |category, index| %>
            <div class="category-item">
              <div class="category-header">
                <h4><%= category['categoryName'] %></h4>
                <div class="category-meta">
                  Select <%= category['selectionQuantity'] %> • 
                  <%= category['products']&.length || 0 %> products
                </div>
              </div>
              
              <% if category['products']&.any? %>
                <div class="products-list">
                  <% category['products'].each do |product| %>
                    <div class="product-item">
                      <span class="product-name"><%= product['variantTitle'] %></span>
                      <code class="product-sku"><%= product['variantSku'] %></code>
                      <% if product['isDefault'] %>
                        <span class="badge badge-primary">Default</span>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="empty-products">
                  <em class="text-muted">No products assigned to this category yet.</em>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="empty-state">
          <div class="empty-icon">🏷️</div>
          <h4>No Categories Yet</h4>
          <p class="text-muted">
            Add categories to organize products within this bundle.
          </p>
          <button class="btn btn-primary" onclick="addCategory()">
            Add Your First Category
          </button>
        </div>
      <% end %>
    </div>
  </div>
  
  <div class="col-md-4">
    <!-- Quick Actions -->
    <div class="info-card">
      <h4>Quick Actions</h4>
      <div class="action-list">
        <%= link_to admin_bundle_categories_path(@bundle_data['id']), class: "action-item" do %>
          <span class="action-icon">🏷️</span>
          <div>
            <strong>Manage Categories</strong>
            <small>Add, edit, or remove categories</small>
          </div>
        <% end %>
        
        <% if @bundle_data.dig('metadata', 'categories')&.any? %>
          <%= link_to admin_bundle_category_products_path(@bundle_data['id'], @bundle_data.dig('metadata', 'categories').first['categoryId']),
              class: "action-item" do %>
            <span class="action-icon">📦</span>
            <div>
              <strong>Assign Products</strong>
              <small>Add products to categories</small>
            </div>
          <% end %>
        <% else %>
          <button class="action-item disabled" onclick="alert('Create categories first before assigning products.')">
            <span class="action-icon">📦</span>
            <div>
              <strong>Assign Products</strong>
              <small>Create categories first</small>
            </div>
          </button>
        <% end %>
        
        <%= link_to admin_bundle_export_path(@bundle_data['id']), class: "action-item" do %>
          <span class="action-icon">📤</span>
          <div>
            <strong>Export Metadata</strong>
            <small>Generate JSON configuration</small>
          </div>
        <% end %>
        
        <button class="action-item" onclick="previewBundle()">
          <span class="action-icon">👁️</span>
          <div>
            <strong>Preview Bundle</strong>
            <small>See how it looks to customers</small>
          </div>
        </button>
      </div>
    </div>
    
    <!-- Bundle Stats -->
    <div class="info-card">
      <h4>Bundle Statistics</h4>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number"><%= categories.length %></div>
          <div class="stat-label">Categories</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-number">
            <%= categories.sum { |cat| cat['products']&.length || 0 } %>
          </div>
          <div class="stat-label">Total Products</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-number">
            <%= categories.sum { |cat| cat['selectionQuantity'] || 0 } %>
          </div>
          <div class="stat-label">Required Selections</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .row {
    display: flex;
    gap: 30px;
  }
  
  .col-md-8 {
    flex: 0 0 66.666667%;
  }
  
  .col-md-4 {
    flex: 0 0 33.333333%;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-actions .action-icons {
    display: flex;
    gap: 8px;
  }

  .header-actions .action-icon {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid transparent;
  }

  .header-actions .action-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .header-actions .edit-icon {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #1976d2;
  }

  .header-actions .edit-icon:hover {
    background-color: #bbdefb;
    border-color: #2196f3;
    color: #0d47a1;
  }

  .header-actions .activate-icon {
    background-color: #e8f5e8;
    border-color: #c8e6c9;
    color: #2e7d32;
  }

  .header-actions .activate-icon:hover {
    background-color: #c8e6c9;
    border-color: #4caf50;
    color: #1b5e20;
  }

  .header-actions .deactivate-icon {
    background-color: #fff3e0;
    border-color: #ffcc02;
    color: #e65100;
  }

  .header-actions .deactivate-icon:hover {
    background-color: #ffcc02;
    border-color: #ff9800;
    color: #bf360c;
  }
  
  .info-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .info-card h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 18px;
  }
  
  .info-card h4 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 16px;
  }
  
  .info-grid {
    display: grid;
    gap: 16px;
  }
  
  .info-item label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    font-size: 14px;
  }
  
  .info-item div {
    color: #6c757d;
    font-size: 14px;
  }
  
  .categories-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .category-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    background: #f8f9fa;
  }
  
  .category-header h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: #495057;
  }
  
  .category-meta {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 12px;
  }
  
  .products-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .product-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }
  
  .product-name {
    flex: 1;
    font-size: 14px;
    color: #495057;
  }
  
  .product-sku {
    font-size: 12px;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
  }
  
  .empty-products {
    padding: 20px;
    text-align: center;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .action-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
  }
  
  .action-item:hover {
    background: #e9ecef;
    border-color: #007bff;
  }
  
  .action-icon {
    font-size: 20px;
  }
  
  .action-item strong {
    display: block;
    font-size: 14px;
    color: #495057;
  }
  
  .action-item small {
    display: block;
    font-size: 12px;
    color: #6c757d;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
  
  .stat-item {
    text-align: center;
    padding: 16px 8px;
    background: #f8f9fa;
    border-radius: 6px;
  }
  
  .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 4px;
  }
  
  .stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
  }
  
  .badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
  }
  
  .badge-success {
    background-color: #d4edda;
    color: #155724;
  }
  
  .badge-secondary {
    background-color: #e2e3e5;
    color: #383d41;
  }
  
  .badge-primary {
    background-color: #cce7ff;
    color: #004085;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
</style>

<script>
function addCategory() {
  alert('Category management will be implemented in the next ticket!');
}

// manageCategories function removed - now uses direct link

// assignProducts function removed - now uses direct link

// exportMetadata function removed - now uses direct link

function previewBundle() {
  alert('Bundle preview functionality coming soon!');
}
</script>
