<div class="flex items-center justify-between">
  <div class="flex items-center gap-2">
    <h1 class="font-custom text-4xl font-bold text-gray-600">Settings</h1>
  </div>
</div>

<div class="bg-white rounded-lg p-6 border border-gray-100">
  <table class="w-full">
    <thead class="bg-slate-50 border-b border-gray-400">
      <tr>
        <th class="pl-2 text-left text-slate-600">Name</th>
        <th class="pl-2 text-left text-slate-600">Values</th>
      </tr>
    </thead>
    <tbody>
      <% @settings.each do |setting| %>
        <tr class="odd:bg-white even:bg-slate-50 hover:bg-slate-100">
          <td class="pl-2 text-left text-gray-600"><%= link_to setting.name.humanize.titleize, edit_admin_setting_path(setting) %></td>
          <td class="pl-2 text-left text-gray-400 font-mono whitespace-pre"><%= format_settings_values(setting.values) %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>
