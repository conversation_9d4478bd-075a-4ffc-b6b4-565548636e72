@import "tailwindcss";

@source "../../../app/views/**/*.html.erb";

.gradient-background::before {
  content: "";
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(
      oklab(99.999999% 0 0 / 0.25),
      oklab(99.999999% 0 0 / 0) 40%
    ),
    radial-gradient(
      oklab(87.835683% 0.003366 0.14967) 30%,
      oklab(67.83255% 0.157095 -0.003474),
      oklab(67.83255% 0.157095 -0.003474 / 0.4) 41%,
      oklab(67.83255% 0.157095 -0.003474 / 0) 52%
    ),
    radial-gradient(
      oklab(58.984582% 0.14704 -0.231127) 37%,
      oklab(58.984582% 0.14704 -0.231127 / 0) 46%
    ),
    linear-gradient(
      155deg,
      oklab(76.355294% -0.174894 0.102381 / 0) 65%,
      oklab(76.355294% -0.174894 0.102381 / 1) 95%
    ),
    linear-gradient(
      45deg,
      oklab(53.548175% -0.040251 -0.199701),
      oklab(63.951656% -0.055073 -0.189536)
    );
  opacity: 0.06;
  filter: blur(30px);
  background-size:
    200% 200%,
    285% 500%,
    285% 500%,
    cover,
    cover;
  background-position:
    bottom left,
    109% 68%,
    109% 68%,
    center,
    center;
  z-index: -1;
  pointer-events: none;
}

.gradient-background {
  position: relative;
  isolation: isolate;
}

a {
  text-decoration: none;
  @apply text-blue-500 hover:text-orange-600;
}

.error-field {
  @apply text-red-500;
}
