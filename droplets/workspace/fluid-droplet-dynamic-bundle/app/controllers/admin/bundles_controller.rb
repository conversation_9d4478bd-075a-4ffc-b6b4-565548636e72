# frozen_string_literal: true

module Admin
  # Controller for managing dynamic bundles in the admin interface
  # Provides CRUD operations for bundle management
  class BundlesController < ApplicationController
    layout 'bundle_admin'

    before_action :authenticate_admin! # Assuming admin authentication exists
    before_action :ensure_company_context
    before_action :set_bundle, only: [:show, :edit, :update, :destroy, :toggle_status]

    # GET /admin/bundles
    # Lists all dynamic bundles with pagination
    def index
      @page = params[:page]&.to_i || 1
      @per_page = params[:per_page]&.to_i || 20

      # REAL API CALL - Use Fluid API to list bundles
      Rails.logger.info("REAL API: Calling Fluid API to list bundles")

      result = Fluid::BundlesService.call(
        action: :list,
        page: @page,
        per_page: @per_page,
        company: @company
      )

      if result.success?
        @bundles = result.data[:bundles] || []
        @pagination = result.data[:pagination] || { page: @page, per_page: @per_page, total: 0 }
        @total_count = @pagination[:total_count] || 0

        flash.now[:success] = "Loaded #{@bundles.size} bundles from Fluid API!"
        Rails.logger.info("Real API successful: #{@bundles.size} bundles loaded from Fluid")
      else
        # Fallback to test data if API fails
        Rails.logger.warn("API failed, using fallback data: #{result.error}")
        @bundles = [
          { "id" => 1, "name" => "Test Bundle 1", "status" => "active" },
          { "id" => 2, "name" => "Test Bundle 2", "status" => "draft" }
        ]
        @pagination = { page: @page, per_page: @per_page, total: 2 }
        @total_count = 2

        flash.now[:warning] = "Using test data - API error: #{result.error}"
      end
    end

    # GET /admin/bundles/:id
    # Shows details of a specific bundle
    def show
      # SIMPLE TEST - Use test data for now
      Rails.logger.info("SIMPLE TEST: Loading bundle #{params[:id]} with test data")

      bundle_id = params[:id].to_i

      @bundle_data = case bundle_id
      when 1
        {
          "id" => 1,
          "name" => "Test Bundle 1",
          "sku" => "TEST-BUNDLE-001",
          "description" => "This is a test bundle for development",
          "status" => "active",
          "created_at" => "2024-01-01T00:00:00Z",
          "updated_at" => "2024-01-01T00:00:00Z",
          "categories" => [
            { "id" => 1, "name" => "Category 1", "position" => 1, "products" => [] },
            { "id" => 2, "name" => "Category 2", "position" => 2, "products" => [] }
          ]
        }
      when 2
        {
          "id" => 2,
          "name" => "Test Bundle 2",
          "sku" => "TEST-BUNDLE-002",
          "description" => "Another test bundle for development",
          "status" => "draft",
          "created_at" => "2024-01-02T00:00:00Z",
          "updated_at" => "2024-01-02T00:00:00Z",
          "categories" => [
            { "id" => 3, "name" => "Category A", "position" => 1, "products" => [] }
          ]
        }
      else
        flash[:error] = "Bundle not found"
        redirect_to admin_bundles_path
        return
      end

      Rails.logger.info("Simple test successful: Bundle #{bundle_id} loaded")
    end

    # GET /admin/bundles/new
    # Shows form for creating a new bundle
    def new
      # Create an OpenStruct to work with form_with
      @bundle = OpenStruct.new(
        name: "",
        sku: "",
        description: "",
        status: "active"
      )
    end

    # POST /admin/bundles
    # Creates a new bundle shell
    def create
      # SIMPLE TEST - Simulate bundle creation
      Rails.logger.info("SIMPLE TEST: Creating bundle with params: #{bundle_params.inspect}")

      # Simulate validation
      if bundle_params[:name].blank?
        @bundle = OpenStruct.new(bundle_params.to_h)
        flash.now[:error] = "Bundle name is required"
        render :new, status: :unprocessable_entity
        return
      end

      if bundle_params[:sku].blank?
        @bundle = OpenStruct.new(bundle_params.to_h)
        flash.now[:error] = "Bundle SKU is required"
        render :new, status: :unprocessable_entity
        return
      end

      # Simulate successful creation
      new_id = rand(1000..9999)
      @bundle_data = {
        "id" => new_id,
        "name" => bundle_params[:name],
        "sku" => bundle_params[:sku],
        "description" => bundle_params[:description],
        "status" => bundle_params[:status] || "active",
        "created_at" => Time.current.iso8601,
        "updated_at" => Time.current.iso8601,
        "categories" => []
      }

      flash[:success] = "Bundle '#{@bundle_data['name']}' created successfully!"
      redirect_to admin_bundle_path(@bundle_data['id'])

      Rails.logger.info("Simple test successful: Bundle created with ID #{new_id}")
    end

    # GET /admin/bundles/:id/edit
    # Shows form for editing an existing bundle
    def edit
      # SIMPLE TEST - Use test data for editing
      Rails.logger.info("SIMPLE TEST: Loading bundle #{params[:id]} for editing")

      bundle_id = params[:id].to_i

      @bundle = case bundle_id
      when 1
        OpenStruct.new(
          id: 1,
          name: "Test Bundle 1",
          sku: "TEST-BUNDLE-001",
          description: "This is a test bundle for development",
          status: "active"
        )
      when 2
        OpenStruct.new(
          id: 2,
          name: "Test Bundle 2",
          sku: "TEST-BUNDLE-002",
          description: "Another test bundle for development",
          status: "draft"
        )
      else
        flash[:error] = "Bundle not found"
        redirect_to admin_bundles_path
        return
      end

      Rails.logger.info("Simple test successful: Bundle #{bundle_id} loaded for editing")
    end

    # PATCH/PUT /admin/bundles/:id
    # Updates an existing bundle
    def update
      # SIMPLE TEST - Simulate bundle update
      Rails.logger.info("SIMPLE TEST: Updating bundle #{params[:id]} with params: #{bundle_params.inspect}")

      bundle_id = params[:id].to_i

      # Simulate validation
      if bundle_params[:name].blank?
        @bundle = OpenStruct.new(bundle_params.to_h.merge(id: params[:id]))
        flash.now[:error] = "Bundle name is required"
        render :edit, status: :unprocessable_entity
        return
      end

      # Check if bundle exists
      unless [1, 2].include?(bundle_id)
        flash[:error] = "Bundle not found"
        redirect_to admin_bundles_path
        return
      end

      # Simulate successful update
      @bundle_data = {
        "id" => bundle_id,
        "name" => bundle_params[:name],
        "description" => bundle_params[:description],
        "status" => bundle_params[:status] || "active",
        "updated_at" => Time.current.iso8601
      }

      flash[:success] = "Bundle '#{@bundle_data['name']}' updated successfully!"
      redirect_to admin_bundle_path(@bundle_data['id'])

      Rails.logger.info("Simple test successful: Bundle #{bundle_id} updated")
    end

    # DELETE /admin/bundles/:id
    # Deletes a bundle (if supported by API)
    def destroy
      # SIMPLE TEST - Simulate bundle deletion
      Rails.logger.info("SIMPLE TEST: Deleting bundle #{params[:id]}")

      bundle_id = params[:id].to_i

      # Check if bundle exists
      unless [1, 2].include?(bundle_id)
        flash[:error] = "Bundle not found"
        redirect_to admin_bundles_path
        return
      end

      # Simulate successful deletion
      flash[:success] = "Bundle deleted successfully!"
      redirect_to admin_bundles_path

      Rails.logger.info("Simple test successful: Bundle #{bundle_id} deleted")
    rescue => e
      flash[:error] = "Failed to delete bundle: #{e.message}"
      redirect_to admin_bundles_path
    end

    # PATCH /admin/bundles/:id/toggle_status
    # Toggles bundle status between active/inactive
    def toggle_status
      # This would need to be implemented based on the actual API
      # For now, we'll show a placeholder
      
      flash[:success] = "Bundle status updated successfully!"
      redirect_back(fallback_location: admin_bundles_path)
    rescue => e
      flash[:error] = "Failed to update bundle status: #{e.message}"
      redirect_back(fallback_location: admin_bundles_path)
    end

    private

    # Set bundle for actions that need it
    def set_bundle
      @bundle_id = params[:id]
    end

    # Strong parameters for bundle creation/update
    def bundle_params
      params.require(:bundle).permit(:name, :sku, :description, :status)
    end

    # Ensure company context is available
    def ensure_company_context
      unless @company
        # HARDCODE FOR DEVELOPMENT - Remove when testing with real stores
        if Rails.env.development?
          @company = OpenStruct.new(
            fluid_company_id: *********,
            name: "Development Company",
            droplet_installation_uuid: "dev-dri-123",
            authentication_token: "your_real_fluid_token_here"
          )
          Rails.logger.info("HARDCODED: Using development company (ID: #{@company.fluid_company_id})")
          return
        end

        handle_missing_company_context
      end
    end

    # Placeholder for admin authentication
    # This should be implemented based on your authentication system
    def authenticate_admin!
      # TODO: Implement admin authentication
      # For now, we'll assume all users are admins in development
      if Rails.env.development?
        Rails.logger.info("DEVELOPMENT: Skipping authentication")
        return true
      end

      # In production, implement proper authentication
      redirect_to root_path unless current_user&.admin?
    end


  end
end
