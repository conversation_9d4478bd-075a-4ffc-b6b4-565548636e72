# frozen_string_literal: true

module Admin
  # Controller for managing categories within dynamic bundles
  # Handles CRUD operations for bundle categories
  class CategoriesController < ApplicationController
    layout 'bundle_admin'
    
    before_action :authenticate_admin!
    before_action :set_bundle
    before_action :set_category, only: [:show, :edit, :update, :destroy, :move_up, :move_down]

    # GET /admin/bundles/:bundle_id/categories
    # Lists all categories for a specific bundle
    def index
      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundles_path
        return
      end

      # Fetch categories from the bundle metadata
      result = Fluid::BundlesService.call(
        action: :find,
        bundle_id: @bundle_id
      )

      if result.success?
        bundle_data = result.data[:bundle]
        @categories = bundle_data.dig('metadata', 'categories') || []
      else
        @categories = []
        flash.now[:error] = "Failed to load categories: #{result.error}"
      end
    end

    # GET /admin/bundles/:bundle_id/categories/:id
    # Shows details of a specific category
    def show
      # Category details will be loaded by set_category
    end

    # GET /admin/bundles/:bundle_id/categories/new
    # Shows form for creating a new category
    def new
      @category = {
        categoryName: "",
        selectionQuantity: 1,
        displayOrder: next_display_order,
        products: []
      }
    end

    # POST /admin/bundles/:bundle_id/categories
    # Creates a new category
    def create
      @category = category_params.to_h.merge(
        'categoryId' => generate_category_id,
        'displayOrder' => next_display_order,
        'products' => []
      )

      # Use BundleMetadataService to add category
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :add_category,
        category_data: @category
      )

      if result.success?
        flash[:success] = "Category '#{@category['categoryName']}' created successfully!"
        redirect_to admin_bundle_categories_path(@bundle_id)
      else
        @category = category_params.to_h
        flash.now[:error] = "Failed to create category: #{result.error}"
        render :new, status: :unprocessable_entity
      end
    rescue => e
      @category = category_params.to_h
      flash.now[:error] = "Failed to create category: #{e.message}"
      render :new, status: :unprocessable_entity
    end

    # GET /admin/bundles/:bundle_id/categories/:id/edit
    # Shows form for editing an existing category
    def edit
      # Category will be loaded by set_category
    end

    # PATCH/PUT /admin/bundles/:bundle_id/categories/:id
    # Updates an existing category
    def update
      # Merge existing category data with updates
      updated_category = @category.merge(category_params.to_h)

      # Use BundleMetadataService to update category
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :update_category,
        category_id: @category['categoryId'],
        category_data: updated_category
      )

      if result.success?
        flash[:success] = "Category '#{updated_category['categoryName']}' updated successfully!"
        redirect_to admin_bundle_categories_path(@bundle_id)
      else
        flash.now[:error] = "Failed to update category: #{result.error}"
        render :edit, status: :unprocessable_entity
      end
    rescue => e
      flash.now[:error] = "Failed to update category: #{e.message}"
      render :edit, status: :unprocessable_entity
    end

    # DELETE /admin/bundles/:bundle_id/categories/:id
    # Deletes a category
    def destroy
      category_name = @category['categoryName']

      # Use BundleMetadataService to delete category
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :delete_category,
        category_id: @category['categoryId']
      )

      if result.success?
        flash[:success] = "Category '#{category_name}' deleted successfully!"
      else
        flash[:error] = "Failed to delete category: #{result.error}"
      end

      redirect_to admin_bundle_categories_path(@bundle_id)
    rescue => e
      flash[:error] = "Failed to delete category: #{e.message}"
      redirect_to admin_bundle_categories_path(@bundle_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:id/move_up
    # Moves category up in display order
    def move_up
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_category_up,
        category_id: @category['categoryId']
      )

      if result.success?
        flash[:success] = "Category moved up successfully!"
      else
        flash[:error] = "Failed to move category: #{result.error}"
      end

      redirect_to admin_bundle_categories_path(@bundle_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:id/move_down
    # Moves category down in display order
    def move_down
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_category_down,
        category_id: @category['categoryId']
      )

      if result.success?
        flash[:success] = "Category moved down successfully!"
      else
        flash[:error] = "Failed to move category: #{result.error}"
      end

      redirect_to admin_bundle_categories_path(@bundle_id)
    end

    private

    # Set bundle ID from params
    def set_bundle
      @bundle_id = params[:bundle_id]

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundles_path
        return
      end

      # Load bundle data for context
      result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
      if result.success?
        @bundle = result.data[:bundle]
      else
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundles_path
      end
    end

    # Set category for actions that need it
    def set_category
      category_id = params[:id]

      # Find category in bundle metadata
      categories = @bundle.dig('metadata', 'categories') || []
      @category = categories.find { |cat| cat['categoryId'] == category_id }

      unless @category
        flash[:error] = "Category not found"
        redirect_to admin_bundle_categories_path(@bundle_id)
      end
    end

    # Strong parameters for category creation/update
    def category_params
      params.require(:category).permit(:categoryName, :selectionQuantity, :displayOrder)
    end

    # Generate next display order
    def next_display_order
      categories = @bundle.dig('metadata', 'categories') || []
      return 0 if categories.empty?

      max_order = categories.map { |cat| cat['displayOrder'] || 0 }.max
      max_order + 1
    end

    # Generate unique category ID
    def generate_category_id
      "cat-#{SecureRandom.hex(4)}"
    end

    # Placeholder for admin authentication
    def authenticate_admin!
      return true if Rails.env.development?
      redirect_to root_path unless current_user&.admin?
    end


  end
end
