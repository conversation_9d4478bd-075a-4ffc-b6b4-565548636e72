# frozen_string_literal: true

module Admin
  # Controller for managing bundle metadata export to Fluid products
  # Handles JSON generation, validation, preview, and export operations
  class ExportsController < ApplicationController
    layout 'bundle_admin'
    
    before_action :authenticate_admin!
    before_action :set_bundle

    # GET /admin/bundles/:bundle_id/export
    # Shows export dashboard with current status and options
    def show
      @export_service = BundleMetadataExportService.new(@bundle_id)
      
      # Generate preview without actually exporting
      preview_result = generate_preview
      
      if preview_result.success?
        @metadata_json = preview_result.data[:metadata]
        @validation_errors = preview_result.data[:validation_errors] || []
        @is_valid = @validation_errors.empty?
        @json_size = @metadata_json.to_json.bytesize
        @formatted_json = JSON.pretty_generate(@metadata_json)
      else
        @metadata_json = {}
        @validation_errors = [preview_result.error]
        @is_valid = false
        @json_size = 0
        @formatted_json = "{}"
      end
      
      # Get export history from bundle metadata or logs
      @export_history = get_export_history
    end

    # POST /admin/bundles/:bundle_id/export
    # Performs the actual export to Fluid
    def create
      result = BundleMetadataExportService.call(@bundle_id)
      
      if result.success?
        flash[:success] = "Bundle metadata exported successfully! JSON size: #{result.data[:metadata].to_json.bytesize} bytes"
        redirect_to admin_bundle_export_path(@bundle_id)
      else
        flash[:error] = "Export failed: #{result.error}"
        redirect_to admin_bundle_export_path(@bundle_id)
      end
    end

    # GET /admin/bundles/:bundle_id/export/preview
    # Returns JSON preview for AJAX requests
    def preview
      result = generate_preview
      
      if result.success?
        render json: {
          success: true,
          metadata: result.data[:metadata],
          validation_errors: result.data[:validation_errors] || [],
          is_valid: result.data[:validation_errors].empty?,
          json_size: result.data[:metadata].to_json.bytesize,
          formatted_json: JSON.pretty_generate(result.data[:metadata])
        }
      else
        render json: {
          success: false,
          error: result.error,
          metadata: {},
          validation_errors: [result.error],
          is_valid: false,
          json_size: 0,
          formatted_json: "{}"
        }
      end
    end

    # POST /admin/bundles/:bundle_id/export/validate
    # Validates bundle configuration without exporting
    def validate
      result = generate_preview
      
      if result.success?
        validation_errors = result.data[:validation_errors] || []
        is_valid = validation_errors.empty?
        
        render json: {
          success: true,
          is_valid: is_valid,
          validation_errors: validation_errors,
          message: is_valid ? "Bundle configuration is valid and ready for export!" : "Bundle configuration has validation errors."
        }
      else
        render json: {
          success: false,
          is_valid: false,
          validation_errors: [result.error],
          message: "Validation failed: #{result.error}"
        }
      end
    end

    # GET /admin/bundles/:bundle_id/export/download
    # Downloads the metadata JSON as a file
    def download
      result = generate_preview
      
      if result.success? && result.data[:validation_errors].empty?
        metadata = result.data[:metadata]
        filename = "#{@bundle['sku']}_metadata_#{Time.current.strftime('%Y%m%d_%H%M%S')}.json"
        
        send_data JSON.pretty_generate(metadata),
                  filename: filename,
                  type: 'application/json',
                  disposition: 'attachment'
      else
        flash[:error] = "Cannot download: Bundle configuration is invalid"
        redirect_to admin_bundle_export_path(@bundle_id)
      end
    end

    private

    # Set bundle from params
    def set_bundle
      @bundle_id = params[:bundle_id]

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundles_path
        return
      end

      # Load bundle data from API
      result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
      if result.success?
        @bundle = result.data[:bundle]
      else
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundles_path
        return
      end
    end

    # Placeholder for admin authentication
    def authenticate_admin!
      return true if Rails.env.development?
      redirect_to root_path unless current_user&.admin?
    end

    # Generate metadata preview without exporting
    def generate_preview
      service = BundleMetadataExportService.new(@bundle_id)
      
      # We need to access private methods for preview, so we'll create a custom preview method
      begin
        service.send(:load_bundle_data)
        service.send(:validate_bundle_configuration)
        
        validation_errors = service.instance_variable_get(:@validation_errors)
        metadata = service.send(:generate_metadata_json)
        
        ServiceResult.success({
          metadata: metadata,
          validation_errors: validation_errors
        })
      rescue => e
        ServiceResult.failure("Preview generation failed: #{e.message}")
      end
    end

    # Get export history from bundle metadata or logs
    # In a real implementation, this could come from:
    # - Bundle metadata export_history field
    # - Separate export logs table
    # - External logging service
    def get_export_history
      # For now, return empty array - implement based on your logging strategy
      # Could be stored in bundle metadata like:
      # @bundle.dig('metadata', 'export_history') || []

      # Or from a separate ExportLog model:
      # ExportLog.where(bundle_id: @bundle_id).order(created_at: :desc).limit(10)

      []
    end
  end
end
