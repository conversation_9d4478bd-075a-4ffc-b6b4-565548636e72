# frozen_string_literal: true

# Concern for setting Bundle droplet context from request data
# Similar to <PERSON><PERSON><PERSON>'s pattern for session and context management
module BundleContextConcern
  extend ActiveSupport::Concern

  included do
    before_action :set_bundle_context, if: :should_set_bundle_context?
  end

private

  # Determine if we should set Bundle context for this request
  def should_set_bundle_context?
    # Set context for admin controllers and bundle-related endpoints
    controller_path.start_with?('admin/') || 
    %w[bundle bundles category categories product products export exports].include?(controller_name)
  end

  # Extract and set company context from request
  def set_bundle_context
    @company = extract_company_from_request
    
    if @company
      Rails.logger.info("BundleContextConcern - Set context for company: #{@company.name} (ID: #{@company.fluid_company_id})")
    else
      Rails.logger.debug("BundleContextConcern - No company found in request")
    end
  end

  # Extract Company from various request sources
  def extract_company_from_request
    # Try droplet installation UUID (dri) from params first
    dri = extract_dri_from_request
    if dri.present?
      company = Company.find_by_dri(dri)
      if company
        # Store in session for subsequent requests
        session[:droplet_installation_uuid] = dri
        session[:company_id] = company.fluid_company_id
        return company
      end
    end

    # Try session (for subsequent requests after initial dri)
    if session[:droplet_installation_uuid].present?
      company = Company.find_by_dri(session[:droplet_installation_uuid])
      return company if company
    end

    # Try company_id from session
    if session[:company_id].present?
      company = Company.find_by(fluid_company_id: session[:company_id])
      return company if company
    end

    # Try URL params
    if params[:company_id].present?
      company = Company.find_by(fluid_company_id: params[:company_id])
      return company if company
    end

    # Try headers (for API requests)
    if request.headers["X-Company-ID"].present?
      company = Company.find_by(fluid_company_id: request.headers["X-Company-ID"])
      return company if company
    end

    # Try to parse request body for callback payloads
    company_id_from_body = extract_company_id_from_body
    if company_id_from_body
      company = Company.find_by(fluid_company_id: company_id_from_body)
      return company if company
    end

    nil
  end

  # Extract droplet installation UUID from request parameters
  def extract_dri_from_request
    params[:dri] || params[:droplet_uuid] || params[:installation_uuid]
  end

  # Extract company_id from request body (for callbacks)
  def extract_company_id_from_body
    return nil unless request.content_type&.include?("application/json")

    begin
      request.body.rewind
      body = request.body.read
      request.body.rewind

      return nil if body.blank?

      parsed_body = JSON.parse(body)

      # Try different payload structures
      company_id = parsed_body.dig("company", "id") ||
                   parsed_body.dig("cart", "company", "id") ||
                   parsed_body.dig("company_id") ||
                   parsed_body.dig("fluid_company_id")

      company_id&.to_i
    rescue JSON::ParserError => e
      Rails.logger.warn("BundleContextConcern - Failed to parse JSON body: #{e.message}")
      nil
    rescue => e
      Rails.logger.error("BundleContextConcern - Unexpected error parsing body: #{e.message}")
      nil
    end
  end

  # Handle missing company context
  def handle_missing_company_context
    if Rails.env.development? && ENV['FLUID_API_TOKEN'].present?
      Rails.logger.info("BundleContextConcern - Development mode: Using environment API token")
      return true # Allow development mode without company context
    end

    flash[:error] = "Missing droplet installation context. Please access through your store admin."
    redirect_to root_path
    false
  end
end
