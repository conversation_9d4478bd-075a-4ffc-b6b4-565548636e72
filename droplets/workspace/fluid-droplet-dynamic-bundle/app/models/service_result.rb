# frozen_string_literal: true

# ServiceResult using Ruby's Data class for immutable, high-performance result objects
# Provides a consistent interface for service responses across the application
#
# @example Success result
#   result = ServiceResult.success({ products: [...] })
#   result.success? # => true
#   result.data     # => { products: [...] }
#   result.error    # => nil
#
# @example Failure result
#   result = ServiceResult.failure("API connection failed")
#   result.success? # => false
#   result.failure? # => true
#   result.error    # => "API connection failed"
#   result.data     # => nil
#
ServiceResult = Data.define(:data, :error) do
  # Check if the result represents a successful operation
  # @return [Boolean] true if no error is present
  def success?
    error.nil?
  end

  # Check if the result represents a failed operation
  # @return [Boolean] true if an error is present
  def failure?
    !success?
  end

  # Create a successful result
  # @param data [Object] the payload/data of the successful operation
  # @return [ServiceResult] a successful result instance
  def self.success(data = nil)
    new(data: data, error: nil)
  end

  # Create a failure result
  # @param error [String, Exception, Hash] the error message, exception, or error details
  # @return [ServiceResult] a failure result instance
  def self.failure(error)
    error_data = case error
                 when Exception
                   error.message
                 when Hash
                   error
                 else
                   error.to_s
                 end
    new(data: nil, error: error_data)
  end

  # Convert to hash for JSON serialization or legacy compatibility
  # @return [Hash] hash representation of the result
  def to_h
    {
      success: success?,
      data: data,
      error: error
    }
  end

  # Alias for to_h for compatibility
  alias_method :to_hash, :to_h

  # Get data or raise error if failed
  # @raise [StandardError] if the result is a failure
  # @return [Object] the data if successful
  def data!
    raise StandardError, error if failure?
    data
  end

  # Transform the data if successful, otherwise return self
  # @yield [Object] the data to transform
  # @return [ServiceResult] new result with transformed data or original failure
  def map
    return self if failure?
    ServiceResult.success(yield(data))
  end

  # Chain operations that return ServiceResult
  # @yield [Object] the data to process
  # @return [ServiceResult] the result of the block or original failure
  def and_then
    return self if failure?
    yield(data)
  end

  # Get data or default value if failed
  # @param default_value [Object] value to return if failed
  # @return [Object] data if successful, default_value if failed
  def value_or(default_value)
    success? ? data : default_value
  end
end
