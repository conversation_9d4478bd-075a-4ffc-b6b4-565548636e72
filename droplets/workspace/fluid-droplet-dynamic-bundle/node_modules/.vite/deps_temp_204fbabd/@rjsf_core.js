import {
  ADDITIONAL_PROPERTY_FLAG,
  ANY_OF_KEY,
  ERRORS_KEY,
  ID_KEY,
  ITEMS_KEY,
  NAME_KEY,
  ONE_OF_KEY,
  PROPERTIES_KEY,
  REF_KEY,
  RJSF_ADDITIONAL_PROPERTIES_FLAG,
  SUBMIT_BTN_OPTIONS_KEY,
  TranslatableString,
  UI_GLOBAL_OPTIONS_KEY,
  UI_OPTIONS_KEY,
  allowAdditionalItems,
  ariaDescribedByIds,
  asNumber,
  basePickBy_default,
  baseUnset_default,
  canExpand,
  cloneDeep_default,
  createErrorHandler,
  createSchemaUtils,
  dataURItoBlob,
  dateRangeOptions,
  deepEquals,
  descriptionId,
  englishStringTranslator,
  enumOptionsDeselectValue,
  enumOptionsIndexForValue,
  enumOptionsIsSelected,
  enumOptionsSelectValue,
  enumOptionsValueForIndex,
  errorId,
  examplesId,
  flatRest_default,
  forEach_default,
  getChangedFields,
  getDateElementProps,
  getDiscriminatorFieldFromSchema,
  getInputProps,
  getSchemaType,
  getSubmitButtonOptions,
  getTemplate,
  getUiOptions,
  getWidget,
  get_default,
  hasIn_default,
  hasWidget,
  has_default,
  helpId,
  isCustomWidget,
  isEmpty_default,
  isFixedItems,
  isNil_default,
  isObject,
  isObject_default,
  labelValue,
  localToUTC,
  mergeObjects,
  mergeSchemas,
  omit_default,
  optionId,
  optionsList,
  orderProperties,
  parseDateString,
  require_jsx_runtime,
  schemaRequiresTrueValue,
  set_default,
  shouldRender,
  titleId,
  toDateString,
  toErrorList,
  toPath_default,
  unwrapErrorHandler,
  utcToLocal,
  validationDataMerge
} from "./chunk-F6P5UATK.js";
import {
  require_react
} from "./chunk-S5FHZHKU.js";
import {
  __publicField,
  __toESM
} from "./chunk-EQCVQC35.js";

// node_modules/@rjsf/core/lib/components/Form.js
var import_jsx_runtime45 = __toESM(require_jsx_runtime());
var import_react17 = __toESM(require_react());

// node_modules/lodash-es/_basePick.js
function basePick(object, paths) {
  return basePickBy_default(object, paths, function(value, path) {
    return hasIn_default(object, path);
  });
}
var basePick_default = basePick;

// node_modules/lodash-es/pick.js
var pick = flatRest_default(function(object, paths) {
  return object == null ? {} : basePick_default(object, paths);
});
var pick_default = pick;

// node_modules/@rjsf/core/lib/components/fields/ArrayField.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());

// node_modules/@rjsf/core/node_modules/nanoid/index.browser.js
var nanoid = (size = 21) => crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {
  byte &= 63;
  if (byte < 36) {
    id += byte.toString(36);
  } else if (byte < 62) {
    id += (byte - 26).toString(36).toUpperCase();
  } else if (byte > 62) {
    id += "-";
  } else {
    id += "_";
  }
  return id;
}, "");

// node_modules/@rjsf/core/lib/components/fields/ArrayField.js
function generateRowId() {
  return nanoid();
}
function generateKeyedFormData(formData) {
  return !Array.isArray(formData) ? [] : formData.map((item) => {
    return {
      key: generateRowId(),
      item
    };
  });
}
function keyedToPlainFormData(keyedFormData) {
  if (Array.isArray(keyedFormData)) {
    return keyedFormData.map((keyedItem) => keyedItem.item);
  }
  return [];
}
var ArrayField = class extends import_react.Component {
  /** Constructs an `ArrayField` from the `props`, generating the initial keyed data from the `formData`
   *
   * @param props - The `FieldProps` for this template
   */
  constructor(props) {
    super(props);
    /** Returns the default form information for an item based on the schema for that item. Deals with the possibility
     * that the schema is fixed and allows additional items.
     */
    __publicField(this, "_getNewFormDataRow", () => {
      const { schema, registry } = this.props;
      const { schemaUtils } = registry;
      let itemSchema = schema.items;
      if (isFixedItems(schema) && allowAdditionalItems(schema)) {
        itemSchema = schema.additionalItems;
      }
      return schemaUtils.getDefaultFormState(itemSchema);
    });
    /** Callback handler for when the user clicks on the add button. Creates a new row of keyed form data at the end of
     * the list, adding it into the state, and then returning `onChange()` with the plain form data converted from the
     * keyed data
     *
     * @param event - The event for the click
     */
    __publicField(this, "onAddClick", (event) => {
      this._handleAddClick(event);
    });
    /** Callback handler for when the user clicks on the add button on an existing array element. Creates a new row of
     * keyed form data inserted at the `index`, adding it into the state, and then returning `onChange()` with the plain
     * form data converted from the keyed data
     *
     * @param index - The index at which the add button is clicked
     */
    __publicField(this, "onAddIndexClick", (index) => {
      return (event) => {
        this._handleAddClick(event, index);
      };
    });
    /** Callback handler for when the user clicks on the copy button on an existing array element. Clones the row of
     * keyed form data at the `index` into the next position in the state, and then returning `onChange()` with the plain
     * form data converted from the keyed data
     *
     * @param index - The index at which the copy button is clicked
     */
    __publicField(this, "onCopyIndexClick", (index) => {
      return (event) => {
        if (event) {
          event.preventDefault();
        }
        const { onChange, errorSchema } = this.props;
        const { keyedFormData } = this.state;
        let newErrorSchema;
        if (errorSchema) {
          newErrorSchema = {};
          for (const idx in errorSchema) {
            const i2 = parseInt(idx);
            if (i2 <= index) {
              set_default(newErrorSchema, [i2], errorSchema[idx]);
            } else if (i2 > index) {
              set_default(newErrorSchema, [i2 + 1], errorSchema[idx]);
            }
          }
        }
        const newKeyedFormDataRow = {
          key: generateRowId(),
          item: cloneDeep_default(keyedFormData[index].item)
        };
        const newKeyedFormData = [...keyedFormData];
        if (index !== void 0) {
          newKeyedFormData.splice(index + 1, 0, newKeyedFormDataRow);
        } else {
          newKeyedFormData.push(newKeyedFormDataRow);
        }
        this.setState({
          keyedFormData: newKeyedFormData,
          updatedKeyedFormData: true
        }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));
      };
    });
    /** Callback handler for when the user clicks on the remove button on an existing array element. Removes the row of
     * keyed form data at the `index` in the state, and then returning `onChange()` with the plain form data converted
     * from the keyed data
     *
     * @param index - The index at which the remove button is clicked
     */
    __publicField(this, "onDropIndexClick", (index) => {
      return (event) => {
        if (event) {
          event.preventDefault();
        }
        const { onChange, errorSchema } = this.props;
        const { keyedFormData } = this.state;
        let newErrorSchema;
        if (errorSchema) {
          newErrorSchema = {};
          for (const idx in errorSchema) {
            const i2 = parseInt(idx);
            if (i2 < index) {
              set_default(newErrorSchema, [i2], errorSchema[idx]);
            } else if (i2 > index) {
              set_default(newErrorSchema, [i2 - 1], errorSchema[idx]);
            }
          }
        }
        const newKeyedFormData = keyedFormData.filter((_2, i2) => i2 !== index);
        this.setState({
          keyedFormData: newKeyedFormData,
          updatedKeyedFormData: true
        }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));
      };
    });
    /** Callback handler for when the user clicks on one of the move item buttons on an existing array element. Moves the
     * row of keyed form data at the `index` to the `newIndex` in the state, and then returning `onChange()` with the
     * plain form data converted from the keyed data
     *
     * @param index - The index of the item to move
     * @param newIndex - The index to where the item is to be moved
     */
    __publicField(this, "onReorderClick", (index, newIndex) => {
      return (event) => {
        if (event) {
          event.preventDefault();
          event.currentTarget.blur();
        }
        const { onChange, errorSchema } = this.props;
        let newErrorSchema;
        if (errorSchema) {
          newErrorSchema = {};
          for (const idx in errorSchema) {
            const i2 = parseInt(idx);
            if (i2 == index) {
              set_default(newErrorSchema, [newIndex], errorSchema[index]);
            } else if (i2 == newIndex) {
              set_default(newErrorSchema, [index], errorSchema[newIndex]);
            } else {
              set_default(newErrorSchema, [idx], errorSchema[i2]);
            }
          }
        }
        const { keyedFormData } = this.state;
        function reOrderArray() {
          const _newKeyedFormData = keyedFormData.slice();
          _newKeyedFormData.splice(index, 1);
          _newKeyedFormData.splice(newIndex, 0, keyedFormData[index]);
          return _newKeyedFormData;
        }
        const newKeyedFormData = reOrderArray();
        this.setState({
          keyedFormData: newKeyedFormData
        }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));
      };
    });
    /** Callback handler used to deal with changing the value of the data in the array at the `index`. Calls the
     * `onChange` callback with the updated form data
     *
     * @param index - The index of the item being changed
     */
    __publicField(this, "onChangeForIndex", (index) => {
      return (value, newErrorSchema, id) => {
        const { formData, onChange, errorSchema } = this.props;
        const arrayData = Array.isArray(formData) ? formData : [];
        const newFormData = arrayData.map((item, i2) => {
          const jsonValue = typeof value === "undefined" ? null : value;
          return index === i2 ? jsonValue : item;
        });
        onChange(newFormData, errorSchema && errorSchema && {
          ...errorSchema,
          [index]: newErrorSchema
        }, id);
      };
    });
    /** Callback handler used to change the value for a checkbox */
    __publicField(this, "onSelectChange", (value) => {
      const { onChange, idSchema } = this.props;
      onChange(value, void 0, idSchema && idSchema.$id);
    });
    const { formData = [] } = props;
    const keyedFormData = generateKeyedFormData(formData);
    this.state = {
      keyedFormData,
      updatedKeyedFormData: false
    };
  }
  /** React lifecycle method that is called when the props are about to change allowing the state to be updated. It
   * regenerates the keyed form data and returns it
   *
   * @param nextProps - The next set of props data
   * @param prevState - The previous set of state data
   */
  static getDerivedStateFromProps(nextProps, prevState) {
    if (prevState.updatedKeyedFormData) {
      return {
        updatedKeyedFormData: false
      };
    }
    const nextFormData = Array.isArray(nextProps.formData) ? nextProps.formData : [];
    const previousKeyedFormData = prevState.keyedFormData || [];
    const newKeyedFormData = nextFormData.length === previousKeyedFormData.length ? previousKeyedFormData.map((previousKeyedFormDatum, index) => {
      return {
        key: previousKeyedFormDatum.key,
        item: nextFormData[index]
      };
    }) : generateKeyedFormData(nextFormData);
    return {
      keyedFormData: newKeyedFormData
    };
  }
  /** Returns the appropriate title for an item by getting first the title from the schema.items, then falling back to
   * the description from the schema.items, and finally the string "Item"
   */
  get itemTitle() {
    const { schema, registry } = this.props;
    const { translateString } = registry;
    return get_default(schema, [ITEMS_KEY, "title"], get_default(schema, [ITEMS_KEY, "description"], translateString(TranslatableString.ArrayItemTitle)));
  }
  /** Determines whether the item described in the schema is always required, which is determined by whether any item
   * may be null.
   *
   * @param itemSchema - The schema for the item
   * @return - True if the item schema type does not contain the "null" type
   */
  isItemRequired(itemSchema) {
    if (Array.isArray(itemSchema.type)) {
      return !itemSchema.type.includes("null");
    }
    return itemSchema.type !== "null";
  }
  /** Determines whether more items can be added to the array. If the uiSchema indicates the array doesn't allow adding
   * then false is returned. Otherwise, if the schema indicates that there are a maximum number of items and the
   * `formData` matches that value, then false is returned, otherwise true is returned.
   *
   * @param formItems - The list of items in the form
   * @returns - True if the item is addable otherwise false
   */
  canAddItem(formItems) {
    const { schema, uiSchema, registry } = this.props;
    let { addable } = getUiOptions(uiSchema, registry.globalUiOptions);
    if (addable !== false) {
      if (schema.maxItems !== void 0) {
        addable = formItems.length < schema.maxItems;
      } else {
        addable = true;
      }
    }
    return addable;
  }
  /** Callback handler for when the user clicks on the add or add at index buttons. Creates a new row of keyed form data
   * either at the end of the list (when index is not specified) or inserted at the `index` when it is, adding it into
   * the state, and then returning `onChange()` with the plain form data converted from the keyed data
   *
   * @param event - The event for the click
   * @param [index] - The optional index at which to add the new data
   */
  _handleAddClick(event, index) {
    if (event) {
      event.preventDefault();
    }
    const { onChange, errorSchema } = this.props;
    const { keyedFormData } = this.state;
    let newErrorSchema;
    if (errorSchema) {
      newErrorSchema = {};
      for (const idx in errorSchema) {
        const i2 = parseInt(idx);
        if (index === void 0 || i2 < index) {
          set_default(newErrorSchema, [i2], errorSchema[idx]);
        } else if (i2 >= index) {
          set_default(newErrorSchema, [i2 + 1], errorSchema[idx]);
        }
      }
    }
    const newKeyedFormDataRow = {
      key: generateRowId(),
      item: this._getNewFormDataRow()
    };
    const newKeyedFormData = [...keyedFormData];
    if (index !== void 0) {
      newKeyedFormData.splice(index, 0, newKeyedFormDataRow);
    } else {
      newKeyedFormData.push(newKeyedFormDataRow);
    }
    this.setState({
      keyedFormData: newKeyedFormData,
      updatedKeyedFormData: true
    }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));
  }
  /** Renders the `ArrayField` depending on the specific needs of the schema and uischema elements
   */
  render() {
    const { schema, uiSchema, idSchema, registry } = this.props;
    const { schemaUtils, translateString } = registry;
    if (!(ITEMS_KEY in schema)) {
      const uiOptions = getUiOptions(uiSchema);
      const UnsupportedFieldTemplate = getTemplate("UnsupportedFieldTemplate", registry, uiOptions);
      return (0, import_jsx_runtime.jsx)(UnsupportedFieldTemplate, { schema, idSchema, reason: translateString(TranslatableString.MissingItems), registry });
    }
    if (schemaUtils.isMultiSelect(schema)) {
      return this.renderMultiSelect();
    }
    if (isCustomWidget(uiSchema)) {
      return this.renderCustomWidget();
    }
    if (isFixedItems(schema)) {
      return this.renderFixedArray();
    }
    if (schemaUtils.isFilesArray(schema, uiSchema)) {
      return this.renderFiles();
    }
    return this.renderNormalArray();
  }
  /** Renders a normal array without any limitations of length
   */
  renderNormalArray() {
    const { schema, uiSchema = {}, errorSchema, idSchema, name, title, disabled = false, readonly = false, autofocus = false, required = false, registry, onBlur, onFocus, idPrefix, idSeparator = "_", rawErrors } = this.props;
    const { keyedFormData } = this.state;
    const fieldTitle = schema.title || title || name;
    const { schemaUtils, formContext } = registry;
    const uiOptions = getUiOptions(uiSchema);
    const _schemaItems = isObject_default(schema.items) ? schema.items : {};
    const itemsSchema = schemaUtils.retrieveSchema(_schemaItems);
    const formData = keyedToPlainFormData(this.state.keyedFormData);
    const canAdd = this.canAddItem(formData);
    const arrayProps = {
      canAdd,
      items: keyedFormData.map((keyedItem, index) => {
        const { key, item } = keyedItem;
        const itemCast = item;
        const itemSchema = schemaUtils.retrieveSchema(_schemaItems, itemCast);
        const itemErrorSchema = errorSchema ? errorSchema[index] : void 0;
        const itemIdPrefix = idSchema.$id + idSeparator + index;
        const itemIdSchema = schemaUtils.toIdSchema(itemSchema, itemIdPrefix, itemCast, idPrefix, idSeparator);
        return this.renderArrayFieldItem({
          key,
          index,
          name: name && `${name}-${index}`,
          title: fieldTitle ? `${fieldTitle}-${index + 1}` : void 0,
          canAdd,
          canMoveUp: index > 0,
          canMoveDown: index < formData.length - 1,
          itemSchema,
          itemIdSchema,
          itemErrorSchema,
          itemData: itemCast,
          itemUiSchema: uiSchema.items,
          autofocus: autofocus && index === 0,
          onBlur,
          onFocus,
          rawErrors,
          totalItems: keyedFormData.length
        });
      }),
      className: `field field-array field-array-of-${itemsSchema.type}`,
      disabled,
      idSchema,
      uiSchema,
      onAddClick: this.onAddClick,
      readonly,
      required,
      schema,
      title: fieldTitle,
      formContext,
      formData,
      rawErrors,
      registry
    };
    const Template = getTemplate("ArrayFieldTemplate", registry, uiOptions);
    return (0, import_jsx_runtime.jsx)(Template, { ...arrayProps });
  }
  /** Renders an array using the custom widget provided by the user in the `uiSchema`
   */
  renderCustomWidget() {
    const { schema, idSchema, uiSchema, disabled = false, readonly = false, autofocus = false, required = false, hideError, placeholder, onBlur, onFocus, formData: items = [], registry, rawErrors, name } = this.props;
    const { widgets: widgets2, formContext, globalUiOptions, schemaUtils } = registry;
    const { widget, title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);
    const Widget = getWidget(schema, widget, widgets2);
    const label = uiTitle ?? schema.title ?? name;
    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);
    return (0, import_jsx_runtime.jsx)(Widget, { id: idSchema.$id, name, multiple: true, onChange: this.onSelectChange, onBlur, onFocus, options, schema, uiSchema, registry, value: items, disabled, readonly, hideError, required, label, hideLabel: !displayLabel, placeholder, formContext, autofocus, rawErrors });
  }
  /** Renders an array as a set of checkboxes
   */
  renderMultiSelect() {
    const { schema, idSchema, uiSchema, formData: items = [], disabled = false, readonly = false, autofocus = false, required = false, placeholder, onBlur, onFocus, registry, rawErrors, name } = this.props;
    const { widgets: widgets2, schemaUtils, formContext, globalUiOptions } = registry;
    const itemsSchema = schemaUtils.retrieveSchema(schema.items, items);
    const enumOptions = optionsList(itemsSchema, uiSchema);
    const { widget = "select", title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);
    const Widget = getWidget(schema, widget, widgets2);
    const label = uiTitle ?? schema.title ?? name;
    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);
    return (0, import_jsx_runtime.jsx)(Widget, { id: idSchema.$id, name, multiple: true, onChange: this.onSelectChange, onBlur, onFocus, options: { ...options, enumOptions }, schema, uiSchema, registry, value: items, disabled, readonly, required, label, hideLabel: !displayLabel, placeholder, formContext, autofocus, rawErrors });
  }
  /** Renders an array of files using the `FileWidget`
   */
  renderFiles() {
    const { schema, uiSchema, idSchema, name, disabled = false, readonly = false, autofocus = false, required = false, onBlur, onFocus, registry, formData: items = [], rawErrors } = this.props;
    const { widgets: widgets2, formContext, globalUiOptions, schemaUtils } = registry;
    const { widget = "files", title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);
    const Widget = getWidget(schema, widget, widgets2);
    const label = uiTitle ?? schema.title ?? name;
    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);
    return (0, import_jsx_runtime.jsx)(Widget, { options, id: idSchema.$id, name, multiple: true, onChange: this.onSelectChange, onBlur, onFocus, schema, uiSchema, value: items, disabled, readonly, required, registry, formContext, autofocus, rawErrors, label, hideLabel: !displayLabel });
  }
  /** Renders an array that has a maximum limit of items
   */
  renderFixedArray() {
    const { schema, uiSchema = {}, formData = [], errorSchema, idPrefix, idSeparator = "_", idSchema, name, title, disabled = false, readonly = false, autofocus = false, required = false, registry, onBlur, onFocus, rawErrors } = this.props;
    const { keyedFormData } = this.state;
    let { formData: items = [] } = this.props;
    const fieldTitle = schema.title || title || name;
    const uiOptions = getUiOptions(uiSchema);
    const { schemaUtils, formContext } = registry;
    const _schemaItems = isObject_default(schema.items) ? schema.items : [];
    const itemSchemas = _schemaItems.map((item, index) => schemaUtils.retrieveSchema(item, formData[index]));
    const additionalSchema = isObject_default(schema.additionalItems) ? schemaUtils.retrieveSchema(schema.additionalItems, formData) : null;
    if (!items || items.length < itemSchemas.length) {
      items = items || [];
      items = items.concat(new Array(itemSchemas.length - items.length));
    }
    const canAdd = this.canAddItem(items) && !!additionalSchema;
    const arrayProps = {
      canAdd,
      className: "field field-array field-array-fixed-items",
      disabled,
      idSchema,
      formData,
      items: keyedFormData.map((keyedItem, index) => {
        const { key, item } = keyedItem;
        const itemCast = item;
        const additional = index >= itemSchemas.length;
        const itemSchema = (additional && isObject_default(schema.additionalItems) ? schemaUtils.retrieveSchema(schema.additionalItems, itemCast) : itemSchemas[index]) || {};
        const itemIdPrefix = idSchema.$id + idSeparator + index;
        const itemIdSchema = schemaUtils.toIdSchema(itemSchema, itemIdPrefix, itemCast, idPrefix, idSeparator);
        const itemUiSchema = additional ? uiSchema.additionalItems || {} : Array.isArray(uiSchema.items) ? uiSchema.items[index] : uiSchema.items || {};
        const itemErrorSchema = errorSchema ? errorSchema[index] : void 0;
        return this.renderArrayFieldItem({
          key,
          index,
          name: name && `${name}-${index}`,
          title: fieldTitle ? `${fieldTitle}-${index + 1}` : void 0,
          canAdd,
          canRemove: additional,
          canMoveUp: index >= itemSchemas.length + 1,
          canMoveDown: additional && index < items.length - 1,
          itemSchema,
          itemData: itemCast,
          itemUiSchema,
          itemIdSchema,
          itemErrorSchema,
          autofocus: autofocus && index === 0,
          onBlur,
          onFocus,
          rawErrors,
          totalItems: keyedFormData.length
        });
      }),
      onAddClick: this.onAddClick,
      readonly,
      required,
      registry,
      schema,
      uiSchema,
      title: fieldTitle,
      formContext,
      errorSchema,
      rawErrors
    };
    const Template = getTemplate("ArrayFieldTemplate", registry, uiOptions);
    return (0, import_jsx_runtime.jsx)(Template, { ...arrayProps });
  }
  /** Renders the individual array item using a `SchemaField` along with the additional properties required to be send
   * back to the `ArrayFieldItemTemplate`.
   *
   * @param props - The props for the individual array item to be rendered
   */
  renderArrayFieldItem(props) {
    const { key, index, name, canAdd, canRemove = true, canMoveUp, canMoveDown, itemSchema, itemData, itemUiSchema, itemIdSchema, itemErrorSchema, autofocus, onBlur, onFocus, rawErrors, totalItems, title } = props;
    const { disabled, hideError, idPrefix, idSeparator, readonly, uiSchema, registry, formContext } = this.props;
    const { fields: { ArraySchemaField, SchemaField: SchemaField2 }, globalUiOptions } = registry;
    const ItemSchemaField = ArraySchemaField || SchemaField2;
    const { orderable = true, removable = true, copyable = false } = getUiOptions(uiSchema, globalUiOptions);
    const has = {
      moveUp: orderable && canMoveUp,
      moveDown: orderable && canMoveDown,
      copy: copyable && canAdd,
      remove: removable && canRemove,
      toolbar: false
    };
    has.toolbar = Object.keys(has).some((key2) => has[key2]);
    return {
      children: (0, import_jsx_runtime.jsx)(ItemSchemaField, { name, title, index, schema: itemSchema, uiSchema: itemUiSchema, formData: itemData, formContext, errorSchema: itemErrorSchema, idPrefix, idSeparator, idSchema: itemIdSchema, required: this.isItemRequired(itemSchema), onChange: this.onChangeForIndex(index), onBlur, onFocus, registry, disabled, readonly, hideError, autofocus, rawErrors }),
      className: "array-item",
      disabled,
      canAdd,
      hasCopy: has.copy,
      hasToolbar: has.toolbar,
      hasMoveUp: has.moveUp,
      hasMoveDown: has.moveDown,
      hasRemove: has.remove,
      index,
      totalItems,
      key,
      onAddIndexClick: this.onAddIndexClick,
      onCopyIndexClick: this.onCopyIndexClick,
      onDropIndexClick: this.onDropIndexClick,
      onReorderClick: this.onReorderClick,
      readonly,
      registry,
      schema: itemSchema,
      uiSchema: itemUiSchema
    };
  }
};
var ArrayField_default = ArrayField;

// node_modules/@rjsf/core/lib/components/fields/BooleanField.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
function BooleanField(props) {
  const { schema, name, uiSchema, idSchema, formData, registry, required, disabled, readonly, hideError, autofocus, title, onChange, onFocus, onBlur, rawErrors } = props;
  const { title: schemaTitle } = schema;
  const { widgets: widgets2, formContext, translateString, globalUiOptions } = registry;
  const {
    widget = "checkbox",
    title: uiTitle,
    // Unlike the other fields, don't use `getDisplayLabel()` since it always returns false for the boolean type
    label: displayLabel = true,
    ...options
  } = getUiOptions(uiSchema, globalUiOptions);
  const Widget = getWidget(schema, widget, widgets2);
  const yes = translateString(TranslatableString.YesLabel);
  const no = translateString(TranslatableString.NoLabel);
  let enumOptions;
  const label = uiTitle ?? schemaTitle ?? title ?? name;
  if (Array.isArray(schema.oneOf)) {
    enumOptions = optionsList({
      oneOf: schema.oneOf.map((option) => {
        if (isObject_default(option)) {
          return {
            ...option,
            title: option.title || (option.const === true ? yes : no)
          };
        }
        return void 0;
      }).filter((o2) => o2)
      // cast away the error that typescript can't grok is fixed
    }, uiSchema);
  } else {
    const schemaWithEnumNames = schema;
    const enums = schema.enum ?? [true, false];
    if (!schemaWithEnumNames.enumNames && enums.length === 2 && enums.every((v2) => typeof v2 === "boolean")) {
      enumOptions = [
        {
          value: enums[0],
          label: enums[0] ? yes : no
        },
        {
          value: enums[1],
          label: enums[1] ? yes : no
        }
      ];
    } else {
      enumOptions = optionsList({
        enum: enums,
        // NOTE: enumNames is deprecated, but still supported for now.
        enumNames: schemaWithEnumNames.enumNames
      }, uiSchema);
    }
  }
  return (0, import_jsx_runtime2.jsx)(Widget, { options: { ...options, enumOptions }, schema, uiSchema, id: idSchema.$id, name, onChange, onFocus, onBlur, label, hideLabel: !displayLabel, value: formData, required, disabled, readonly, hideError, registry, formContext, autofocus, rawErrors });
}
var BooleanField_default = BooleanField;

// node_modules/@rjsf/core/lib/components/fields/MultiSchemaField.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var import_react2 = __toESM(require_react());
var AnyOfField = class extends import_react2.Component {
  /** Constructs an `AnyOfField` with the given `props` to initialize the initially selected option in state
   *
   * @param props - The `FieldProps` for this template
   */
  constructor(props) {
    super(props);
    /** Callback handler to remember what the currently selected option is. In addition to that the `formData` is updated
     * to remove properties that are not part of the newly selected option schema, and then the updated data is passed to
     * the `onChange` handler.
     *
     * @param option - The new option value being selected
     */
    __publicField(this, "onOptionChange", (option) => {
      const { selectedOption, retrievedOptions } = this.state;
      const { formData, onChange, registry } = this.props;
      const { schemaUtils } = registry;
      const intOption = option !== void 0 ? parseInt(option, 10) : -1;
      if (intOption === selectedOption) {
        return;
      }
      const newOption = intOption >= 0 ? retrievedOptions[intOption] : void 0;
      const oldOption = selectedOption >= 0 ? retrievedOptions[selectedOption] : void 0;
      let newFormData = schemaUtils.sanitizeDataForNewSchema(newOption, oldOption, formData);
      if (newOption) {
        newFormData = schemaUtils.getDefaultFormState(newOption, newFormData, "excludeObjectChildren");
      }
      this.setState({ selectedOption: intOption }, () => {
        onChange(newFormData, void 0, this.getFieldId());
      });
    });
    const { formData, options, registry: { schemaUtils } } = this.props;
    const retrievedOptions = options.map((opt) => schemaUtils.retrieveSchema(opt, formData));
    this.state = {
      retrievedOptions,
      selectedOption: this.getMatchingOption(0, formData, retrievedOptions)
    };
  }
  /** React lifecycle method that is called when the props and/or state for this component is updated. It recomputes the
   * currently selected option based on the overall `formData`
   *
   * @param prevProps - The previous `FieldProps` for this template
   * @param prevState - The previous `AnyOfFieldState` for this template
   */
  componentDidUpdate(prevProps, prevState) {
    const { formData, options, idSchema } = this.props;
    const { selectedOption } = this.state;
    let newState = this.state;
    if (!deepEquals(prevProps.options, options)) {
      const { registry: { schemaUtils } } = this.props;
      const retrievedOptions = options.map((opt) => schemaUtils.retrieveSchema(opt, formData));
      newState = { selectedOption, retrievedOptions };
    }
    if (!deepEquals(formData, prevProps.formData) && idSchema.$id === prevProps.idSchema.$id) {
      const { retrievedOptions } = newState;
      const matchingOption = this.getMatchingOption(selectedOption, formData, retrievedOptions);
      if (prevState && matchingOption !== selectedOption) {
        newState = { selectedOption: matchingOption, retrievedOptions };
      }
    }
    if (newState !== this.state) {
      this.setState(newState);
    }
  }
  /** Determines the best matching option for the given `formData` and `options`.
   *
   * @param formData - The new formData
   * @param options - The list of options to choose from
   * @return - The index of the `option` that best matches the `formData`
   */
  getMatchingOption(selectedOption, formData, options) {
    const { schema, registry: { schemaUtils } } = this.props;
    const discriminator = getDiscriminatorFieldFromSchema(schema);
    const option = schemaUtils.getClosestMatchingOption(formData, options, selectedOption, discriminator);
    return option;
  }
  getFieldId() {
    const { idSchema, schema } = this.props;
    return `${idSchema.$id}${schema.oneOf ? "__oneof_select" : "__anyof_select"}`;
  }
  /** Renders the `AnyOfField` selector along with a `SchemaField` for the value of the `formData`
   */
  render() {
    const { name, disabled = false, errorSchema = {}, formContext, onBlur, onFocus, readonly, registry, schema, uiSchema } = this.props;
    const { widgets: widgets2, fields: fields2, translateString, globalUiOptions, schemaUtils } = registry;
    const { SchemaField: _SchemaField } = fields2;
    const { selectedOption, retrievedOptions } = this.state;
    const { widget = "select", placeholder, autofocus, autocomplete, title = schema.title, ...uiOptions } = getUiOptions(uiSchema, globalUiOptions);
    const Widget = getWidget({ type: "number" }, widget, widgets2);
    const rawErrors = get_default(errorSchema, ERRORS_KEY, []);
    const fieldErrorSchema = omit_default(errorSchema, [ERRORS_KEY]);
    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);
    const option = selectedOption >= 0 ? retrievedOptions[selectedOption] || null : null;
    let optionSchema;
    if (option) {
      const { required } = schema;
      optionSchema = required ? mergeSchemas({ required }, option) : option;
    }
    let optionsUiSchema = [];
    if (ONE_OF_KEY in schema && uiSchema && ONE_OF_KEY in uiSchema) {
      if (Array.isArray(uiSchema[ONE_OF_KEY])) {
        optionsUiSchema = uiSchema[ONE_OF_KEY];
      } else {
        console.warn(`uiSchema.oneOf is not an array for "${title || name}"`);
      }
    } else if (ANY_OF_KEY in schema && uiSchema && ANY_OF_KEY in uiSchema) {
      if (Array.isArray(uiSchema[ANY_OF_KEY])) {
        optionsUiSchema = uiSchema[ANY_OF_KEY];
      } else {
        console.warn(`uiSchema.anyOf is not an array for "${title || name}"`);
      }
    }
    let optionUiSchema = uiSchema;
    if (selectedOption >= 0 && optionsUiSchema.length > selectedOption) {
      optionUiSchema = optionsUiSchema[selectedOption];
    }
    const translateEnum = title ? TranslatableString.TitleOptionPrefix : TranslatableString.OptionPrefix;
    const translateParams = title ? [title] : [];
    const enumOptions = retrievedOptions.map((opt, index) => {
      const { title: uiTitle = opt.title } = getUiOptions(optionsUiSchema[index]);
      return {
        label: uiTitle || translateString(translateEnum, translateParams.concat(String(index + 1))),
        value: index
      };
    });
    return (0, import_jsx_runtime3.jsxs)("div", { className: "panel panel-default panel-body", children: [(0, import_jsx_runtime3.jsx)("div", { className: "form-group", children: (0, import_jsx_runtime3.jsx)(Widget, { id: this.getFieldId(), name: `${name}${schema.oneOf ? "__oneof_select" : "__anyof_select"}`, schema: { type: "number", default: 0 }, onChange: this.onOptionChange, onBlur, onFocus, disabled: disabled || isEmpty_default(enumOptions), multiple: false, rawErrors, errorSchema: fieldErrorSchema, value: selectedOption >= 0 ? selectedOption : void 0, options: { enumOptions, ...uiOptions }, registry, formContext, placeholder, autocomplete, autofocus, label: title ?? name, hideLabel: !displayLabel, readonly }) }), optionSchema && (0, import_jsx_runtime3.jsx)(_SchemaField, { ...this.props, schema: optionSchema, uiSchema: optionUiSchema })] });
  }
};
var MultiSchemaField_default = AnyOfField;

// node_modules/@rjsf/core/lib/components/fields/NumberField.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var import_react3 = __toESM(require_react());
var trailingCharMatcherWithPrefix = /\.([0-9]*0)*$/;
var trailingCharMatcher = /[0.]0*$/;
function NumberField(props) {
  const { registry, onChange, formData, value: initialValue } = props;
  const [lastValue, setLastValue] = (0, import_react3.useState)(initialValue);
  const { StringField: StringField2 } = registry.fields;
  let value = formData;
  const handleChange = (0, import_react3.useCallback)((value2, errorSchema, id) => {
    setLastValue(value2);
    if (`${value2}`.charAt(0) === ".") {
      value2 = `0${value2}`;
    }
    const processed = typeof value2 === "string" && value2.match(trailingCharMatcherWithPrefix) ? asNumber(value2.replace(trailingCharMatcher, "")) : asNumber(value2);
    onChange(processed, errorSchema, id);
  }, [onChange]);
  if (typeof lastValue === "string" && typeof value === "number") {
    const re2 = new RegExp(`^(${String(value).replace(".", "\\.")})?\\.?0*$`);
    if (lastValue.match(re2)) {
      value = lastValue;
    }
  }
  return (0, import_jsx_runtime4.jsx)(StringField2, { ...props, formData: value, onChange: handleChange });
}
var NumberField_default = NumberField;

// node_modules/@rjsf/core/lib/components/fields/ObjectField.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var import_react4 = __toESM(require_react());

// node_modules/markdown-to-jsx/dist/index.modern.js
var e = __toESM(require_react());
function t() {
  return t = Object.assign ? Object.assign.bind() : function(e2) {
    for (var t2 = 1; t2 < arguments.length; t2++) {
      var n2 = arguments[t2];
      for (var r2 in n2) Object.prototype.hasOwnProperty.call(n2, r2) && (e2[r2] = n2[r2]);
    }
    return e2;
  }, t.apply(this, arguments);
}
var n = ["children", "options"];
var r = { blockQuote: "0", breakLine: "1", breakThematic: "2", codeBlock: "3", codeFenced: "4", codeInline: "5", footnote: "6", footnoteReference: "7", gfmTask: "8", heading: "9", headingSetext: "10", htmlBlock: "11", htmlComment: "12", htmlSelfClosing: "13", image: "14", link: "15", linkAngleBraceStyleDetector: "16", linkBareUrlDetector: "17", linkMailtoDetector: "18", newlineCoalescer: "19", orderedList: "20", paragraph: "21", ref: "22", refImage: "23", refLink: "24", table: "25", tableSeparator: "26", text: "27", textBolded: "28", textEmphasized: "29", textEscaped: "30", textMarked: "31", textStrikethroughed: "32", unorderedList: "33" };
var i;
!function(e2) {
  e2[e2.MAX = 0] = "MAX", e2[e2.HIGH = 1] = "HIGH", e2[e2.MED = 2] = "MED", e2[e2.LOW = 3] = "LOW", e2[e2.MIN = 4] = "MIN";
}(i || (i = {}));
var l = ["allowFullScreen", "allowTransparency", "autoComplete", "autoFocus", "autoPlay", "cellPadding", "cellSpacing", "charSet", "classId", "colSpan", "contentEditable", "contextMenu", "crossOrigin", "encType", "formAction", "formEncType", "formMethod", "formNoValidate", "formTarget", "frameBorder", "hrefLang", "inputMode", "keyParams", "keyType", "marginHeight", "marginWidth", "maxLength", "mediaGroup", "minLength", "noValidate", "radioGroup", "readOnly", "rowSpan", "spellCheck", "srcDoc", "srcLang", "srcSet", "tabIndex", "useMap"].reduce((e2, t2) => (e2[t2.toLowerCase()] = t2, e2), { class: "className", for: "htmlFor" });
var a = { amp: "&", apos: "'", gt: ">", lt: "<", nbsp: " ", quot: "“" };
var o = ["style", "script"];
var c = /([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi;
var s = /mailto:/i;
var d = /\n{2,}$/;
var u = /^(\s*>[\s\S]*?)(?=\n\n|$)/;
var p = /^ *> ?/gm;
var f = /^(?:\[!([^\]]*)\]\n)?([\s\S]*)/;
var h = /^ {2,}\n/;
var m = /^(?:( *[-*_])){3,} *(?:\n *)+\n/;
var g = /^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/;
var y = /^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/;
var k = /^(`+)((?:\\`|[^`])+)\1/;
var x = /^(?:\n *)*\n/;
var b = /\r\n?/g;
var v = /^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/;
var C = /^\[\^([^\]]+)]/;
var $ = /\f/g;
var S = /^---[ \t]*\n(.|\n)*\n---[ \t]*\n/;
var w = /^\s*?\[(x|\s)\]/;
var E = /^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/;
var z = /^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/;
var L = /^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/;
var A = /^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i;
var T = /&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi;
var B = /^<!--[\s\S]*?(?:-->)/;
var O = /^(data|aria|x)-[a-z_][a-z\d_.-]*$/;
var M = /^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i;
var R = /^\{.*\}$/;
var I = /^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/;
var U = /^<([^ >]+@[^ >]+)>/;
var D = /^<([^ >]+:\/[^ >]+)>/;
var N = /-([a-z])?/gi;
var j = /^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/;
var H = /^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/;
var P = /^!\[([^\]]*)\] ?\[([^\]]*)\]/;
var _ = /^\[([^\]]*)\] ?\[([^\]]*)\]/;
var F = /(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/;
var G = /\t/g;
var W = /(^ *\||\| *$)/g;
var Z = /^ *:-+: *$/;
var q = /^ *:-+ *$/;
var Q = /^ *-+: *$/;
var V = "((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)";
var X = new RegExp(`^([*_])\\1${V}\\1\\1(?!\\1)`);
var J = new RegExp(`^([*_])${V}\\1(?!\\1)`);
var K = new RegExp(`^(==)${V}\\1`);
var Y = new RegExp(`^(~~)${V}\\1`);
var ee = /^\\([^0-9A-Za-z\s])/;
var te = /\\([^0-9A-Za-z\s])/g;
var ne = /^([\s\S](?:(?!  |[0-9]\.)[^*_~\-\n<`\\\[!])*)/;
var re = /^\n+/;
var ie = /^([ \t]*)/;
var le = /\\([^\\])/g;
var ae = /(?:^|\n)( *)$/;
var oe = "(?:\\d+\\.)";
var ce = "(?:[*+-])";
function se(e2) {
  return "( *)(" + (1 === e2 ? oe : ce) + ") +";
}
var de = se(1);
var ue = se(2);
function pe(e2) {
  return new RegExp("^" + (1 === e2 ? de : ue));
}
var fe = pe(1);
var he = pe(2);
function me(e2) {
  return new RegExp("^" + (1 === e2 ? de : ue) + "[^\\n]*(?:\\n(?!\\1" + (1 === e2 ? oe : ce) + " )[^\\n]*)*(\\n|$)", "gm");
}
var ge = me(1);
var ye = me(2);
function ke(e2) {
  const t2 = 1 === e2 ? oe : ce;
  return new RegExp("^( *)(" + t2 + ") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1" + t2 + " (?!" + t2 + " ))\\n*|\\s*\\n*$)");
}
var xe = ke(1);
var be = ke(2);
function ve(e2, t2) {
  const n2 = 1 === t2, i2 = n2 ? xe : be, l2 = n2 ? ge : ye, a2 = n2 ? fe : he;
  return { match: Oe(function(e3, t3) {
    const n3 = ae.exec(t3.prevCapture);
    return n3 && (t3.list || !t3.inline && !t3.simple) ? i2.exec(e3 = n3[1] + e3) : null;
  }), order: 1, parse(e3, t3, r2) {
    const i3 = n2 ? +e3[2] : void 0, o2 = e3[0].replace(d, "\n").match(l2);
    let c2 = false;
    return { items: o2.map(function(e4, n3) {
      const i4 = a2.exec(e4)[0].length, l3 = new RegExp("^ {1," + i4 + "}", "gm"), s2 = e4.replace(l3, "").replace(a2, ""), d2 = n3 === o2.length - 1, u2 = -1 !== s2.indexOf("\n\n") || d2 && c2;
      c2 = u2;
      const p2 = r2.inline, f2 = r2.list;
      let h2;
      r2.list = true, u2 ? (r2.inline = false, h2 = Ee(s2) + "\n\n") : (r2.inline = true, h2 = Ee(s2));
      const m2 = t3(h2, r2);
      return r2.inline = p2, r2.list = f2, m2;
    }), ordered: n2, start: i3 };
  }, render: (t3, n3, i3) => e2(t3.ordered ? "ol" : "ul", { key: i3.key, start: t3.type === r.orderedList ? t3.start : void 0 }, t3.items.map(function(t4, r2) {
    return e2("li", { key: r2 }, n3(t4, i3));
  })) };
}
var Ce = new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`);
var $e = /^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;
var Se = [u, g, y, E, L, z, j, xe, be];
var we = [...Se, /^[^\n]+(?:  \n|\n{2,})/, A, B, M];
function Ee(e2) {
  let t2 = e2.length;
  for (; t2 > 0 && e2[t2 - 1] <= " "; ) t2--;
  return e2.slice(0, t2);
}
function ze(e2) {
  return e2.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g, "a").replace(/[çÇ]/g, "c").replace(/[ðÐ]/g, "d").replace(/[ÈÉÊËéèêë]/g, "e").replace(/[ÏïÎîÍíÌì]/g, "i").replace(/[Ññ]/g, "n").replace(/[øØœŒÕõÔôÓóÒò]/g, "o").replace(/[ÜüÛûÚúÙù]/g, "u").replace(/[ŸÿÝý]/g, "y").replace(/[^a-z0-9- ]/gi, "").replace(/ /gi, "-").toLowerCase();
}
function Le(e2) {
  return Q.test(e2) ? "right" : Z.test(e2) ? "center" : q.test(e2) ? "left" : null;
}
function Ae(e2, t2, n2, r2) {
  const i2 = n2.inTable;
  n2.inTable = true;
  let l2 = [[]], a2 = "";
  function o2() {
    if (!a2) return;
    const e3 = l2[l2.length - 1];
    e3.push.apply(e3, t2(a2, n2)), a2 = "";
  }
  return e2.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((e3, t3, n3) => {
    "|" === e3.trim() && (o2(), r2) ? 0 !== t3 && t3 !== n3.length - 1 && l2.push([]) : a2 += e3;
  }), o2(), n2.inTable = i2, l2;
}
function Te(e2, t2, n2) {
  n2.inline = true;
  const i2 = e2[2] ? e2[2].replace(W, "").split("|").map(Le) : [], l2 = e2[3] ? function(e3, t3, n3) {
    return e3.trim().split("\n").map(function(e4) {
      return Ae(e4, t3, n3, true);
    });
  }(e2[3], t2, n2) : [], a2 = Ae(e2[1], t2, n2, !!l2.length);
  return n2.inline = false, l2.length ? { align: i2, cells: l2, header: a2, type: r.table } : { children: a2, type: r.paragraph };
}
function Be(e2, t2) {
  return null == e2.align[t2] ? {} : { textAlign: e2.align[t2] };
}
function Oe(e2) {
  return e2.inline = 1, e2;
}
function Me(e2) {
  return Oe(function(t2, n2) {
    return n2.inline ? e2.exec(t2) : null;
  });
}
function Re(e2) {
  return Oe(function(t2, n2) {
    return n2.inline || n2.simple ? e2.exec(t2) : null;
  });
}
function Ie(e2) {
  return function(t2, n2) {
    return n2.inline || n2.simple ? null : e2.exec(t2);
  };
}
function Ue(e2) {
  return Oe(function(t2) {
    return e2.exec(t2);
  });
}
function De(e2, t2) {
  if (t2.inline || t2.simple) return null;
  let n2 = "";
  e2.split("\n").every((e3) => (e3 += "\n", !Se.some((t3) => t3.test(e3)) && (n2 += e3, !!e3.trim())));
  const r2 = Ee(n2);
  return "" == r2 ? null : [n2, , r2];
}
function Ne(e2) {
  try {
    if (decodeURIComponent(e2).replace(/[^A-Za-z0-9/:]/g, "").match(/^\s*(javascript|vbscript|data(?!:image)):/i)) return null;
  } catch (e3) {
    return null;
  }
  return e2;
}
function je(e2) {
  return e2.replace(le, "$1");
}
function He(e2, t2, n2) {
  const r2 = n2.inline || false, i2 = n2.simple || false;
  n2.inline = true, n2.simple = true;
  const l2 = e2(t2, n2);
  return n2.inline = r2, n2.simple = i2, l2;
}
function Pe(e2, t2, n2) {
  const r2 = n2.inline || false, i2 = n2.simple || false;
  n2.inline = false, n2.simple = true;
  const l2 = e2(t2, n2);
  return n2.inline = r2, n2.simple = i2, l2;
}
function _e(e2, t2, n2) {
  const r2 = n2.inline || false;
  n2.inline = false;
  const i2 = e2(t2, n2);
  return n2.inline = r2, i2;
}
var Fe = (e2, t2, n2) => ({ children: He(t2, e2[2], n2) });
function Ge() {
  return {};
}
function We() {
  return null;
}
function Ze(...e2) {
  return e2.filter(Boolean).join(" ");
}
function qe(e2, t2, n2) {
  let r2 = e2;
  const i2 = t2.split(".");
  for (; i2.length && (r2 = r2[i2[0]], void 0 !== r2); ) i2.shift();
  return r2 || n2;
}
function Qe(n2 = "", i2 = {}) {
  function d2(e2, n3, ...r2) {
    const l2 = qe(i2.overrides, `${e2}.props`, {});
    return i2.createElement(function(e3, t2) {
      const n4 = qe(t2, e3);
      return n4 ? "function" == typeof n4 || "object" == typeof n4 && "render" in n4 ? n4 : qe(t2, `${e3}.component`, e3) : e3;
    }(e2, i2.overrides), t({}, n3, l2, { className: Ze(null == n3 ? void 0 : n3.className, l2.className) || void 0 }), ...r2);
  }
  function W2(e2) {
    e2 = e2.replace(S, "");
    let t2 = false;
    i2.forceInline ? t2 = true : i2.forceBlock || (t2 = false === F.test(e2));
    const n3 = ae2(le2(t2 ? e2 : `${Ee(e2).replace(re, "")}

`, { inline: t2 }));
    for (; "string" == typeof n3[n3.length - 1] && !n3[n3.length - 1].trim(); ) n3.pop();
    if (null === i2.wrapper) return n3;
    const r2 = i2.wrapper || (t2 ? "span" : "div");
    let l2;
    if (n3.length > 1 || i2.forceWrapper) l2 = n3;
    else {
      if (1 === n3.length) return l2 = n3[0], "string" == typeof l2 ? d2("span", { key: "outer" }, l2) : l2;
      l2 = null;
    }
    return i2.createElement(r2, { key: "outer" }, l2);
  }
  function Z2(e2, t2) {
    const n3 = t2.match(c);
    return n3 ? n3.reduce(function(t3, n4) {
      const r2 = n4.indexOf("=");
      if (-1 !== r2) {
        const a2 = function(e3) {
          return -1 !== e3.indexOf("-") && null === e3.match(O) && (e3 = e3.replace(N, function(e4, t4) {
            return t4.toUpperCase();
          })), e3;
        }(n4.slice(0, r2)).trim(), o2 = function(e3) {
          const t4 = e3[0];
          return ('"' === t4 || "'" === t4) && e3.length >= 2 && e3[e3.length - 1] === t4 ? e3.slice(1, -1) : e3;
        }(n4.slice(r2 + 1).trim()), c2 = l[a2] || a2;
        if ("ref" === c2) return t3;
        const s2 = t3[c2] = function(e3, t4, n5, r3) {
          return "style" === t4 ? n5.split(/;\s?/).reduce(function(e4, t5) {
            const n6 = t5.slice(0, t5.indexOf(":"));
            return e4[n6.trim().replace(/(-[a-z])/g, (e5) => e5[1].toUpperCase())] = t5.slice(n6.length + 1).trim(), e4;
          }, {}) : "href" === t4 || "src" === t4 ? r3(n5, e3, t4) : (n5.match(R) && (n5 = n5.slice(1, n5.length - 1)), "true" === n5 || "false" !== n5 && n5);
        }(e2, a2, o2, i2.sanitizer);
        "string" == typeof s2 && (A.test(s2) || M.test(s2)) && (t3[c2] = W2(s2.trim()));
      } else "style" !== n4 && (t3[l[n4] || n4] = true);
      return t3;
    }, {}) : null;
  }
  i2.overrides = i2.overrides || {}, i2.sanitizer = i2.sanitizer || Ne, i2.slugify = i2.slugify || ze, i2.namedCodesToUnicode = i2.namedCodesToUnicode ? t({}, a, i2.namedCodesToUnicode) : a, i2.createElement = i2.createElement || e.createElement;
  const q2 = [], Q2 = {}, V2 = { [r.blockQuote]: { match: Ie(u), order: 1, parse(e2, t2, n3) {
    const [, r2, i3] = e2[0].replace(p, "").match(f);
    return { alert: r2, children: t2(i3, n3) };
  }, render(e2, t2, n3) {
    const l2 = { key: n3.key };
    return e2.alert && (l2.className = "markdown-alert-" + i2.slugify(e2.alert.toLowerCase(), ze), e2.children.unshift({ attrs: {}, children: [{ type: r.text, text: e2.alert }], noInnerParse: true, type: r.htmlBlock, tag: "header" })), d2("blockquote", l2, t2(e2.children, n3));
  } }, [r.breakLine]: { match: Ue(h), order: 1, parse: Ge, render: (e2, t2, n3) => d2("br", { key: n3.key }) }, [r.breakThematic]: { match: Ie(m), order: 1, parse: Ge, render: (e2, t2, n3) => d2("hr", { key: n3.key }) }, [r.codeBlock]: { match: Ie(y), order: 0, parse: (e2) => ({ lang: void 0, text: Ee(e2[0].replace(/^ {4}/gm, "")).replace(te, "$1") }), render: (e2, n3, r2) => d2("pre", { key: r2.key }, d2("code", t({}, e2.attrs, { className: e2.lang ? `lang-${e2.lang}` : "" }), e2.text)) }, [r.codeFenced]: { match: Ie(g), order: 0, parse: (e2) => ({ attrs: Z2("code", e2[3] || ""), lang: e2[2] || void 0, text: e2[4].replace(te, "$1"), type: r.codeBlock }) }, [r.codeInline]: { match: Re(k), order: 3, parse: (e2) => ({ text: e2[2].replace(te, "$1") }), render: (e2, t2, n3) => d2("code", { key: n3.key }, e2.text) }, [r.footnote]: { match: Ie(v), order: 0, parse: (e2) => (q2.push({ footnote: e2[2], identifier: e2[1] }), {}), render: We }, [r.footnoteReference]: { match: Me(C), order: 1, parse: (e2) => ({ target: `#${i2.slugify(e2[1], ze)}`, text: e2[1] }), render: (e2, t2, n3) => d2("a", { key: n3.key, href: i2.sanitizer(e2.target, "a", "href") }, d2("sup", { key: n3.key }, e2.text)) }, [r.gfmTask]: { match: Me(w), order: 1, parse: (e2) => ({ completed: "x" === e2[1].toLowerCase() }), render: (e2, t2, n3) => d2("input", { checked: e2.completed, key: n3.key, readOnly: true, type: "checkbox" }) }, [r.heading]: { match: Ie(i2.enforceAtxHeadings ? z : E), order: 1, parse: (e2, t2, n3) => ({ children: He(t2, e2[2], n3), id: i2.slugify(e2[2], ze), level: e2[1].length }), render: (e2, t2, n3) => d2(`h${e2.level}`, { id: e2.id, key: n3.key }, t2(e2.children, n3)) }, [r.headingSetext]: { match: Ie(L), order: 0, parse: (e2, t2, n3) => ({ children: He(t2, e2[1], n3), level: "=" === e2[2] ? 1 : 2, type: r.heading }) }, [r.htmlBlock]: { match: Ue(A), order: 1, parse(e2, t2, n3) {
    const [, r2] = e2[3].match(ie), i3 = new RegExp(`^${r2}`, "gm"), l2 = e2[3].replace(i3, ""), a2 = (c2 = l2, we.some((e3) => e3.test(c2)) ? _e : He);
    var c2;
    const s2 = e2[1].toLowerCase(), d3 = -1 !== o.indexOf(s2), u2 = (d3 ? s2 : e2[1]).trim(), p2 = { attrs: Z2(u2, e2[2]), noInnerParse: d3, tag: u2 };
    return n3.inAnchor = n3.inAnchor || "a" === s2, d3 ? p2.text = e2[3] : p2.children = a2(t2, l2, n3), n3.inAnchor = false, p2;
  }, render: (e2, n3, r2) => d2(e2.tag, t({ key: r2.key }, e2.attrs), e2.text || (e2.children ? n3(e2.children, r2) : "")) }, [r.htmlSelfClosing]: { match: Ue(M), order: 1, parse(e2) {
    const t2 = e2[1].trim();
    return { attrs: Z2(t2, e2[2] || ""), tag: t2 };
  }, render: (e2, n3, r2) => d2(e2.tag, t({}, e2.attrs, { key: r2.key })) }, [r.htmlComment]: { match: Ue(B), order: 1, parse: () => ({}), render: We }, [r.image]: { match: Re($e), order: 1, parse: (e2) => ({ alt: e2[1], target: je(e2[2]), title: e2[3] }), render: (e2, t2, n3) => d2("img", { key: n3.key, alt: e2.alt || void 0, title: e2.title || void 0, src: i2.sanitizer(e2.target, "img", "src") }) }, [r.link]: { match: Me(Ce), order: 3, parse: (e2, t2, n3) => ({ children: Pe(t2, e2[1], n3), target: je(e2[2]), title: e2[3] }), render: (e2, t2, n3) => d2("a", { key: n3.key, href: i2.sanitizer(e2.target, "a", "href"), title: e2.title }, t2(e2.children, n3)) }, [r.linkAngleBraceStyleDetector]: { match: Me(D), order: 0, parse: (e2) => ({ children: [{ text: e2[1], type: r.text }], target: e2[1], type: r.link }) }, [r.linkBareUrlDetector]: { match: Oe((e2, t2) => t2.inAnchor || i2.disableAutoLink ? null : Me(I)(e2, t2)), order: 0, parse: (e2) => ({ children: [{ text: e2[1], type: r.text }], target: e2[1], title: void 0, type: r.link }) }, [r.linkMailtoDetector]: { match: Me(U), order: 0, parse(e2) {
    let t2 = e2[1], n3 = e2[1];
    return s.test(n3) || (n3 = "mailto:" + n3), { children: [{ text: t2.replace("mailto:", ""), type: r.text }], target: n3, type: r.link };
  } }, [r.orderedList]: ve(d2, 1), [r.unorderedList]: ve(d2, 2), [r.newlineCoalescer]: { match: Ie(x), order: 3, parse: Ge, render: () => "\n" }, [r.paragraph]: { match: Oe(De), order: 3, parse: Fe, render: (e2, t2, n3) => d2("p", { key: n3.key }, t2(e2.children, n3)) }, [r.ref]: { match: Me(H), order: 0, parse: (e2) => (Q2[e2[1]] = { target: e2[2], title: e2[4] }, {}), render: We }, [r.refImage]: { match: Re(P), order: 0, parse: (e2) => ({ alt: e2[1] || void 0, ref: e2[2] }), render: (e2, t2, n3) => Q2[e2.ref] ? d2("img", { key: n3.key, alt: e2.alt, src: i2.sanitizer(Q2[e2.ref].target, "img", "src"), title: Q2[e2.ref].title }) : null }, [r.refLink]: { match: Me(_), order: 0, parse: (e2, t2, n3) => ({ children: t2(e2[1], n3), fallbackChildren: e2[0], ref: e2[2] }), render: (e2, t2, n3) => Q2[e2.ref] ? d2("a", { key: n3.key, href: i2.sanitizer(Q2[e2.ref].target, "a", "href"), title: Q2[e2.ref].title }, t2(e2.children, n3)) : d2("span", { key: n3.key }, e2.fallbackChildren) }, [r.table]: { match: Ie(j), order: 1, parse: Te, render(e2, t2, n3) {
    const r2 = e2;
    return d2("table", { key: n3.key }, d2("thead", null, d2("tr", null, r2.header.map(function(e3, i3) {
      return d2("th", { key: i3, style: Be(r2, i3) }, t2(e3, n3));
    }))), d2("tbody", null, r2.cells.map(function(e3, i3) {
      return d2("tr", { key: i3 }, e3.map(function(e4, i4) {
        return d2("td", { key: i4, style: Be(r2, i4) }, t2(e4, n3));
      }));
    })));
  } }, [r.text]: { match: Ue(ne), order: 4, parse: (e2) => ({ text: e2[0].replace(T, (e3, t2) => i2.namedCodesToUnicode[t2] ? i2.namedCodesToUnicode[t2] : e3) }), render: (e2) => e2.text }, [r.textBolded]: { match: Re(X), order: 2, parse: (e2, t2, n3) => ({ children: t2(e2[2], n3) }), render: (e2, t2, n3) => d2("strong", { key: n3.key }, t2(e2.children, n3)) }, [r.textEmphasized]: { match: Re(J), order: 3, parse: (e2, t2, n3) => ({ children: t2(e2[2], n3) }), render: (e2, t2, n3) => d2("em", { key: n3.key }, t2(e2.children, n3)) }, [r.textEscaped]: { match: Re(ee), order: 1, parse: (e2) => ({ text: e2[1], type: r.text }) }, [r.textMarked]: { match: Re(K), order: 3, parse: Fe, render: (e2, t2, n3) => d2("mark", { key: n3.key }, t2(e2.children, n3)) }, [r.textStrikethroughed]: { match: Re(Y), order: 3, parse: Fe, render: (e2, t2, n3) => d2("del", { key: n3.key }, t2(e2.children, n3)) } };
  true === i2.disableParsingRawHTML && (delete V2[r.htmlBlock], delete V2[r.htmlSelfClosing]);
  const le2 = function(e2) {
    let t2 = Object.keys(e2);
    function n3(r2, i3) {
      let l2, a2, o2 = [], c2 = "", s2 = "";
      for (i3.prevCapture = i3.prevCapture || ""; r2; ) {
        let d3 = 0;
        for (; d3 < t2.length; ) {
          if (c2 = t2[d3], l2 = e2[c2], i3.inline && !l2.match.inline) {
            d3++;
            continue;
          }
          const u2 = l2.match(r2, i3);
          if (u2) {
            s2 = u2[0], i3.prevCapture += s2, r2 = r2.substring(s2.length), a2 = l2.parse(u2, n3, i3), null == a2.type && (a2.type = c2), o2.push(a2);
            break;
          }
          d3++;
        }
      }
      return i3.prevCapture = "", o2;
    }
    return t2.sort(function(t3, n4) {
      let r2 = e2[t3].order, i3 = e2[n4].order;
      return r2 !== i3 ? r2 - i3 : t3 < n4 ? -1 : 1;
    }), function(e3, t3) {
      return n3(function(e4) {
        return e4.replace(b, "\n").replace($, "").replace(G, "    ");
      }(e3), t3);
    };
  }(V2), ae2 = (oe2 = /* @__PURE__ */ function(e2, t2) {
    return function(n3, r2, i3) {
      const l2 = e2[n3.type].render;
      return t2 ? t2(() => l2(n3, r2, i3), n3, r2, i3) : l2(n3, r2, i3);
    };
  }(V2, i2.renderRule), function e2(t2, n3 = {}) {
    if (Array.isArray(t2)) {
      const r2 = n3.key, i3 = [];
      let l2 = false;
      for (let r3 = 0; r3 < t2.length; r3++) {
        n3.key = r3;
        const a2 = e2(t2[r3], n3), o2 = "string" == typeof a2;
        o2 && l2 ? i3[i3.length - 1] += a2 : null !== a2 && i3.push(a2), l2 = o2;
      }
      return n3.key = r2, i3;
    }
    return oe2(t2, e2, n3);
  });
  var oe2;
  const ce2 = W2(n2);
  return q2.length ? d2("div", null, ce2, d2("footer", { key: "footer" }, q2.map(function(e2) {
    return d2("div", { id: i2.slugify(e2.identifier, ze), key: e2.identifier }, e2.identifier, ae2(le2(e2.footnote, { inline: true })));
  }))) : ce2;
}
var index_modern_default = (t2) => {
  let { children: r2 = "", options: i2 } = t2, l2 = function(e2, t3) {
    if (null == e2) return {};
    var n2, r3, i3 = {}, l3 = Object.keys(e2);
    for (r3 = 0; r3 < l3.length; r3++) t3.indexOf(n2 = l3[r3]) >= 0 || (i3[n2] = e2[n2]);
    return i3;
  }(t2, n);
  return e.cloneElement(Qe(r2, i2), l2);
};

// node_modules/lodash-es/unset.js
function unset(object, path) {
  return object == null ? true : baseUnset_default(object, path);
}
var unset_default = unset;

// node_modules/@rjsf/core/lib/components/fields/ObjectField.js
var ObjectField = class extends import_react4.Component {
  constructor() {
    super(...arguments);
    /** Set up the initial state */
    __publicField(this, "state", {
      wasPropertyKeyModified: false,
      additionalProperties: {}
    });
    /** Returns the `onPropertyChange` handler for the `name` field. Handles the special case where a user is attempting
     * to clear the data for a field added as an additional property. Calls the `onChange()` handler with the updated
     * formData.
     *
     * @param name - The name of the property
     * @param addedByAdditionalProperties - Flag indicating whether this property is an additional property
     * @returns - The onPropertyChange callback for the `name` property
     */
    __publicField(this, "onPropertyChange", (name, addedByAdditionalProperties = false) => {
      return (value, newErrorSchema, id) => {
        const { formData, onChange, errorSchema } = this.props;
        if (value === void 0 && addedByAdditionalProperties) {
          value = "";
        }
        const newFormData = { ...formData, [name]: value };
        onChange(newFormData, errorSchema && errorSchema && {
          ...errorSchema,
          [name]: newErrorSchema
        }, id);
      };
    });
    /** Returns a callback to handle the onDropPropertyClick event for the given `key` which removes the old `key` data
     * and calls the `onChange` callback with it
     *
     * @param key - The key for which the drop callback is desired
     * @returns - The drop property click callback
     */
    __publicField(this, "onDropPropertyClick", (key) => {
      return (event) => {
        event.preventDefault();
        const { onChange, formData } = this.props;
        const copiedFormData = { ...formData };
        unset_default(copiedFormData, key);
        onChange(copiedFormData);
      };
    });
    /** Computes the next available key name from the `preferredKey`, indexing through the already existing keys until one
     * that is already not assigned is found.
     *
     * @param preferredKey - The preferred name of a new key
     * @param [formData] - The form data in which to check if the desired key already exists
     * @returns - The name of the next available key from `preferredKey`
     */
    __publicField(this, "getAvailableKey", (preferredKey, formData) => {
      const { uiSchema, registry } = this.props;
      const { duplicateKeySuffixSeparator = "-" } = getUiOptions(uiSchema, registry.globalUiOptions);
      let index = 0;
      let newKey = preferredKey;
      while (has_default(formData, newKey)) {
        newKey = `${preferredKey}${duplicateKeySuffixSeparator}${++index}`;
      }
      return newKey;
    });
    /** Returns a callback function that deals with the rename of a key for an additional property for a schema. That
     * callback will attempt to rename the key and move the existing data to that key, calling `onChange` when it does.
     *
     * @param oldValue - The old value of a field
     * @returns - The key change callback function
     */
    __publicField(this, "onKeyChange", (oldValue) => {
      return (value, newErrorSchema) => {
        if (oldValue === value) {
          return;
        }
        const { formData, onChange, errorSchema } = this.props;
        value = this.getAvailableKey(value, formData);
        const newFormData = {
          ...formData
        };
        const newKeys = { [oldValue]: value };
        const keyValues = Object.keys(newFormData).map((key) => {
          const newKey = newKeys[key] || key;
          return { [newKey]: newFormData[key] };
        });
        const renamedObj = Object.assign({}, ...keyValues);
        this.setState({ wasPropertyKeyModified: true });
        onChange(renamedObj, errorSchema && errorSchema && {
          ...errorSchema,
          [value]: newErrorSchema
        });
      };
    });
    /** Handles the adding of a new additional property on the given `schema`. Calls the `onChange` callback once the new
     * default data for that field has been added to the formData.
     *
     * @param schema - The schema element to which the new property is being added
     */
    __publicField(this, "handleAddClick", (schema) => () => {
      if (!schema.additionalProperties) {
        return;
      }
      const { formData, onChange, registry } = this.props;
      const newFormData = { ...formData };
      let type = void 0;
      let constValue = void 0;
      let defaultValue = void 0;
      if (isObject_default(schema.additionalProperties)) {
        type = schema.additionalProperties.type;
        constValue = schema.additionalProperties.const;
        defaultValue = schema.additionalProperties.default;
        let apSchema = schema.additionalProperties;
        if (REF_KEY in apSchema) {
          const { schemaUtils } = registry;
          apSchema = schemaUtils.retrieveSchema({ $ref: apSchema[REF_KEY] }, formData);
          type = apSchema.type;
          constValue = apSchema.const;
          defaultValue = apSchema.default;
        }
        if (!type && (ANY_OF_KEY in apSchema || ONE_OF_KEY in apSchema)) {
          type = "object";
        }
      }
      const newKey = this.getAvailableKey("newKey", newFormData);
      const newValue = constValue ?? defaultValue ?? this.getDefaultValue(type);
      set_default(newFormData, newKey, newValue);
      onChange(newFormData);
    });
  }
  /** Returns a flag indicating whether the `name` field is required in the object schema
   *
   * @param name - The name of the field to check for required-ness
   * @returns - True if the field `name` is required, false otherwise
   */
  isRequired(name) {
    const { schema } = this.props;
    return Array.isArray(schema.required) && schema.required.indexOf(name) !== -1;
  }
  /** Returns a default value to be used for a new additional schema property of the given `type`
   *
   * @param type - The type of the new additional schema property
   */
  getDefaultValue(type) {
    const { registry: { translateString } } = this.props;
    switch (type) {
      case "array":
        return [];
      case "boolean":
        return false;
      case "null":
        return null;
      case "number":
        return 0;
      case "object":
        return {};
      case "string":
      default:
        return translateString(TranslatableString.NewStringDefault);
    }
  }
  /** Renders the `ObjectField` from the given props
   */
  render() {
    const { schema: rawSchema, uiSchema = {}, formData, errorSchema, idSchema, name, required = false, disabled, readonly, hideError, idPrefix, idSeparator, onBlur, onFocus, registry, title } = this.props;
    const { fields: fields2, formContext, schemaUtils, translateString, globalUiOptions } = registry;
    const { SchemaField: SchemaField2 } = fields2;
    const schema = schemaUtils.retrieveSchema(rawSchema, formData);
    const uiOptions = getUiOptions(uiSchema, globalUiOptions);
    const { properties: schemaProperties = {} } = schema;
    const templateTitle = uiOptions.title ?? schema.title ?? title ?? name;
    const description = uiOptions.description ?? schema.description;
    let orderedProperties;
    try {
      const properties = Object.keys(schemaProperties);
      orderedProperties = orderProperties(properties, uiOptions.order);
    } catch (err) {
      return (0, import_jsx_runtime5.jsxs)("div", { children: [(0, import_jsx_runtime5.jsx)("p", { className: "config-error", style: { color: "red" }, children: (0, import_jsx_runtime5.jsx)(index_modern_default, { options: { disableParsingRawHTML: true }, children: translateString(TranslatableString.InvalidObjectField, [name || "root", err.message]) }) }), (0, import_jsx_runtime5.jsx)("pre", { children: JSON.stringify(schema) })] });
    }
    const Template = getTemplate("ObjectFieldTemplate", registry, uiOptions);
    const templateProps = {
      // getDisplayLabel() always returns false for object types, so just check the `uiOptions.label`
      title: uiOptions.label === false ? "" : templateTitle,
      description: uiOptions.label === false ? void 0 : description,
      properties: orderedProperties.map((name2) => {
        const addedByAdditionalProperties = has_default(schema, [PROPERTIES_KEY, name2, ADDITIONAL_PROPERTY_FLAG]);
        const fieldUiSchema = addedByAdditionalProperties ? uiSchema.additionalProperties : uiSchema[name2];
        const hidden = getUiOptions(fieldUiSchema).widget === "hidden";
        const fieldIdSchema = get_default(idSchema, [name2], {});
        return {
          content: (0, import_jsx_runtime5.jsx)(SchemaField2, { name: name2, required: this.isRequired(name2), schema: get_default(schema, [PROPERTIES_KEY, name2], {}), uiSchema: fieldUiSchema, errorSchema: get_default(errorSchema, name2), idSchema: fieldIdSchema, idPrefix, idSeparator, formData: get_default(formData, name2), formContext, wasPropertyKeyModified: this.state.wasPropertyKeyModified, onKeyChange: this.onKeyChange(name2), onChange: this.onPropertyChange(name2, addedByAdditionalProperties), onBlur, onFocus, registry, disabled, readonly, hideError, onDropPropertyClick: this.onDropPropertyClick }, name2),
          name: name2,
          readonly,
          disabled,
          required,
          hidden
        };
      }),
      readonly,
      disabled,
      required,
      idSchema,
      uiSchema,
      errorSchema,
      schema,
      formData,
      formContext,
      registry
    };
    return (0, import_jsx_runtime5.jsx)(Template, { ...templateProps, onAddClick: this.handleAddClick });
  }
};
var ObjectField_default = ObjectField;

// node_modules/@rjsf/core/lib/components/fields/SchemaField.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var import_react5 = __toESM(require_react());
var COMPONENT_TYPES = {
  array: "ArrayField",
  boolean: "BooleanField",
  integer: "NumberField",
  number: "NumberField",
  object: "ObjectField",
  string: "StringField",
  null: "NullField"
};
function getFieldComponent(schema, uiOptions, idSchema, registry) {
  const field = uiOptions.field;
  const { fields: fields2, translateString } = registry;
  if (typeof field === "function") {
    return field;
  }
  if (typeof field === "string" && field in fields2) {
    return fields2[field];
  }
  const schemaType = getSchemaType(schema);
  const type = Array.isArray(schemaType) ? schemaType[0] : schemaType || "";
  const schemaId = schema.$id;
  let componentName = COMPONENT_TYPES[type];
  if (schemaId && schemaId in fields2) {
    componentName = schemaId;
  }
  if (!componentName && (schema.anyOf || schema.oneOf)) {
    return () => null;
  }
  return componentName in fields2 ? fields2[componentName] : () => {
    const UnsupportedFieldTemplate = getTemplate("UnsupportedFieldTemplate", registry, uiOptions);
    return (0, import_jsx_runtime6.jsx)(UnsupportedFieldTemplate, { schema, idSchema, reason: translateString(TranslatableString.UnknownFieldType, [String(schema.type)]), registry });
  };
}
function SchemaFieldRender(props) {
  const { schema: _schema, idSchema: _idSchema, uiSchema, formData, errorSchema, idPrefix, idSeparator, name, onChange, onKeyChange, onDropPropertyClick, required, registry, wasPropertyKeyModified = false } = props;
  const { formContext, schemaUtils, globalUiOptions } = registry;
  const uiOptions = getUiOptions(uiSchema, globalUiOptions);
  const FieldTemplate2 = getTemplate("FieldTemplate", registry, uiOptions);
  const DescriptionFieldTemplate = getTemplate("DescriptionFieldTemplate", registry, uiOptions);
  const FieldHelpTemplate2 = getTemplate("FieldHelpTemplate", registry, uiOptions);
  const FieldErrorTemplate2 = getTemplate("FieldErrorTemplate", registry, uiOptions);
  const schema = schemaUtils.retrieveSchema(_schema, formData);
  const fieldId = _idSchema[ID_KEY];
  const idSchema = mergeObjects(schemaUtils.toIdSchema(schema, fieldId, formData, idPrefix, idSeparator), _idSchema);
  const handleFieldComponentChange = (0, import_react5.useCallback)((formData2, newErrorSchema, id2) => {
    const theId = id2 || fieldId;
    return onChange(formData2, newErrorSchema, theId);
  }, [fieldId, onChange]);
  const FieldComponent = getFieldComponent(schema, uiOptions, idSchema, registry);
  const disabled = Boolean(uiOptions.disabled ?? props.disabled);
  const readonly = Boolean(uiOptions.readonly ?? (props.readonly || props.schema.readOnly || schema.readOnly));
  const uiSchemaHideError = uiOptions.hideError;
  const hideError = uiSchemaHideError === void 0 ? props.hideError : Boolean(uiSchemaHideError);
  const autofocus = Boolean(uiOptions.autofocus ?? props.autofocus);
  if (Object.keys(schema).length === 0) {
    return null;
  }
  const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);
  const { __errors, ...fieldErrorSchema } = errorSchema || {};
  const fieldUiSchema = omit_default(uiSchema, ["ui:classNames", "classNames", "ui:style"]);
  if (UI_OPTIONS_KEY in fieldUiSchema) {
    fieldUiSchema[UI_OPTIONS_KEY] = omit_default(fieldUiSchema[UI_OPTIONS_KEY], ["classNames", "style"]);
  }
  const field = (0, import_jsx_runtime6.jsx)(FieldComponent, { ...props, onChange: handleFieldComponentChange, idSchema, schema, uiSchema: fieldUiSchema, disabled, readonly, hideError, autofocus, errorSchema: fieldErrorSchema, formContext, rawErrors: __errors });
  const id = idSchema[ID_KEY];
  let label;
  if (wasPropertyKeyModified) {
    label = name;
  } else {
    label = ADDITIONAL_PROPERTY_FLAG in schema ? name : uiOptions.title || props.schema.title || schema.title || props.title || name;
  }
  const description = uiOptions.description || props.schema.description || schema.description || "";
  const richDescription = uiOptions.enableMarkdownInDescription ? (0, import_jsx_runtime6.jsx)(index_modern_default, { options: { disableParsingRawHTML: true }, children: description }) : description;
  const help = uiOptions.help;
  const hidden = uiOptions.widget === "hidden";
  const classNames = ["form-group", "field", `field-${getSchemaType(schema)}`];
  if (!hideError && __errors && __errors.length > 0) {
    classNames.push("field-error has-error has-danger");
  }
  if (uiSchema == null ? void 0 : uiSchema.classNames) {
    if (true) {
      console.warn("'uiSchema.classNames' is deprecated and may be removed in a major release; Use 'ui:classNames' instead.");
    }
    classNames.push(uiSchema.classNames);
  }
  if (uiOptions.classNames) {
    classNames.push(uiOptions.classNames);
  }
  const helpComponent = (0, import_jsx_runtime6.jsx)(FieldHelpTemplate2, { help, idSchema, schema, uiSchema, hasErrors: !hideError && __errors && __errors.length > 0, registry });
  const errorsComponent = hideError || (schema.anyOf || schema.oneOf) && !schemaUtils.isSelect(schema) ? void 0 : (0, import_jsx_runtime6.jsx)(FieldErrorTemplate2, { errors: __errors, errorSchema, idSchema, schema, uiSchema, registry });
  const fieldProps = {
    description: (0, import_jsx_runtime6.jsx)(DescriptionFieldTemplate, { id: descriptionId(id), description: richDescription, schema, uiSchema, registry }),
    rawDescription: description,
    help: helpComponent,
    rawHelp: typeof help === "string" ? help : void 0,
    errors: errorsComponent,
    rawErrors: hideError ? void 0 : __errors,
    id,
    label,
    hidden,
    onChange,
    onKeyChange,
    onDropPropertyClick,
    required,
    disabled,
    readonly,
    hideError,
    displayLabel,
    classNames: classNames.join(" ").trim(),
    style: uiOptions.style,
    formContext,
    formData,
    schema,
    uiSchema,
    registry
  };
  const _AnyOfField = registry.fields.AnyOfField;
  const _OneOfField = registry.fields.OneOfField;
  const isReplacingAnyOrOneOf = (uiSchema == null ? void 0 : uiSchema["ui:field"]) && (uiSchema == null ? void 0 : uiSchema["ui:fieldReplacesAnyOrOneOf"]) === true;
  return (0, import_jsx_runtime6.jsx)(FieldTemplate2, { ...fieldProps, children: (0, import_jsx_runtime6.jsxs)(import_jsx_runtime6.Fragment, { children: [field, schema.anyOf && !isReplacingAnyOrOneOf && !schemaUtils.isSelect(schema) && (0, import_jsx_runtime6.jsx)(_AnyOfField, { name, disabled, readonly, hideError, errorSchema, formData, formContext, idPrefix, idSchema, idSeparator, onBlur: props.onBlur, onChange: props.onChange, onFocus: props.onFocus, options: schema.anyOf.map((_schema2) => schemaUtils.retrieveSchema(isObject_default(_schema2) ? _schema2 : {}, formData)), registry, required, schema, uiSchema }), schema.oneOf && !isReplacingAnyOrOneOf && !schemaUtils.isSelect(schema) && (0, import_jsx_runtime6.jsx)(_OneOfField, { name, disabled, readonly, hideError, errorSchema, formData, formContext, idPrefix, idSchema, idSeparator, onBlur: props.onBlur, onChange: props.onChange, onFocus: props.onFocus, options: schema.oneOf.map((_schema2) => schemaUtils.retrieveSchema(isObject_default(_schema2) ? _schema2 : {}, formData)), registry, required, schema, uiSchema })] }) });
}
var SchemaField = class extends import_react5.Component {
  shouldComponentUpdate(nextProps) {
    return !deepEquals(this.props, nextProps);
  }
  render() {
    return (0, import_jsx_runtime6.jsx)(SchemaFieldRender, { ...this.props });
  }
};
var SchemaField_default = SchemaField;

// node_modules/@rjsf/core/lib/components/fields/StringField.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
function StringField(props) {
  const { schema, name, uiSchema, idSchema, formData, required, disabled = false, readonly = false, autofocus = false, onChange, onBlur, onFocus, registry, rawErrors, hideError } = props;
  const { title, format } = schema;
  const { widgets: widgets2, formContext, schemaUtils, globalUiOptions } = registry;
  const enumOptions = schemaUtils.isSelect(schema) ? optionsList(schema, uiSchema) : void 0;
  let defaultWidget = enumOptions ? "select" : "text";
  if (format && hasWidget(schema, format, widgets2)) {
    defaultWidget = format;
  }
  const { widget = defaultWidget, placeholder = "", title: uiTitle, ...options } = getUiOptions(uiSchema);
  const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);
  const label = uiTitle ?? title ?? name;
  const Widget = getWidget(schema, widget, widgets2);
  return (0, import_jsx_runtime7.jsx)(Widget, { options: { ...options, enumOptions }, schema, uiSchema, id: idSchema.$id, name, label, hideLabel: !displayLabel, hideError, value: formData, onChange, onBlur, onFocus, required, disabled, readonly, formContext, autofocus, registry, placeholder, rawErrors });
}
var StringField_default = StringField;

// node_modules/@rjsf/core/lib/components/fields/NullField.js
var import_react6 = __toESM(require_react());
function NullField(props) {
  const { formData, onChange } = props;
  (0, import_react6.useEffect)(() => {
    if (formData === void 0) {
      onChange(null);
    }
  }, [formData, onChange]);
  return null;
}
var NullField_default = NullField;

// node_modules/@rjsf/core/lib/components/fields/index.js
function fields() {
  return {
    AnyOfField: MultiSchemaField_default,
    ArrayField: ArrayField_default,
    // ArrayField falls back to SchemaField if ArraySchemaField is not defined, which it isn't by default
    BooleanField: BooleanField_default,
    NumberField: NumberField_default,
    ObjectField: ObjectField_default,
    OneOfField: MultiSchemaField_default,
    SchemaField: SchemaField_default,
    StringField: StringField_default,
    NullField: NullField_default
  };
}
var fields_default = fields;

// node_modules/@rjsf/core/lib/components/templates/ArrayFieldDescriptionTemplate.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
function ArrayFieldDescriptionTemplate(props) {
  const { idSchema, description, registry, schema, uiSchema } = props;
  const options = getUiOptions(uiSchema, registry.globalUiOptions);
  const { label: displayLabel = true } = options;
  if (!description || !displayLabel) {
    return null;
  }
  const DescriptionFieldTemplate = getTemplate("DescriptionFieldTemplate", registry, options);
  return (0, import_jsx_runtime8.jsx)(DescriptionFieldTemplate, { id: descriptionId(idSchema), description, schema, uiSchema, registry });
}

// node_modules/@rjsf/core/lib/components/templates/ArrayFieldItemTemplate.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
function ArrayFieldItemTemplate(props) {
  const { children, className, disabled, hasToolbar, hasMoveDown, hasMoveUp, hasRemove, hasCopy, index, onCopyIndexClick, onDropIndexClick, onReorderClick, readonly, registry, uiSchema } = props;
  const { CopyButton: CopyButton2, MoveDownButton: MoveDownButton2, MoveUpButton: MoveUpButton2, RemoveButton: RemoveButton2 } = registry.templates.ButtonTemplates;
  const btnStyle = {
    flex: 1,
    paddingLeft: 6,
    paddingRight: 6,
    fontWeight: "bold"
  };
  return (0, import_jsx_runtime9.jsxs)("div", { className, children: [(0, import_jsx_runtime9.jsx)("div", { className: hasToolbar ? "col-xs-9" : "col-xs-12", children }), hasToolbar && (0, import_jsx_runtime9.jsx)("div", { className: "col-xs-3 array-item-toolbox", children: (0, import_jsx_runtime9.jsxs)("div", { className: "btn-group", style: {
    display: "flex",
    justifyContent: "space-around"
  }, children: [(hasMoveUp || hasMoveDown) && (0, import_jsx_runtime9.jsx)(MoveUpButton2, { style: btnStyle, disabled: disabled || readonly || !hasMoveUp, onClick: onReorderClick(index, index - 1), uiSchema, registry }), (hasMoveUp || hasMoveDown) && (0, import_jsx_runtime9.jsx)(MoveDownButton2, { style: btnStyle, disabled: disabled || readonly || !hasMoveDown, onClick: onReorderClick(index, index + 1), uiSchema, registry }), hasCopy && (0, import_jsx_runtime9.jsx)(CopyButton2, { style: btnStyle, disabled: disabled || readonly, onClick: onCopyIndexClick(index), uiSchema, registry }), hasRemove && (0, import_jsx_runtime9.jsx)(RemoveButton2, { style: btnStyle, disabled: disabled || readonly, onClick: onDropIndexClick(index), uiSchema, registry })] }) })] });
}

// node_modules/@rjsf/core/lib/components/templates/ArrayFieldTemplate.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
function ArrayFieldTemplate(props) {
  const { canAdd, className, disabled, idSchema, uiSchema, items, onAddClick, readonly, registry, required, schema, title } = props;
  const uiOptions = getUiOptions(uiSchema);
  const ArrayFieldDescriptionTemplate2 = getTemplate("ArrayFieldDescriptionTemplate", registry, uiOptions);
  const ArrayFieldItemTemplate2 = getTemplate("ArrayFieldItemTemplate", registry, uiOptions);
  const ArrayFieldTitleTemplate2 = getTemplate("ArrayFieldTitleTemplate", registry, uiOptions);
  const { ButtonTemplates: { AddButton: AddButton2 } } = registry.templates;
  return (0, import_jsx_runtime10.jsxs)("fieldset", { className, id: idSchema.$id, children: [(0, import_jsx_runtime10.jsx)(ArrayFieldTitleTemplate2, { idSchema, title: uiOptions.title || title, required, schema, uiSchema, registry }), (0, import_jsx_runtime10.jsx)(ArrayFieldDescriptionTemplate2, { idSchema, description: uiOptions.description || schema.description, schema, uiSchema, registry }), (0, import_jsx_runtime10.jsx)("div", { className: "row array-item-list", children: items && items.map(({ key, ...itemProps }) => (0, import_jsx_runtime10.jsx)(ArrayFieldItemTemplate2, { ...itemProps }, key)) }), canAdd && (0, import_jsx_runtime10.jsx)(AddButton2, { className: "array-item-add", onClick: onAddClick, disabled: disabled || readonly, uiSchema, registry })] });
}

// node_modules/@rjsf/core/lib/components/templates/ArrayFieldTitleTemplate.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
function ArrayFieldTitleTemplate(props) {
  const { idSchema, title, schema, uiSchema, required, registry } = props;
  const options = getUiOptions(uiSchema, registry.globalUiOptions);
  const { label: displayLabel = true } = options;
  if (!title || !displayLabel) {
    return null;
  }
  const TitleFieldTemplate = getTemplate("TitleFieldTemplate", registry, options);
  return (0, import_jsx_runtime11.jsx)(TitleFieldTemplate, { id: titleId(idSchema), title, required, schema, uiSchema, registry });
}

// node_modules/@rjsf/core/lib/components/templates/BaseInputTemplate.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var import_react7 = __toESM(require_react());
function BaseInputTemplate(props) {
  const {
    id,
    name,
    // remove this from ...rest
    value,
    readonly,
    disabled,
    autofocus,
    onBlur,
    onFocus,
    onChange,
    onChangeOverride,
    options,
    schema,
    uiSchema,
    formContext,
    registry,
    rawErrors,
    type,
    hideLabel,
    // remove this from ...rest
    hideError,
    // remove this from ...rest
    ...rest
  } = props;
  if (!id) {
    console.log("No id for", props);
    throw new Error(`no id for props ${JSON.stringify(props)}`);
  }
  const inputProps = {
    ...rest,
    ...getInputProps(schema, type, options)
  };
  let inputValue;
  if (inputProps.type === "number" || inputProps.type === "integer") {
    inputValue = value || value === 0 ? value : "";
  } else {
    inputValue = value == null ? "" : value;
  }
  const _onChange = (0, import_react7.useCallback)(({ target: { value: value2 } }) => onChange(value2 === "" ? options.emptyValue : value2), [onChange, options]);
  const _onBlur = (0, import_react7.useCallback)(({ target }) => onBlur(id, target && target.value), [onBlur, id]);
  const _onFocus = (0, import_react7.useCallback)(({ target }) => onFocus(id, target && target.value), [onFocus, id]);
  return (0, import_jsx_runtime12.jsxs)(import_jsx_runtime12.Fragment, { children: [(0, import_jsx_runtime12.jsx)("input", { id, name: id, className: "form-control", readOnly: readonly, disabled, autoFocus: autofocus, value: inputValue, ...inputProps, list: schema.examples ? examplesId(id) : void 0, onChange: onChangeOverride || _onChange, onBlur: _onBlur, onFocus: _onFocus, "aria-describedby": ariaDescribedByIds(id, !!schema.examples) }), Array.isArray(schema.examples) && (0, import_jsx_runtime12.jsx)("datalist", { id: examplesId(id), children: schema.examples.concat(schema.default && !schema.examples.includes(schema.default) ? [schema.default] : []).map((example) => {
    return (0, import_jsx_runtime12.jsx)("option", { value: example }, example);
  }) }, `datalist_${id}`)] });
}

// node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/SubmitButton.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
function SubmitButton({ uiSchema }) {
  const { submitText, norender, props: submitButtonProps = {} } = getSubmitButtonOptions(uiSchema);
  if (norender) {
    return null;
  }
  return (0, import_jsx_runtime13.jsx)("div", { children: (0, import_jsx_runtime13.jsx)("button", { type: "submit", ...submitButtonProps, className: `btn btn-info ${submitButtonProps.className || ""}`, children: submitText }) });
}

// node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/AddButton.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime());

// node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/IconButton.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
function IconButton(props) {
  const { iconType = "default", icon, className, uiSchema, registry, ...otherProps } = props;
  return (0, import_jsx_runtime14.jsx)("button", { type: "button", className: `btn btn-${iconType} ${className}`, ...otherProps, children: (0, import_jsx_runtime14.jsx)("i", { className: `glyphicon glyphicon-${icon}` }) });
}
function CopyButton(props) {
  const { registry: { translateString } } = props;
  return (0, import_jsx_runtime14.jsx)(IconButton, { title: translateString(TranslatableString.CopyButton), className: "array-item-copy", ...props, icon: "copy" });
}
function MoveDownButton(props) {
  const { registry: { translateString } } = props;
  return (0, import_jsx_runtime14.jsx)(IconButton, { title: translateString(TranslatableString.MoveDownButton), className: "array-item-move-down", ...props, icon: "arrow-down" });
}
function MoveUpButton(props) {
  const { registry: { translateString } } = props;
  return (0, import_jsx_runtime14.jsx)(IconButton, { title: translateString(TranslatableString.MoveUpButton), className: "array-item-move-up", ...props, icon: "arrow-up" });
}
function RemoveButton(props) {
  const { registry: { translateString } } = props;
  return (0, import_jsx_runtime14.jsx)(IconButton, { title: translateString(TranslatableString.RemoveButton), className: "array-item-remove", ...props, iconType: "danger", icon: "remove" });
}

// node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/AddButton.js
function AddButton({ className, onClick, disabled, registry }) {
  const { translateString } = registry;
  return (0, import_jsx_runtime15.jsx)("div", { className: "row", children: (0, import_jsx_runtime15.jsx)("p", { className: `col-xs-3 col-xs-offset-9 text-right ${className}`, children: (0, import_jsx_runtime15.jsx)(IconButton, { iconType: "info", icon: "plus", className: "btn-add col-xs-12", title: translateString(TranslatableString.AddButton), onClick, disabled, registry }) }) });
}

// node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/index.js
function buttonTemplates() {
  return {
    SubmitButton,
    AddButton,
    CopyButton,
    MoveDownButton,
    MoveUpButton,
    RemoveButton
  };
}
var ButtonTemplates_default = buttonTemplates;

// node_modules/@rjsf/core/lib/components/templates/DescriptionField.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
function DescriptionField(props) {
  const { id, description } = props;
  if (!description) {
    return null;
  }
  if (typeof description === "string") {
    return (0, import_jsx_runtime16.jsx)("p", { id, className: "field-description", children: description });
  } else {
    return (0, import_jsx_runtime16.jsx)("div", { id, className: "field-description", children: description });
  }
}

// node_modules/@rjsf/core/lib/components/templates/ErrorList.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
function ErrorList({ errors, registry }) {
  const { translateString } = registry;
  return (0, import_jsx_runtime17.jsxs)("div", { className: "panel panel-danger errors", children: [(0, import_jsx_runtime17.jsx)("div", { className: "panel-heading", children: (0, import_jsx_runtime17.jsx)("h3", { className: "panel-title", children: translateString(TranslatableString.ErrorsLabel) }) }), (0, import_jsx_runtime17.jsx)("ul", { className: "list-group", children: errors.map((error, i2) => {
    return (0, import_jsx_runtime17.jsx)("li", { className: "list-group-item text-danger", children: error.stack }, i2);
  }) })] });
}

// node_modules/@rjsf/core/lib/components/templates/FieldTemplate/FieldTemplate.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime());

// node_modules/@rjsf/core/lib/components/templates/FieldTemplate/Label.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
var REQUIRED_FIELD_SYMBOL = "*";
function Label(props) {
  const { label, required, id } = props;
  if (!label) {
    return null;
  }
  return (0, import_jsx_runtime18.jsxs)("label", { className: "control-label", htmlFor: id, children: [label, required && (0, import_jsx_runtime18.jsx)("span", { className: "required", children: REQUIRED_FIELD_SYMBOL })] });
}

// node_modules/@rjsf/core/lib/components/templates/FieldTemplate/FieldTemplate.js
function FieldTemplate(props) {
  const { id, label, children, errors, help, description, hidden, required, displayLabel, registry, uiSchema } = props;
  const uiOptions = getUiOptions(uiSchema);
  const WrapIfAdditionalTemplate2 = getTemplate("WrapIfAdditionalTemplate", registry, uiOptions);
  if (hidden) {
    return (0, import_jsx_runtime19.jsx)("div", { className: "hidden", children });
  }
  return (0, import_jsx_runtime19.jsxs)(WrapIfAdditionalTemplate2, { ...props, children: [displayLabel && (0, import_jsx_runtime19.jsx)(Label, { label, required, id }), displayLabel && description ? description : null, children, errors, help] });
}

// node_modules/@rjsf/core/lib/components/templates/FieldTemplate/index.js
var FieldTemplate_default = FieldTemplate;

// node_modules/@rjsf/core/lib/components/templates/FieldErrorTemplate.js
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
function FieldErrorTemplate(props) {
  const { errors = [], idSchema } = props;
  if (errors.length === 0) {
    return null;
  }
  const id = errorId(idSchema);
  return (0, import_jsx_runtime20.jsx)("div", { children: (0, import_jsx_runtime20.jsx)("ul", { id, className: "error-detail bs-callout bs-callout-info", children: errors.filter((elem) => !!elem).map((error, index) => {
    return (0, import_jsx_runtime20.jsx)("li", { className: "text-danger", children: error }, index);
  }) }) });
}

// node_modules/@rjsf/core/lib/components/templates/FieldHelpTemplate.js
var import_jsx_runtime21 = __toESM(require_jsx_runtime());
function FieldHelpTemplate(props) {
  const { idSchema, help } = props;
  if (!help) {
    return null;
  }
  const id = helpId(idSchema);
  if (typeof help === "string") {
    return (0, import_jsx_runtime21.jsx)("p", { id, className: "help-block", children: help });
  }
  return (0, import_jsx_runtime21.jsx)("div", { id, className: "help-block", children: help });
}

// node_modules/@rjsf/core/lib/components/templates/ObjectFieldTemplate.js
var import_jsx_runtime22 = __toESM(require_jsx_runtime());
function ObjectFieldTemplate(props) {
  const { description, disabled, formData, idSchema, onAddClick, properties, readonly, registry, required, schema, title, uiSchema } = props;
  const options = getUiOptions(uiSchema);
  const TitleFieldTemplate = getTemplate("TitleFieldTemplate", registry, options);
  const DescriptionFieldTemplate = getTemplate("DescriptionFieldTemplate", registry, options);
  const { ButtonTemplates: { AddButton: AddButton2 } } = registry.templates;
  return (0, import_jsx_runtime22.jsxs)("fieldset", { id: idSchema.$id, children: [title && (0, import_jsx_runtime22.jsx)(TitleFieldTemplate, { id: titleId(idSchema), title, required, schema, uiSchema, registry }), description && (0, import_jsx_runtime22.jsx)(DescriptionFieldTemplate, { id: descriptionId(idSchema), description, schema, uiSchema, registry }), properties.map((prop) => prop.content), canExpand(schema, uiSchema, formData) && (0, import_jsx_runtime22.jsx)(AddButton2, { className: "object-property-expand", onClick: onAddClick(schema), disabled: disabled || readonly, uiSchema, registry })] });
}

// node_modules/@rjsf/core/lib/components/templates/TitleField.js
var import_jsx_runtime23 = __toESM(require_jsx_runtime());
var REQUIRED_FIELD_SYMBOL2 = "*";
function TitleField(props) {
  const { id, title, required } = props;
  return (0, import_jsx_runtime23.jsxs)("legend", { id, children: [title, required && (0, import_jsx_runtime23.jsx)("span", { className: "required", children: REQUIRED_FIELD_SYMBOL2 })] });
}

// node_modules/@rjsf/core/lib/components/templates/UnsupportedField.js
var import_jsx_runtime24 = __toESM(require_jsx_runtime());
function UnsupportedField(props) {
  const { schema, idSchema, reason, registry } = props;
  const { translateString } = registry;
  let translateEnum = TranslatableString.UnsupportedField;
  const translateParams = [];
  if (idSchema && idSchema.$id) {
    translateEnum = TranslatableString.UnsupportedFieldWithId;
    translateParams.push(idSchema.$id);
  }
  if (reason) {
    translateEnum = translateEnum === TranslatableString.UnsupportedField ? TranslatableString.UnsupportedFieldWithReason : TranslatableString.UnsupportedFieldWithIdAndReason;
    translateParams.push(reason);
  }
  return (0, import_jsx_runtime24.jsxs)("div", { className: "unsupported-field", children: [(0, import_jsx_runtime24.jsx)("p", { children: (0, import_jsx_runtime24.jsx)(index_modern_default, { options: { disableParsingRawHTML: true }, children: translateString(translateEnum, translateParams) }) }), schema && (0, import_jsx_runtime24.jsx)("pre", { children: JSON.stringify(schema, null, 2) })] });
}
var UnsupportedField_default = UnsupportedField;

// node_modules/@rjsf/core/lib/components/templates/WrapIfAdditionalTemplate.js
var import_jsx_runtime25 = __toESM(require_jsx_runtime());
function WrapIfAdditionalTemplate(props) {
  const { id, classNames, style, disabled, label, onKeyChange, onDropPropertyClick, readonly, required, schema, children, uiSchema, registry } = props;
  const { templates: templates2, translateString } = registry;
  const { RemoveButton: RemoveButton2 } = templates2.ButtonTemplates;
  const keyLabel = translateString(TranslatableString.KeyLabel, [label]);
  const additional = ADDITIONAL_PROPERTY_FLAG in schema;
  if (!additional) {
    return (0, import_jsx_runtime25.jsx)("div", { className: classNames, style, children });
  }
  return (0, import_jsx_runtime25.jsx)("div", { className: classNames, style, children: (0, import_jsx_runtime25.jsxs)("div", { className: "row", children: [(0, import_jsx_runtime25.jsx)("div", { className: "col-xs-5 form-additional", children: (0, import_jsx_runtime25.jsxs)("div", { className: "form-group", children: [(0, import_jsx_runtime25.jsx)(Label, { label: keyLabel, required, id: `${id}-key` }), (0, import_jsx_runtime25.jsx)("input", { className: "form-control", type: "text", id: `${id}-key`, onBlur: ({ target }) => onKeyChange(target && target.value), defaultValue: label })] }) }), (0, import_jsx_runtime25.jsx)("div", { className: "form-additional form-group col-xs-5", children }), (0, import_jsx_runtime25.jsx)("div", { className: "col-xs-2", children: (0, import_jsx_runtime25.jsx)(RemoveButton2, { className: "array-item-remove btn-block", style: { border: "0" }, disabled: disabled || readonly, onClick: onDropPropertyClick(label), uiSchema, registry }) })] }) });
}

// node_modules/@rjsf/core/lib/components/templates/index.js
function templates() {
  return {
    ArrayFieldDescriptionTemplate,
    ArrayFieldItemTemplate,
    ArrayFieldTemplate,
    ArrayFieldTitleTemplate,
    ButtonTemplates: ButtonTemplates_default(),
    BaseInputTemplate,
    DescriptionFieldTemplate: DescriptionField,
    ErrorListTemplate: ErrorList,
    FieldTemplate: FieldTemplate_default,
    FieldErrorTemplate,
    FieldHelpTemplate,
    ObjectFieldTemplate,
    TitleFieldTemplate: TitleField,
    UnsupportedFieldTemplate: UnsupportedField_default,
    WrapIfAdditionalTemplate
  };
}
var templates_default = templates;

// node_modules/@rjsf/core/lib/components/widgets/AltDateWidget.js
var import_jsx_runtime26 = __toESM(require_jsx_runtime());
var import_react8 = __toESM(require_react());
function readyForChange(state) {
  return Object.values(state).every((value) => value !== -1);
}
function DateElement({ type, range, value, select, rootId, name, disabled, readonly, autofocus, registry, onBlur, onFocus }) {
  const id = rootId + "_" + type;
  const { SelectWidget: SelectWidget2 } = registry.widgets;
  return (0, import_jsx_runtime26.jsx)(SelectWidget2, { schema: { type: "integer" }, id, name, className: "form-control", options: { enumOptions: dateRangeOptions(range[0], range[1]) }, placeholder: type, value, disabled, readonly, autofocus, onChange: (value2) => select(type, value2), onBlur, onFocus, registry, label: "", "aria-describedby": ariaDescribedByIds(rootId) });
}
function AltDateWidget({ time = false, disabled = false, readonly = false, autofocus = false, options, id, name, registry, onBlur, onFocus, onChange, value }) {
  const { translateString } = registry;
  const [lastValue, setLastValue] = (0, import_react8.useState)(value);
  const [state, setState] = (0, import_react8.useReducer)((state2, action) => {
    return { ...state2, ...action };
  }, parseDateString(value, time));
  (0, import_react8.useEffect)(() => {
    const stateValue = toDateString(state, time);
    if (readyForChange(state) && stateValue !== value) {
      onChange(stateValue);
    } else if (lastValue !== value) {
      setLastValue(value);
      setState(parseDateString(value, time));
    }
  }, [time, value, onChange, state, lastValue]);
  const handleChange = (0, import_react8.useCallback)((property, value2) => {
    setState({ [property]: value2 });
  }, []);
  const handleSetNow = (0, import_react8.useCallback)((event) => {
    event.preventDefault();
    if (disabled || readonly) {
      return;
    }
    const nextState = parseDateString((/* @__PURE__ */ new Date()).toJSON(), time);
    onChange(toDateString(nextState, time));
  }, [disabled, readonly, time]);
  const handleClear = (0, import_react8.useCallback)((event) => {
    event.preventDefault();
    if (disabled || readonly) {
      return;
    }
    onChange(void 0);
  }, [disabled, readonly, onChange]);
  return (0, import_jsx_runtime26.jsxs)("ul", { className: "list-inline", children: [getDateElementProps(state, time, options.yearsRange, options.format).map((elemProps, i2) => (0, import_jsx_runtime26.jsx)("li", { className: "list-inline-item", children: (0, import_jsx_runtime26.jsx)(DateElement, { rootId: id, name, select: handleChange, ...elemProps, disabled, readonly, registry, onBlur, onFocus, autofocus: autofocus && i2 === 0 }) }, i2)), (options.hideNowButton !== "undefined" ? !options.hideNowButton : true) && (0, import_jsx_runtime26.jsx)("li", { className: "list-inline-item", children: (0, import_jsx_runtime26.jsx)("a", { href: "#", className: "btn btn-info btn-now", onClick: handleSetNow, children: translateString(TranslatableString.NowLabel) }) }), (options.hideClearButton !== "undefined" ? !options.hideClearButton : true) && (0, import_jsx_runtime26.jsx)("li", { className: "list-inline-item", children: (0, import_jsx_runtime26.jsx)("a", { href: "#", className: "btn btn-warning btn-clear", onClick: handleClear, children: translateString(TranslatableString.ClearLabel) }) })] });
}
var AltDateWidget_default = AltDateWidget;

// node_modules/@rjsf/core/lib/components/widgets/AltDateTimeWidget.js
var import_jsx_runtime27 = __toESM(require_jsx_runtime());
function AltDateTimeWidget({ time = true, ...props }) {
  const { AltDateWidget: AltDateWidget2 } = props.registry.widgets;
  return (0, import_jsx_runtime27.jsx)(AltDateWidget2, { time, ...props });
}
var AltDateTimeWidget_default = AltDateTimeWidget;

// node_modules/@rjsf/core/lib/components/widgets/CheckboxWidget.js
var import_jsx_runtime28 = __toESM(require_jsx_runtime());
var import_react9 = __toESM(require_react());
function CheckboxWidget({ schema, uiSchema, options, id, value, disabled, readonly, label, hideLabel, autofocus = false, onBlur, onFocus, onChange, registry }) {
  const DescriptionFieldTemplate = getTemplate("DescriptionFieldTemplate", registry, options);
  const required = schemaRequiresTrueValue(schema);
  const handleChange = (0, import_react9.useCallback)((event) => onChange(event.target.checked), [onChange]);
  const handleBlur = (0, import_react9.useCallback)((event) => onBlur(id, event.target.checked), [onBlur, id]);
  const handleFocus = (0, import_react9.useCallback)((event) => onFocus(id, event.target.checked), [onFocus, id]);
  const description = options.description ?? schema.description;
  return (0, import_jsx_runtime28.jsxs)("div", { className: `checkbox ${disabled || readonly ? "disabled" : ""}`, children: [!hideLabel && !!description && (0, import_jsx_runtime28.jsx)(DescriptionFieldTemplate, { id: descriptionId(id), description, schema, uiSchema, registry }), (0, import_jsx_runtime28.jsxs)("label", { children: [(0, import_jsx_runtime28.jsx)("input", { type: "checkbox", id, name: id, checked: typeof value === "undefined" ? false : value, required, disabled: disabled || readonly, autoFocus: autofocus, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, "aria-describedby": ariaDescribedByIds(id) }), labelValue((0, import_jsx_runtime28.jsx)("span", { children: label }), hideLabel)] })] });
}
var CheckboxWidget_default = CheckboxWidget;

// node_modules/@rjsf/core/lib/components/widgets/CheckboxesWidget.js
var import_jsx_runtime29 = __toESM(require_jsx_runtime());
var import_react10 = __toESM(require_react());
function CheckboxesWidget({ id, disabled, options: { inline = false, enumOptions, enumDisabled, emptyValue }, value, autofocus = false, readonly, onChange, onBlur, onFocus }) {
  const checkboxesValues = Array.isArray(value) ? value : [value];
  const handleBlur = (0, import_react10.useCallback)(({ target }) => onBlur(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onBlur, id]);
  const handleFocus = (0, import_react10.useCallback)(({ target }) => onFocus(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onFocus, id]);
  return (0, import_jsx_runtime29.jsx)("div", { className: "checkboxes", id, children: Array.isArray(enumOptions) && enumOptions.map((option, index) => {
    const checked = enumOptionsIsSelected(option.value, checkboxesValues);
    const itemDisabled = Array.isArray(enumDisabled) && enumDisabled.indexOf(option.value) !== -1;
    const disabledCls = disabled || itemDisabled || readonly ? "disabled" : "";
    const handleChange = (event) => {
      if (event.target.checked) {
        onChange(enumOptionsSelectValue(index, checkboxesValues, enumOptions));
      } else {
        onChange(enumOptionsDeselectValue(index, checkboxesValues, enumOptions));
      }
    };
    const checkbox = (0, import_jsx_runtime29.jsxs)("span", { children: [(0, import_jsx_runtime29.jsx)("input", { type: "checkbox", id: optionId(id, index), name: id, checked, value: String(index), disabled: disabled || itemDisabled || readonly, autoFocus: autofocus && index === 0, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, "aria-describedby": ariaDescribedByIds(id) }), (0, import_jsx_runtime29.jsx)("span", { children: option.label })] });
    return inline ? (0, import_jsx_runtime29.jsx)("label", { className: `checkbox-inline ${disabledCls}`, children: checkbox }, index) : (0, import_jsx_runtime29.jsx)("div", { className: `checkbox ${disabledCls}`, children: (0, import_jsx_runtime29.jsx)("label", { children: checkbox }) }, index);
  }) });
}
var CheckboxesWidget_default = CheckboxesWidget;

// node_modules/@rjsf/core/lib/components/widgets/ColorWidget.js
var import_jsx_runtime30 = __toESM(require_jsx_runtime());
function ColorWidget(props) {
  const { disabled, readonly, options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime30.jsx)(BaseInputTemplate2, { type: "color", ...props, disabled: disabled || readonly });
}

// node_modules/@rjsf/core/lib/components/widgets/DateWidget.js
var import_jsx_runtime31 = __toESM(require_jsx_runtime());
var import_react11 = __toESM(require_react());
function DateWidget(props) {
  const { onChange, options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  const handleChange = (0, import_react11.useCallback)((value) => onChange(value || void 0), [onChange]);
  return (0, import_jsx_runtime31.jsx)(BaseInputTemplate2, { type: "date", ...props, onChange: handleChange });
}

// node_modules/@rjsf/core/lib/components/widgets/DateTimeWidget.js
var import_jsx_runtime32 = __toESM(require_jsx_runtime());
function DateTimeWidget(props) {
  const { onChange, value, options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime32.jsx)(BaseInputTemplate2, { type: "datetime-local", ...props, value: utcToLocal(value), onChange: (value2) => onChange(localToUTC(value2)) });
}

// node_modules/@rjsf/core/lib/components/widgets/EmailWidget.js
var import_jsx_runtime33 = __toESM(require_jsx_runtime());
function EmailWidget(props) {
  const { options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime33.jsx)(BaseInputTemplate2, { type: "email", ...props });
}

// node_modules/@rjsf/core/lib/components/widgets/FileWidget.js
var import_jsx_runtime34 = __toESM(require_jsx_runtime());
var import_react12 = __toESM(require_react());
function addNameToDataURL(dataURL, name) {
  if (dataURL === null) {
    return null;
  }
  return dataURL.replace(";base64", `;name=${encodeURIComponent(name)};base64`);
}
function processFile(file) {
  const { name, size, type } = file;
  return new Promise((resolve, reject) => {
    const reader = new window.FileReader();
    reader.onerror = reject;
    reader.onload = (event) => {
      var _a;
      if (typeof ((_a = event.target) == null ? void 0 : _a.result) === "string") {
        resolve({
          dataURL: addNameToDataURL(event.target.result, name),
          name,
          size,
          type
        });
      } else {
        resolve({
          dataURL: null,
          name,
          size,
          type
        });
      }
    };
    reader.readAsDataURL(file);
  });
}
function processFiles(files) {
  return Promise.all(Array.from(files).map(processFile));
}
function FileInfoPreview({ fileInfo, registry }) {
  const { translateString } = registry;
  const { dataURL, type, name } = fileInfo;
  if (!dataURL) {
    return null;
  }
  if (["image/jpeg", "image/png"].includes(type)) {
    return (0, import_jsx_runtime34.jsx)("img", { src: dataURL, style: { maxWidth: "100%" }, className: "file-preview" });
  }
  return (0, import_jsx_runtime34.jsxs)(import_jsx_runtime34.Fragment, { children: [" ", (0, import_jsx_runtime34.jsx)("a", { download: `preview-${name}`, href: dataURL, className: "file-download", children: translateString(TranslatableString.PreviewLabel) })] });
}
function FilesInfo({ filesInfo, registry, preview, onRemove, options }) {
  if (filesInfo.length === 0) {
    return null;
  }
  const { translateString } = registry;
  const { RemoveButton: RemoveButton2 } = getTemplate("ButtonTemplates", registry, options);
  return (0, import_jsx_runtime34.jsx)("ul", { className: "file-info", children: filesInfo.map((fileInfo, key) => {
    const { name, size, type } = fileInfo;
    const handleRemove = () => onRemove(key);
    return (0, import_jsx_runtime34.jsxs)("li", { children: [(0, import_jsx_runtime34.jsx)(index_modern_default, { children: translateString(TranslatableString.FilesInfo, [name, type, String(size)]) }), preview && (0, import_jsx_runtime34.jsx)(FileInfoPreview, { fileInfo, registry }), (0, import_jsx_runtime34.jsx)(RemoveButton2, { onClick: handleRemove, registry })] }, key);
  }) });
}
function extractFileInfo(dataURLs) {
  return dataURLs.reduce((acc, dataURL) => {
    if (!dataURL) {
      return acc;
    }
    try {
      const { blob, name } = dataURItoBlob(dataURL);
      return [
        ...acc,
        {
          dataURL,
          name,
          size: blob.size,
          type: blob.type
        }
      ];
    } catch (e2) {
      return acc;
    }
  }, []);
}
function FileWidget(props) {
  const { disabled, readonly, required, multiple, onChange, value, options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  const handleChange = (0, import_react12.useCallback)((event) => {
    if (!event.target.files) {
      return;
    }
    processFiles(event.target.files).then((filesInfoEvent) => {
      const newValue = filesInfoEvent.map((fileInfo) => fileInfo.dataURL);
      if (multiple) {
        onChange(value.concat(newValue));
      } else {
        onChange(newValue[0]);
      }
    });
  }, [multiple, value, onChange]);
  const filesInfo = (0, import_react12.useMemo)(() => extractFileInfo(Array.isArray(value) ? value : [value]), [value]);
  const rmFile = (0, import_react12.useCallback)((index) => {
    if (multiple) {
      const newValue = value.filter((_2, i2) => i2 !== index);
      onChange(newValue);
    } else {
      onChange(void 0);
    }
  }, [multiple, value, onChange]);
  return (0, import_jsx_runtime34.jsxs)("div", { children: [(0, import_jsx_runtime34.jsx)(BaseInputTemplate2, { ...props, disabled: disabled || readonly, type: "file", required: value ? false : required, onChangeOverride: handleChange, value: "", accept: options.accept ? String(options.accept) : void 0 }), (0, import_jsx_runtime34.jsx)(FilesInfo, { filesInfo, onRemove: rmFile, registry, preview: options.filePreview, options })] });
}
var FileWidget_default = FileWidget;

// node_modules/@rjsf/core/lib/components/widgets/HiddenWidget.js
var import_jsx_runtime35 = __toESM(require_jsx_runtime());
function HiddenWidget({ id, value }) {
  return (0, import_jsx_runtime35.jsx)("input", { type: "hidden", id, name: id, value: typeof value === "undefined" ? "" : value });
}
var HiddenWidget_default = HiddenWidget;

// node_modules/@rjsf/core/lib/components/widgets/PasswordWidget.js
var import_jsx_runtime36 = __toESM(require_jsx_runtime());
function PasswordWidget(props) {
  const { options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime36.jsx)(BaseInputTemplate2, { type: "password", ...props });
}

// node_modules/@rjsf/core/lib/components/widgets/RadioWidget.js
var import_jsx_runtime37 = __toESM(require_jsx_runtime());
var import_react13 = __toESM(require_react());
function RadioWidget({ options, value, required, disabled, readonly, autofocus = false, onBlur, onFocus, onChange, id }) {
  const { enumOptions, enumDisabled, inline, emptyValue } = options;
  const handleBlur = (0, import_react13.useCallback)(({ target }) => onBlur(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onBlur, id]);
  const handleFocus = (0, import_react13.useCallback)(({ target }) => onFocus(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onFocus, id]);
  return (0, import_jsx_runtime37.jsx)("div", { className: "field-radio-group", id, children: Array.isArray(enumOptions) && enumOptions.map((option, i2) => {
    const checked = enumOptionsIsSelected(option.value, value);
    const itemDisabled = Array.isArray(enumDisabled) && enumDisabled.indexOf(option.value) !== -1;
    const disabledCls = disabled || itemDisabled || readonly ? "disabled" : "";
    const handleChange = () => onChange(option.value);
    const radio = (0, import_jsx_runtime37.jsxs)("span", { children: [(0, import_jsx_runtime37.jsx)("input", { type: "radio", id: optionId(id, i2), checked, name: id, required, value: String(i2), disabled: disabled || itemDisabled || readonly, autoFocus: autofocus && i2 === 0, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, "aria-describedby": ariaDescribedByIds(id) }), (0, import_jsx_runtime37.jsx)("span", { children: option.label })] });
    return inline ? (0, import_jsx_runtime37.jsx)("label", { className: `radio-inline ${disabledCls}`, children: radio }, i2) : (0, import_jsx_runtime37.jsx)("div", { className: `radio ${disabledCls}`, children: (0, import_jsx_runtime37.jsx)("label", { children: radio }) }, i2);
  }) });
}
var RadioWidget_default = RadioWidget;

// node_modules/@rjsf/core/lib/components/widgets/RangeWidget.js
var import_jsx_runtime38 = __toESM(require_jsx_runtime());
function RangeWidget(props) {
  const { value, registry: { templates: { BaseInputTemplate: BaseInputTemplate2 } } } = props;
  return (0, import_jsx_runtime38.jsxs)("div", { className: "field-range-wrapper", children: [(0, import_jsx_runtime38.jsx)(BaseInputTemplate2, { type: "range", ...props }), (0, import_jsx_runtime38.jsx)("span", { className: "range-view", children: value })] });
}

// node_modules/@rjsf/core/lib/components/widgets/SelectWidget.js
var import_jsx_runtime39 = __toESM(require_jsx_runtime());
var import_react14 = __toESM(require_react());
function getValue(event, multiple) {
  if (multiple) {
    return Array.from(event.target.options).slice().filter((o2) => o2.selected).map((o2) => o2.value);
  }
  return event.target.value;
}
function SelectWidget({ schema, id, options, value, required, disabled, readonly, multiple = false, autofocus = false, onChange, onBlur, onFocus, placeholder }) {
  const { enumOptions, enumDisabled, emptyValue: optEmptyVal } = options;
  const emptyValue = multiple ? [] : "";
  const handleFocus = (0, import_react14.useCallback)((event) => {
    const newValue = getValue(event, multiple);
    return onFocus(id, enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));
  }, [onFocus, id, schema, multiple, enumOptions, optEmptyVal]);
  const handleBlur = (0, import_react14.useCallback)((event) => {
    const newValue = getValue(event, multiple);
    return onBlur(id, enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));
  }, [onBlur, id, schema, multiple, enumOptions, optEmptyVal]);
  const handleChange = (0, import_react14.useCallback)((event) => {
    const newValue = getValue(event, multiple);
    return onChange(enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));
  }, [onChange, schema, multiple, enumOptions, optEmptyVal]);
  const selectedIndexes = enumOptionsIndexForValue(value, enumOptions, multiple);
  const showPlaceholderOption = !multiple && schema.default === void 0;
  return (0, import_jsx_runtime39.jsxs)("select", { id, name: id, multiple, className: "form-control", value: typeof selectedIndexes === "undefined" ? emptyValue : selectedIndexes, required, disabled: disabled || readonly, autoFocus: autofocus, onBlur: handleBlur, onFocus: handleFocus, onChange: handleChange, "aria-describedby": ariaDescribedByIds(id), children: [showPlaceholderOption && (0, import_jsx_runtime39.jsx)("option", { value: "", children: placeholder }), Array.isArray(enumOptions) && enumOptions.map(({ value: value2, label }, i2) => {
    const disabled2 = enumDisabled && enumDisabled.indexOf(value2) !== -1;
    return (0, import_jsx_runtime39.jsx)("option", { value: String(i2), disabled: disabled2, children: label }, i2);
  })] });
}
var SelectWidget_default = SelectWidget;

// node_modules/@rjsf/core/lib/components/widgets/TextareaWidget.js
var import_jsx_runtime40 = __toESM(require_jsx_runtime());
var import_react15 = __toESM(require_react());
function TextareaWidget({ id, options = {}, placeholder, value, required, disabled, readonly, autofocus = false, onChange, onBlur, onFocus }) {
  const handleChange = (0, import_react15.useCallback)(({ target: { value: value2 } }) => onChange(value2 === "" ? options.emptyValue : value2), [onChange, options.emptyValue]);
  const handleBlur = (0, import_react15.useCallback)(({ target }) => onBlur(id, target && target.value), [onBlur, id]);
  const handleFocus = (0, import_react15.useCallback)(({ target }) => onFocus(id, target && target.value), [id, onFocus]);
  return (0, import_jsx_runtime40.jsx)("textarea", { id, name: id, className: "form-control", value: value ? value : "", placeholder, required, disabled, readOnly: readonly, autoFocus: autofocus, rows: options.rows, onBlur: handleBlur, onFocus: handleFocus, onChange: handleChange, "aria-describedby": ariaDescribedByIds(id) });
}
TextareaWidget.defaultProps = {
  autofocus: false,
  options: {}
};
var TextareaWidget_default = TextareaWidget;

// node_modules/@rjsf/core/lib/components/widgets/TextWidget.js
var import_jsx_runtime41 = __toESM(require_jsx_runtime());
function TextWidget(props) {
  const { options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime41.jsx)(BaseInputTemplate2, { ...props });
}

// node_modules/@rjsf/core/lib/components/widgets/TimeWidget.js
var import_jsx_runtime42 = __toESM(require_jsx_runtime());
var import_react16 = __toESM(require_react());
function TimeWidget(props) {
  const { onChange, options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  const handleChange = (0, import_react16.useCallback)((value) => onChange(value ? `${value}:00` : void 0), [onChange]);
  return (0, import_jsx_runtime42.jsx)(BaseInputTemplate2, { type: "time", ...props, onChange: handleChange });
}

// node_modules/@rjsf/core/lib/components/widgets/URLWidget.js
var import_jsx_runtime43 = __toESM(require_jsx_runtime());
function URLWidget(props) {
  const { options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime43.jsx)(BaseInputTemplate2, { type: "url", ...props });
}

// node_modules/@rjsf/core/lib/components/widgets/UpDownWidget.js
var import_jsx_runtime44 = __toESM(require_jsx_runtime());
function UpDownWidget(props) {
  const { options, registry } = props;
  const BaseInputTemplate2 = getTemplate("BaseInputTemplate", registry, options);
  return (0, import_jsx_runtime44.jsx)(BaseInputTemplate2, { type: "number", ...props });
}

// node_modules/@rjsf/core/lib/components/widgets/index.js
function widgets() {
  return {
    AltDateWidget: AltDateWidget_default,
    AltDateTimeWidget: AltDateTimeWidget_default,
    CheckboxWidget: CheckboxWidget_default,
    CheckboxesWidget: CheckboxesWidget_default,
    ColorWidget,
    DateWidget,
    DateTimeWidget,
    EmailWidget,
    FileWidget: FileWidget_default,
    HiddenWidget: HiddenWidget_default,
    PasswordWidget,
    RadioWidget: RadioWidget_default,
    RangeWidget,
    SelectWidget: SelectWidget_default,
    TextWidget,
    TextareaWidget: TextareaWidget_default,
    TimeWidget,
    UpDownWidget,
    URLWidget
  };
}
var widgets_default = widgets;

// node_modules/@rjsf/core/lib/getDefaultRegistry.js
function getDefaultRegistry() {
  return {
    fields: fields_default(),
    templates: templates_default(),
    widgets: widgets_default(),
    rootSchema: {},
    formContext: {},
    translateString: englishStringTranslator
  };
}

// node_modules/@rjsf/core/lib/components/Form.js
var Form = class extends import_react17.Component {
  /** Constructs the `Form` from the `props`. Will setup the initial state from the props. It will also call the
   * `onChange` handler if the initially provided `formData` is modified to add missing default values as part of the
   * state construction.
   *
   * @param props - The initial props for the `Form`
   */
  constructor(props) {
    super(props);
    /** The ref used to hold the `form` element, this needs to be `any` because `tagName` or `_internalFormWrapper` can
     * provide any possible type here
     */
    __publicField(this, "formElement");
    /** Returns the `formData` with only the elements specified in the `fields` list
     *
     * @param formData - The data for the `Form`
     * @param fields - The fields to keep while filtering
     */
    __publicField(this, "getUsedFormData", (formData, fields2) => {
      if (fields2.length === 0 && typeof formData !== "object") {
        return formData;
      }
      const data = pick_default(formData, fields2);
      if (Array.isArray(formData)) {
        return Object.keys(data).map((key) => data[key]);
      }
      return data;
    });
    /** Returns the list of field names from inspecting the `pathSchema` as well as using the `formData`
     *
     * @param pathSchema - The `PathSchema` object for the form
     * @param [formData] - The form data to use while checking for empty objects/arrays
     */
    __publicField(this, "getFieldNames", (pathSchema, formData) => {
      const getAllPaths = (_obj, acc = [], paths = [[]]) => {
        Object.keys(_obj).forEach((key) => {
          if (typeof _obj[key] === "object") {
            const newPaths = paths.map((path) => [...path, key]);
            if (_obj[key][RJSF_ADDITIONAL_PROPERTIES_FLAG] && _obj[key][NAME_KEY] !== "") {
              acc.push(_obj[key][NAME_KEY]);
            } else {
              getAllPaths(_obj[key], acc, newPaths);
            }
          } else if (key === NAME_KEY && _obj[key] !== "") {
            paths.forEach((path) => {
              const formValue = get_default(formData, path);
              if (typeof formValue !== "object" || isEmpty_default(formValue) || Array.isArray(formValue) && formValue.every((val) => typeof val !== "object")) {
                acc.push(path);
              }
            });
          }
        });
        return acc;
      };
      return getAllPaths(pathSchema);
    });
    /** Returns the `formData` after filtering to remove any extra data not in a form field
     *
     * @param formData - The data for the `Form`
     * @returns The `formData` after omitting extra data
     */
    __publicField(this, "omitExtraData", (formData) => {
      const { schema, schemaUtils } = this.state;
      const retrievedSchema = schemaUtils.retrieveSchema(schema, formData);
      const pathSchema = schemaUtils.toPathSchema(retrievedSchema, "", formData);
      const fieldNames = this.getFieldNames(pathSchema, formData);
      const newFormData = this.getUsedFormData(formData, fieldNames);
      return newFormData;
    });
    /** Function to handle changes made to a field in the `Form`. This handler receives an entirely new copy of the
     * `formData` along with a new `ErrorSchema`. It will first update the `formData` with any missing default fields and
     * then, if `omitExtraData` and `liveOmit` are turned on, the `formData` will be filtered to remove any extra data not
     * in a form field. Then, the resulting formData will be validated if required. The state will be updated with the new
     * updated (potentially filtered) `formData`, any errors that resulted from validation. Finally the `onChange`
     * callback will be called if specified with the updated state.
     *
     * @param formData - The new form data from a change to a field
     * @param newErrorSchema - The new `ErrorSchema` based on the field change
     * @param id - The id of the field that caused the change
     */
    __publicField(this, "onChange", (formData, newErrorSchema, id) => {
      const { extraErrors, omitExtraData, liveOmit, noValidate, liveValidate, onChange } = this.props;
      const { schemaUtils, schema } = this.state;
      let retrievedSchema = this.state.retrievedSchema;
      if (isObject(formData) || Array.isArray(formData)) {
        const newState = this.getStateFromProps(this.props, formData);
        formData = newState.formData;
        retrievedSchema = newState.retrievedSchema;
      }
      const mustValidate = !noValidate && liveValidate;
      let state = { formData, schema };
      let newFormData = formData;
      if (omitExtraData === true && liveOmit === true) {
        newFormData = this.omitExtraData(formData);
        state = {
          formData: newFormData
        };
      }
      if (mustValidate) {
        const schemaValidation = this.validate(newFormData, schema, schemaUtils, retrievedSchema);
        let errors = schemaValidation.errors;
        let errorSchema = schemaValidation.errorSchema;
        const schemaValidationErrors = errors;
        const schemaValidationErrorSchema = errorSchema;
        if (extraErrors) {
          const merged = validationDataMerge(schemaValidation, extraErrors);
          errorSchema = merged.errorSchema;
          errors = merged.errors;
        }
        if (newErrorSchema) {
          const filteredErrors = this.filterErrorsBasedOnSchema(newErrorSchema, retrievedSchema, newFormData);
          errorSchema = mergeObjects(errorSchema, filteredErrors, "preventDuplicates");
        }
        state = {
          formData: newFormData,
          errors,
          errorSchema,
          schemaValidationErrors,
          schemaValidationErrorSchema
        };
      } else if (!noValidate && newErrorSchema) {
        const errorSchema = extraErrors ? mergeObjects(newErrorSchema, extraErrors, "preventDuplicates") : newErrorSchema;
        state = {
          formData: newFormData,
          errorSchema,
          errors: toErrorList(errorSchema)
        };
      }
      this.setState(state, () => onChange && onChange({ ...this.state, ...state }, id));
    });
    /**
     * Callback function to handle reset form data.
     * - Reset all fields with default values.
     * - Reset validations and errors
     *
     */
    __publicField(this, "reset", () => {
      const { onChange } = this.props;
      const newState = this.getStateFromProps(this.props, void 0);
      const newFormData = newState.formData;
      const state = {
        formData: newFormData,
        errorSchema: {},
        errors: [],
        schemaValidationErrors: [],
        schemaValidationErrorSchema: {}
      };
      this.setState(state, () => onChange && onChange({ ...this.state, ...state }));
    });
    /** Callback function to handle when a field on the form is blurred. Calls the `onBlur` callback for the `Form` if it
     * was provided.
     *
     * @param id - The unique `id` of the field that was blurred
     * @param data - The data associated with the field that was blurred
     */
    __publicField(this, "onBlur", (id, data) => {
      const { onBlur } = this.props;
      if (onBlur) {
        onBlur(id, data);
      }
    });
    /** Callback function to handle when a field on the form is focused. Calls the `onFocus` callback for the `Form` if it
     * was provided.
     *
     * @param id - The unique `id` of the field that was focused
     * @param data - The data associated with the field that was focused
     */
    __publicField(this, "onFocus", (id, data) => {
      const { onFocus } = this.props;
      if (onFocus) {
        onFocus(id, data);
      }
    });
    /** Callback function to handle when the form is submitted. First, it prevents the default event behavior. Nothing
     * happens if the target and currentTarget of the event are not the same. It will omit any extra data in the
     * `formData` in the state if `omitExtraData` is true. It will validate the resulting `formData`, reporting errors
     * via the `onError()` callback unless validation is disabled. Finally, it will add in any `extraErrors` and then call
     * back the `onSubmit` callback if it was provided.
     *
     * @param event - The submit HTML form event
     */
    __publicField(this, "onSubmit", (event) => {
      event.preventDefault();
      if (event.target !== event.currentTarget) {
        return;
      }
      event.persist();
      const { omitExtraData, extraErrors, noValidate, onSubmit } = this.props;
      let { formData: newFormData } = this.state;
      if (omitExtraData === true) {
        newFormData = this.omitExtraData(newFormData);
      }
      if (noValidate || this.validateFormWithFormData(newFormData)) {
        const errorSchema = extraErrors || {};
        const errors = extraErrors ? toErrorList(extraErrors) : [];
        this.setState({
          formData: newFormData,
          errors,
          errorSchema,
          schemaValidationErrors: [],
          schemaValidationErrorSchema: {}
        }, () => {
          if (onSubmit) {
            onSubmit({ ...this.state, formData: newFormData, status: "submitted" }, event);
          }
        });
      }
    });
    /** Provides a function that can be used to programmatically submit the `Form` */
    __publicField(this, "submit", () => {
      if (this.formElement.current) {
        const submitCustomEvent = new CustomEvent("submit", {
          cancelable: true
        });
        submitCustomEvent.preventDefault();
        this.formElement.current.dispatchEvent(submitCustomEvent);
        this.formElement.current.requestSubmit();
      }
    });
    /** Validates the form using the given `formData`. For use on form submission or on programmatic validation.
     * If `onError` is provided, then it will be called with the list of errors.
     *
     * @param formData - The form data to validate
     * @returns - True if the form is valid, false otherwise.
     */
    __publicField(this, "validateFormWithFormData", (formData) => {
      const { extraErrors, extraErrorsBlockSubmit, focusOnFirstError, onError } = this.props;
      const { errors: prevErrors } = this.state;
      const schemaValidation = this.validate(formData);
      let errors = schemaValidation.errors;
      let errorSchema = schemaValidation.errorSchema;
      const schemaValidationErrors = errors;
      const schemaValidationErrorSchema = errorSchema;
      const hasError = errors.length > 0 || extraErrors && extraErrorsBlockSubmit;
      if (hasError) {
        if (extraErrors) {
          const merged = validationDataMerge(schemaValidation, extraErrors);
          errorSchema = merged.errorSchema;
          errors = merged.errors;
        }
        if (focusOnFirstError) {
          if (typeof focusOnFirstError === "function") {
            focusOnFirstError(errors[0]);
          } else {
            this.focusOnError(errors[0]);
          }
        }
        this.setState({
          errors,
          errorSchema,
          schemaValidationErrors,
          schemaValidationErrorSchema
        }, () => {
          if (onError) {
            onError(errors);
          } else {
            console.error("Form validation failed", errors);
          }
        });
      } else if (prevErrors.length > 0) {
        this.setState({
          errors: [],
          errorSchema: {},
          schemaValidationErrors: [],
          schemaValidationErrorSchema: {}
        });
      }
      return !hasError;
    });
    if (!props.validator) {
      throw new Error("A validator is required for Form functionality to work");
    }
    this.state = this.getStateFromProps(props, props.formData);
    if (this.props.onChange && !deepEquals(this.state.formData, this.props.formData)) {
      this.props.onChange(this.state);
    }
    this.formElement = (0, import_react17.createRef)();
  }
  /**
   * `getSnapshotBeforeUpdate` is a React lifecycle method that is invoked right before the most recently rendered
   * output is committed to the DOM. It enables your component to capture current values (e.g., scroll position) before
   * they are potentially changed.
   *
   * In this case, it checks if the props have changed since the last render. If they have, it computes the next state
   * of the component using `getStateFromProps` method and returns it along with a `shouldUpdate` flag set to `true` IF
   * the `nextState` and `prevState` are different, otherwise `false`. This ensures that we have the most up-to-date
   * state ready to be applied in `componentDidUpdate`.
   *
   * If `formData` hasn't changed, it simply returns an object with `shouldUpdate` set to `false`, indicating that a
   * state update is not necessary.
   *
   * @param prevProps - The previous set of props before the update.
   * @param prevState - The previous state before the update.
   * @returns Either an object containing the next state and a flag indicating that an update should occur, or an object
   *        with a flag indicating that an update is not necessary.
   */
  getSnapshotBeforeUpdate(prevProps, prevState) {
    if (!deepEquals(this.props, prevProps)) {
      const formDataChangedFields = getChangedFields(this.props.formData, prevProps.formData);
      const isSchemaChanged = !deepEquals(prevProps.schema, this.props.schema);
      const isFormDataChanged = formDataChangedFields.length > 0 || !deepEquals(prevProps.formData, this.props.formData);
      const nextState = this.getStateFromProps(
        this.props,
        this.props.formData,
        // If the `schema` has changed, we need to update the retrieved schema.
        // Or if the `formData` changes, for example in the case of a schema with dependencies that need to
        //  match one of the subSchemas, the retrieved schema must be updated.
        isSchemaChanged || isFormDataChanged ? void 0 : this.state.retrievedSchema,
        isSchemaChanged,
        formDataChangedFields
      );
      const shouldUpdate = !deepEquals(nextState, prevState);
      return { nextState, shouldUpdate };
    }
    return { shouldUpdate: false };
  }
  /**
   * `componentDidUpdate` is a React lifecycle method that is invoked immediately after updating occurs. This method is
   * not called for the initial render.
   *
   * Here, it checks if an update is necessary based on the `shouldUpdate` flag received from `getSnapshotBeforeUpdate`.
   * If an update is required, it applies the next state and, if needed, triggers the `onChange` handler to inform about
   * changes.
   *
   * This method effectively replaces the deprecated `UNSAFE_componentWillReceiveProps`, providing a safer alternative
   * to handle prop changes and state updates.
   *
   * @param _ - The previous set of props.
   * @param prevState - The previous state of the component before the update.
   * @param snapshot - The value returned from `getSnapshotBeforeUpdate`.
   */
  componentDidUpdate(_2, prevState, snapshot) {
    if (snapshot.shouldUpdate) {
      const { nextState } = snapshot;
      if (!deepEquals(nextState.formData, this.props.formData) && !deepEquals(nextState.formData, prevState.formData) && this.props.onChange) {
        this.props.onChange(nextState);
      }
      this.setState(nextState);
    }
  }
  /** Extracts the updated state from the given `props` and `inputFormData`. As part of this process, the
   * `inputFormData` is first processed to add any missing required defaults. After that, the data is run through the
   * validation process IF required by the `props`.
   *
   * @param props - The props passed to the `Form`
   * @param inputFormData - The new or current data for the `Form`
   * @param retrievedSchema - An expanded schema, if not provided, it will be retrieved from the `schema` and `formData`.
   * @param isSchemaChanged - A flag indicating whether the schema has changed.
   * @param formDataChangedFields - The changed fields of `formData`
   * @returns - The new state for the `Form`
   */
  getStateFromProps(props, inputFormData, retrievedSchema, isSchemaChanged = false, formDataChangedFields = []) {
    var _a;
    const state = this.state || {};
    const schema = "schema" in props ? props.schema : this.props.schema;
    const uiSchema = ("uiSchema" in props ? props.uiSchema : this.props.uiSchema) || {};
    const edit = typeof inputFormData !== "undefined";
    const liveValidate = "liveValidate" in props ? props.liveValidate : this.props.liveValidate;
    const mustValidate = edit && !props.noValidate && liveValidate;
    const rootSchema = schema;
    const experimental_defaultFormStateBehavior = "experimental_defaultFormStateBehavior" in props ? props.experimental_defaultFormStateBehavior : this.props.experimental_defaultFormStateBehavior;
    const experimental_customMergeAllOf = "experimental_customMergeAllOf" in props ? props.experimental_customMergeAllOf : this.props.experimental_customMergeAllOf;
    let schemaUtils = state.schemaUtils;
    if (!schemaUtils || schemaUtils.doesSchemaUtilsDiffer(props.validator, rootSchema, experimental_defaultFormStateBehavior, experimental_customMergeAllOf)) {
      schemaUtils = createSchemaUtils(props.validator, rootSchema, experimental_defaultFormStateBehavior, experimental_customMergeAllOf);
    }
    const formData = schemaUtils.getDefaultFormState(schema, inputFormData);
    const _retrievedSchema = this.updateRetrievedSchema(retrievedSchema ?? schemaUtils.retrieveSchema(schema, formData));
    const getCurrentErrors = () => {
      if (props.noValidate || isSchemaChanged) {
        return { errors: [], errorSchema: {} };
      } else if (!props.liveValidate) {
        return {
          errors: state.schemaValidationErrors || [],
          errorSchema: state.schemaValidationErrorSchema || {}
        };
      }
      return {
        errors: state.errors || [],
        errorSchema: state.errorSchema || {}
      };
    };
    let errors;
    let errorSchema;
    let schemaValidationErrors = state.schemaValidationErrors;
    let schemaValidationErrorSchema = state.schemaValidationErrorSchema;
    if (mustValidate) {
      const schemaValidation = this.validate(formData, schema, schemaUtils, _retrievedSchema);
      errors = schemaValidation.errors;
      if (retrievedSchema === void 0) {
        errorSchema = schemaValidation.errorSchema;
      } else {
        errorSchema = mergeObjects((_a = this.state) == null ? void 0 : _a.errorSchema, schemaValidation.errorSchema, "preventDuplicates");
      }
      schemaValidationErrors = errors;
      schemaValidationErrorSchema = errorSchema;
    } else {
      const currentErrors = getCurrentErrors();
      errors = currentErrors.errors;
      errorSchema = currentErrors.errorSchema;
      if (formDataChangedFields.length > 0) {
        const newErrorSchema = formDataChangedFields.reduce((acc, key) => {
          acc[key] = void 0;
          return acc;
        }, {});
        errorSchema = schemaValidationErrorSchema = mergeObjects(currentErrors.errorSchema, newErrorSchema, "preventDuplicates");
      }
    }
    if (props.extraErrors) {
      const merged = validationDataMerge({ errorSchema, errors }, props.extraErrors);
      errorSchema = merged.errorSchema;
      errors = merged.errors;
    }
    const idSchema = schemaUtils.toIdSchema(_retrievedSchema, uiSchema["ui:rootFieldId"], formData, props.idPrefix, props.idSeparator);
    const nextState = {
      schemaUtils,
      schema,
      uiSchema,
      idSchema,
      formData,
      edit,
      errors,
      errorSchema,
      schemaValidationErrors,
      schemaValidationErrorSchema,
      retrievedSchema: _retrievedSchema
    };
    return nextState;
  }
  /** React lifecycle method that is used to determine whether component should be updated.
   *
   * @param nextProps - The next version of the props
   * @param nextState - The next version of the state
   * @returns - True if the component should be updated, false otherwise
   */
  shouldComponentUpdate(nextProps, nextState) {
    return shouldRender(this, nextProps, nextState);
  }
  /** Gets the previously raised customValidate errors.
   *
   * @returns the previous customValidate errors
   */
  getPreviousCustomValidateErrors() {
    const { customValidate, uiSchema } = this.props;
    const prevFormData = this.state.formData;
    let customValidateErrors = {};
    if (typeof customValidate === "function") {
      const errorHandler = customValidate(prevFormData, createErrorHandler(prevFormData), uiSchema);
      const userErrorSchema = unwrapErrorHandler(errorHandler);
      customValidateErrors = userErrorSchema;
    }
    return customValidateErrors;
  }
  /** Validates the `formData` against the `schema` using the `altSchemaUtils` (if provided otherwise it uses the
   * `schemaUtils` in the state), returning the results.
   *
   * @param formData - The new form data to validate
   * @param schema - The schema used to validate against
   * @param altSchemaUtils - The alternate schemaUtils to use for validation
   */
  validate(formData, schema = this.props.schema, altSchemaUtils, retrievedSchema) {
    const schemaUtils = altSchemaUtils ? altSchemaUtils : this.state.schemaUtils;
    const { customValidate, transformErrors, uiSchema } = this.props;
    const resolvedSchema = retrievedSchema ?? schemaUtils.retrieveSchema(schema, formData);
    return schemaUtils.getValidator().validateFormData(formData, resolvedSchema, customValidate, transformErrors, uiSchema);
  }
  /** Renders any errors contained in the `state` in using the `ErrorList`, if not disabled by `showErrorList`. */
  renderErrors(registry) {
    const { errors, errorSchema, schema, uiSchema } = this.state;
    const { formContext } = this.props;
    const options = getUiOptions(uiSchema);
    const ErrorListTemplate = getTemplate("ErrorListTemplate", registry, options);
    if (errors && errors.length) {
      return (0, import_jsx_runtime45.jsx)(ErrorListTemplate, { errors, errorSchema: errorSchema || {}, schema, uiSchema, formContext, registry });
    }
    return null;
  }
  // Filtering errors based on your retrieved schema to only show errors for properties in the selected branch.
  filterErrorsBasedOnSchema(schemaErrors, resolvedSchema, formData) {
    const { retrievedSchema, schemaUtils } = this.state;
    const _retrievedSchema = resolvedSchema ?? retrievedSchema;
    const pathSchema = schemaUtils.toPathSchema(_retrievedSchema, "", formData);
    const fieldNames = this.getFieldNames(pathSchema, formData);
    const filteredErrors = pick_default(schemaErrors, fieldNames);
    if ((resolvedSchema == null ? void 0 : resolvedSchema.type) !== "object" && (resolvedSchema == null ? void 0 : resolvedSchema.type) !== "array") {
      filteredErrors.__errors = schemaErrors.__errors;
    }
    const prevCustomValidateErrors = this.getPreviousCustomValidateErrors();
    const filterPreviousCustomErrors = (errors = [], prevCustomErrors) => {
      if (errors.length === 0) {
        return errors;
      }
      return errors.filter((error) => {
        return !prevCustomErrors.includes(error);
      });
    };
    const filterNilOrEmptyErrors = (errors, previousCustomValidateErrors = {}) => {
      forEach_default(errors, (errorAtKey, errorKey) => {
        const prevCustomValidateErrorAtKey = previousCustomValidateErrors[errorKey];
        if (isNil_default(errorAtKey) || Array.isArray(errorAtKey) && errorAtKey.length === 0) {
          delete errors[errorKey];
        } else if (isObject(errorAtKey) && isObject(prevCustomValidateErrorAtKey) && Array.isArray(prevCustomValidateErrorAtKey == null ? void 0 : prevCustomValidateErrorAtKey.__errors)) {
          errors[errorKey] = filterPreviousCustomErrors(errorAtKey.__errors, prevCustomValidateErrorAtKey.__errors);
        } else if (typeof errorAtKey === "object" && !Array.isArray(errorAtKey.__errors)) {
          filterNilOrEmptyErrors(errorAtKey, previousCustomValidateErrors[errorKey]);
        }
      });
      return errors;
    };
    return filterNilOrEmptyErrors(filteredErrors, prevCustomValidateErrors);
  }
  /**
   * If the retrievedSchema has changed the new retrievedSchema is returned.
   * Otherwise, the old retrievedSchema is returned to persist reference.
   * -  This ensures that AJV retrieves the schema from the cache when it has not changed,
   *    avoiding the performance cost of recompiling the schema.
   *
   * @param retrievedSchema The new retrieved schema.
   * @returns The new retrieved schema if it has changed, else the old retrieved schema.
   */
  updateRetrievedSchema(retrievedSchema) {
    var _a;
    const isTheSame = deepEquals(retrievedSchema, (_a = this.state) == null ? void 0 : _a.retrievedSchema);
    return isTheSame ? this.state.retrievedSchema : retrievedSchema;
  }
  /** Returns the registry for the form */
  getRegistry() {
    var _a;
    const { translateString: customTranslateString, uiSchema = {} } = this.props;
    const { schemaUtils } = this.state;
    const { fields: fields2, templates: templates2, widgets: widgets2, formContext, translateString } = getDefaultRegistry();
    return {
      fields: { ...fields2, ...this.props.fields },
      templates: {
        ...templates2,
        ...this.props.templates,
        ButtonTemplates: {
          ...templates2.ButtonTemplates,
          ...(_a = this.props.templates) == null ? void 0 : _a.ButtonTemplates
        }
      },
      widgets: { ...widgets2, ...this.props.widgets },
      rootSchema: this.props.schema,
      formContext: this.props.formContext || formContext,
      schemaUtils,
      translateString: customTranslateString || translateString,
      globalUiOptions: uiSchema[UI_GLOBAL_OPTIONS_KEY]
    };
  }
  /** Attempts to focus on the field associated with the `error`. Uses the `property` field to compute path of the error
   * field, then, using the `idPrefix` and `idSeparator` converts that path into an id. Then the input element with that
   * id is attempted to be found using the `formElement` ref. If it is located, then it is focused.
   *
   * @param error - The error on which to focus
   */
  focusOnError(error) {
    const { idPrefix = "root", idSeparator = "_" } = this.props;
    const { property } = error;
    const path = toPath_default(property);
    if (path[0] === "") {
      path[0] = idPrefix;
    } else {
      path.unshift(idPrefix);
    }
    const elementId = path.join(idSeparator);
    let field = this.formElement.current.elements[elementId];
    if (!field) {
      field = this.formElement.current.querySelector(`input[id^="${elementId}"`);
    }
    if (field && field.length) {
      field = field[0];
    }
    if (field) {
      field.focus();
    }
  }
  /** Programmatically validate the form.  If `omitExtraData` is true, the `formData` will first be filtered to remove
   * any extra data not in a form field. If `onError` is provided, then it will be called with the list of errors the
   * same way as would happen on form submission.
   *
   * @returns - True if the form is valid, false otherwise.
   */
  validateForm() {
    const { omitExtraData } = this.props;
    let { formData: newFormData } = this.state;
    if (omitExtraData === true) {
      newFormData = this.omitExtraData(newFormData);
    }
    return this.validateFormWithFormData(newFormData);
  }
  /** Renders the `Form` fields inside the <form> | `tagName` or `_internalFormWrapper`, rendering any errors if
   * needed along with the submit button or any children of the form.
   */
  render() {
    const { children, id, idPrefix, idSeparator, className = "", tagName, name, method, target, action, autoComplete, enctype, acceptcharset, acceptCharset, noHtml5Validate = false, disabled, readonly, formContext, showErrorList = "top", _internalFormWrapper } = this.props;
    const { schema, uiSchema, formData, errorSchema, idSchema } = this.state;
    const registry = this.getRegistry();
    const { SchemaField: _SchemaField } = registry.fields;
    const { SubmitButton: SubmitButton2 } = registry.templates.ButtonTemplates;
    const as = _internalFormWrapper ? tagName : void 0;
    const FormTag = _internalFormWrapper || tagName || "form";
    let { [SUBMIT_BTN_OPTIONS_KEY]: submitOptions = {} } = getUiOptions(uiSchema);
    if (disabled) {
      submitOptions = { ...submitOptions, props: { ...submitOptions.props, disabled: true } };
    }
    const submitUiSchema = { [UI_OPTIONS_KEY]: { [SUBMIT_BTN_OPTIONS_KEY]: submitOptions } };
    return (0, import_jsx_runtime45.jsxs)(FormTag, { className: className ? className : "rjsf", id, name, method, target, action, autoComplete, encType: enctype, acceptCharset: acceptCharset || acceptcharset, noValidate: noHtml5Validate, onSubmit: this.onSubmit, as, ref: this.formElement, children: [showErrorList === "top" && this.renderErrors(registry), (0, import_jsx_runtime45.jsx)(_SchemaField, { name: "", schema, uiSchema, errorSchema, idSchema, idPrefix, idSeparator, formContext, formData, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, registry, disabled, readonly }), children ? children : (0, import_jsx_runtime45.jsx)(SubmitButton2, { uiSchema: submitUiSchema, registry }), showErrorList === "bottom" && this.renderErrors(registry)] });
  }
};

// node_modules/@rjsf/core/lib/withTheme.js
var import_jsx_runtime46 = __toESM(require_jsx_runtime());
var import_react18 = __toESM(require_react());
function withTheme(themeProps) {
  return (0, import_react18.forwardRef)(({ fields: fields2, widgets: widgets2, templates: templates2, ...directProps }, ref) => {
    var _a;
    fields2 = { ...themeProps == null ? void 0 : themeProps.fields, ...fields2 };
    widgets2 = { ...themeProps == null ? void 0 : themeProps.widgets, ...widgets2 };
    templates2 = {
      ...themeProps == null ? void 0 : themeProps.templates,
      ...templates2,
      ButtonTemplates: {
        ...(_a = themeProps == null ? void 0 : themeProps.templates) == null ? void 0 : _a.ButtonTemplates,
        ...templates2 == null ? void 0 : templates2.ButtonTemplates
      }
    };
    return (0, import_jsx_runtime46.jsx)(Form, { ...themeProps, ...directProps, fields: fields2, widgets: widgets2, templates: templates2, ref });
  });
}

// node_modules/@rjsf/core/lib/index.js
var lib_default = Form;
export {
  lib_default as default,
  getDefaultRegistry,
  withTheme
};
//# sourceMappingURL=@rjsf_core.js.map
