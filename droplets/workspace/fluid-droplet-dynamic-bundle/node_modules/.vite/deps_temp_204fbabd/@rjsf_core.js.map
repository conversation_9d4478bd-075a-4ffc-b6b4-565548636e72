{"version": 3, "sources": ["../../@rjsf/core/lib/components/Form.js", "../../lodash-es/_basePick.js", "../../lodash-es/pick.js", "../../@rjsf/core/lib/components/fields/ArrayField.js", "../../@rjsf/core/node_modules/nanoid/index.browser.js", "../../@rjsf/core/lib/components/fields/BooleanField.js", "../../@rjsf/core/lib/components/fields/MultiSchemaField.js", "../../@rjsf/core/lib/components/fields/NumberField.js", "../../@rjsf/core/lib/components/fields/ObjectField.js", "../../markdown-to-jsx/index.tsx", "../../lodash-es/unset.js", "../../@rjsf/core/lib/components/fields/SchemaField.js", "../../@rjsf/core/lib/components/fields/StringField.js", "../../@rjsf/core/lib/components/fields/NullField.js", "../../@rjsf/core/lib/components/fields/index.js", "../../@rjsf/core/lib/components/templates/ArrayFieldDescriptionTemplate.js", "../../@rjsf/core/lib/components/templates/ArrayFieldItemTemplate.js", "../../@rjsf/core/lib/components/templates/ArrayFieldTemplate.js", "../../@rjsf/core/lib/components/templates/ArrayFieldTitleTemplate.js", "../../@rjsf/core/lib/components/templates/BaseInputTemplate.js", "../../@rjsf/core/lib/components/templates/ButtonTemplates/SubmitButton.js", "../../@rjsf/core/lib/components/templates/ButtonTemplates/AddButton.js", "../../@rjsf/core/lib/components/templates/ButtonTemplates/IconButton.js", "../../@rjsf/core/lib/components/templates/ButtonTemplates/index.js", "../../@rjsf/core/lib/components/templates/DescriptionField.js", "../../@rjsf/core/lib/components/templates/ErrorList.js", "../../@rjsf/core/lib/components/templates/FieldTemplate/FieldTemplate.js", "../../@rjsf/core/lib/components/templates/FieldTemplate/Label.js", "../../@rjsf/core/lib/components/templates/FieldTemplate/index.js", "../../@rjsf/core/lib/components/templates/FieldErrorTemplate.js", "../../@rjsf/core/lib/components/templates/FieldHelpTemplate.js", "../../@rjsf/core/lib/components/templates/ObjectFieldTemplate.js", "../../@rjsf/core/lib/components/templates/TitleField.js", "../../@rjsf/core/lib/components/templates/UnsupportedField.js", "../../@rjsf/core/lib/components/templates/WrapIfAdditionalTemplate.js", "../../@rjsf/core/lib/components/templates/index.js", "../../@rjsf/core/lib/components/widgets/AltDateWidget.js", "../../@rjsf/core/lib/components/widgets/AltDateTimeWidget.js", "../../@rjsf/core/lib/components/widgets/CheckboxWidget.js", "../../@rjsf/core/lib/components/widgets/CheckboxesWidget.js", "../../@rjsf/core/lib/components/widgets/ColorWidget.js", "../../@rjsf/core/lib/components/widgets/DateWidget.js", "../../@rjsf/core/lib/components/widgets/DateTimeWidget.js", "../../@rjsf/core/lib/components/widgets/EmailWidget.js", "../../@rjsf/core/lib/components/widgets/FileWidget.js", "../../@rjsf/core/lib/components/widgets/HiddenWidget.js", "../../@rjsf/core/lib/components/widgets/PasswordWidget.js", "../../@rjsf/core/lib/components/widgets/RadioWidget.js", "../../@rjsf/core/lib/components/widgets/RangeWidget.js", "../../@rjsf/core/lib/components/widgets/SelectWidget.js", "../../@rjsf/core/lib/components/widgets/TextareaWidget.js", "../../@rjsf/core/lib/components/widgets/TextWidget.js", "../../@rjsf/core/lib/components/widgets/TimeWidget.js", "../../@rjsf/core/lib/components/widgets/URLWidget.js", "../../@rjsf/core/lib/components/widgets/UpDownWidget.js", "../../@rjsf/core/lib/components/widgets/index.js", "../../@rjsf/core/lib/getDefaultRegistry.js", "../../@rjsf/core/lib/withTheme.js", "../../@rjsf/core/lib/index.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Component, createRef } from 'react';\nimport { createSchemaUtils, deepEquals, getChangedFields, getTemplate, getUiOptions, isObject, mergeObjects, NAME_KEY, RJSF_ADDITIONAL_PROPERTIES_FLAG, shouldRender, SUBMIT_BTN_OPTIONS_KEY, toErrorList, UI_GLOBAL_OPTIONS_KEY, UI_OPTIONS_KEY, validationDataMerge, createErrorHandler, unwrapErrorHandler, } from '@rjsf/utils';\nimport _forEach from 'lodash-es/forEach.js';\nimport _get from 'lodash-es/get.js';\nimport _isEmpty from 'lodash-es/isEmpty.js';\nimport _isNil from 'lodash-es/isNil.js';\nimport _pick from 'lodash-es/pick.js';\nimport _toPath from 'lodash-es/toPath.js';\nimport getDefaultRegistry from '../getDefaultRegistry.js';\n/** The `Form` component renders the outer form and all the fields defined in the `schema` */\nexport default class Form extends Component {\n    /** The ref used to hold the `form` element, this needs to be `any` because `tagName` or `_internalFormWrapper` can\n     * provide any possible type here\n     */\n    formElement;\n    /** Constructs the `Form` from the `props`. Will setup the initial state from the props. It will also call the\n     * `onChange` handler if the initially provided `formData` is modified to add missing default values as part of the\n     * state construction.\n     *\n     * @param props - The initial props for the `Form`\n     */\n    constructor(props) {\n        super(props);\n        if (!props.validator) {\n            throw new Error('A validator is required for Form functionality to work');\n        }\n        this.state = this.getStateFromProps(props, props.formData);\n        if (this.props.onChange && !deepEquals(this.state.formData, this.props.formData)) {\n            this.props.onChange(this.state);\n        }\n        this.formElement = createRef();\n    }\n    /**\n     * `getSnapshotBeforeUpdate` is a React lifecycle method that is invoked right before the most recently rendered\n     * output is committed to the DOM. It enables your component to capture current values (e.g., scroll position) before\n     * they are potentially changed.\n     *\n     * In this case, it checks if the props have changed since the last render. If they have, it computes the next state\n     * of the component using `getStateFromProps` method and returns it along with a `shouldUpdate` flag set to `true` IF\n     * the `nextState` and `prevState` are different, otherwise `false`. This ensures that we have the most up-to-date\n     * state ready to be applied in `componentDidUpdate`.\n     *\n     * If `formData` hasn't changed, it simply returns an object with `shouldUpdate` set to `false`, indicating that a\n     * state update is not necessary.\n     *\n     * @param prevProps - The previous set of props before the update.\n     * @param prevState - The previous state before the update.\n     * @returns Either an object containing the next state and a flag indicating that an update should occur, or an object\n     *        with a flag indicating that an update is not necessary.\n     */\n    getSnapshotBeforeUpdate(prevProps, prevState) {\n        if (!deepEquals(this.props, prevProps)) {\n            const formDataChangedFields = getChangedFields(this.props.formData, prevProps.formData);\n            const isSchemaChanged = !deepEquals(prevProps.schema, this.props.schema);\n            // When formData is not an object, getChangedFields returns an empty array.\n            // In this case, deepEquals is most needed to check again.\n            const isFormDataChanged = formDataChangedFields.length > 0 || !deepEquals(prevProps.formData, this.props.formData);\n            const nextState = this.getStateFromProps(this.props, this.props.formData, \n            // If the `schema` has changed, we need to update the retrieved schema.\n            // Or if the `formData` changes, for example in the case of a schema with dependencies that need to\n            //  match one of the subSchemas, the retrieved schema must be updated.\n            isSchemaChanged || isFormDataChanged ? undefined : this.state.retrievedSchema, isSchemaChanged, formDataChangedFields);\n            const shouldUpdate = !deepEquals(nextState, prevState);\n            return { nextState, shouldUpdate };\n        }\n        return { shouldUpdate: false };\n    }\n    /**\n     * `componentDidUpdate` is a React lifecycle method that is invoked immediately after updating occurs. This method is\n     * not called for the initial render.\n     *\n     * Here, it checks if an update is necessary based on the `shouldUpdate` flag received from `getSnapshotBeforeUpdate`.\n     * If an update is required, it applies the next state and, if needed, triggers the `onChange` handler to inform about\n     * changes.\n     *\n     * This method effectively replaces the deprecated `UNSAFE_componentWillReceiveProps`, providing a safer alternative\n     * to handle prop changes and state updates.\n     *\n     * @param _ - The previous set of props.\n     * @param prevState - The previous state of the component before the update.\n     * @param snapshot - The value returned from `getSnapshotBeforeUpdate`.\n     */\n    componentDidUpdate(_, prevState, snapshot) {\n        if (snapshot.shouldUpdate) {\n            const { nextState } = snapshot;\n            if (!deepEquals(nextState.formData, this.props.formData) &&\n                !deepEquals(nextState.formData, prevState.formData) &&\n                this.props.onChange) {\n                this.props.onChange(nextState);\n            }\n            this.setState(nextState);\n        }\n    }\n    /** Extracts the updated state from the given `props` and `inputFormData`. As part of this process, the\n     * `inputFormData` is first processed to add any missing required defaults. After that, the data is run through the\n     * validation process IF required by the `props`.\n     *\n     * @param props - The props passed to the `Form`\n     * @param inputFormData - The new or current data for the `Form`\n     * @param retrievedSchema - An expanded schema, if not provided, it will be retrieved from the `schema` and `formData`.\n     * @param isSchemaChanged - A flag indicating whether the schema has changed.\n     * @param formDataChangedFields - The changed fields of `formData`\n     * @returns - The new state for the `Form`\n     */\n    getStateFromProps(props, inputFormData, retrievedSchema, isSchemaChanged = false, formDataChangedFields = []) {\n        const state = this.state || {};\n        const schema = 'schema' in props ? props.schema : this.props.schema;\n        const uiSchema = ('uiSchema' in props ? props.uiSchema : this.props.uiSchema) || {};\n        const edit = typeof inputFormData !== 'undefined';\n        const liveValidate = 'liveValidate' in props ? props.liveValidate : this.props.liveValidate;\n        const mustValidate = edit && !props.noValidate && liveValidate;\n        const rootSchema = schema;\n        const experimental_defaultFormStateBehavior = 'experimental_defaultFormStateBehavior' in props\n            ? props.experimental_defaultFormStateBehavior\n            : this.props.experimental_defaultFormStateBehavior;\n        const experimental_customMergeAllOf = 'experimental_customMergeAllOf' in props\n            ? props.experimental_customMergeAllOf\n            : this.props.experimental_customMergeAllOf;\n        let schemaUtils = state.schemaUtils;\n        if (!schemaUtils ||\n            schemaUtils.doesSchemaUtilsDiffer(props.validator, rootSchema, experimental_defaultFormStateBehavior, experimental_customMergeAllOf)) {\n            schemaUtils = createSchemaUtils(props.validator, rootSchema, experimental_defaultFormStateBehavior, experimental_customMergeAllOf);\n        }\n        const formData = schemaUtils.getDefaultFormState(schema, inputFormData);\n        const _retrievedSchema = this.updateRetrievedSchema(retrievedSchema ?? schemaUtils.retrieveSchema(schema, formData));\n        const getCurrentErrors = () => {\n            // If the `props.noValidate` option is set or the schema has changed, we reset the error state.\n            if (props.noValidate || isSchemaChanged) {\n                return { errors: [], errorSchema: {} };\n            }\n            else if (!props.liveValidate) {\n                return {\n                    errors: state.schemaValidationErrors || [],\n                    errorSchema: state.schemaValidationErrorSchema || {},\n                };\n            }\n            return {\n                errors: state.errors || [],\n                errorSchema: state.errorSchema || {},\n            };\n        };\n        let errors;\n        let errorSchema;\n        let schemaValidationErrors = state.schemaValidationErrors;\n        let schemaValidationErrorSchema = state.schemaValidationErrorSchema;\n        if (mustValidate) {\n            const schemaValidation = this.validate(formData, schema, schemaUtils, _retrievedSchema);\n            errors = schemaValidation.errors;\n            // If retrievedSchema is undefined which means the schema or formData has changed, we do not merge state.\n            // Else in the case where it hasn't changed, we merge 'state.errorSchema' with 'schemaValidation.errorSchema.' This done to display the raised field error.\n            if (retrievedSchema === undefined) {\n                errorSchema = schemaValidation.errorSchema;\n            }\n            else {\n                errorSchema = mergeObjects(this.state?.errorSchema, schemaValidation.errorSchema, 'preventDuplicates');\n            }\n            schemaValidationErrors = errors;\n            schemaValidationErrorSchema = errorSchema;\n        }\n        else {\n            const currentErrors = getCurrentErrors();\n            errors = currentErrors.errors;\n            errorSchema = currentErrors.errorSchema;\n            if (formDataChangedFields.length > 0) {\n                const newErrorSchema = formDataChangedFields.reduce((acc, key) => {\n                    acc[key] = undefined;\n                    return acc;\n                }, {});\n                errorSchema = schemaValidationErrorSchema = mergeObjects(currentErrors.errorSchema, newErrorSchema, 'preventDuplicates');\n            }\n        }\n        if (props.extraErrors) {\n            const merged = validationDataMerge({ errorSchema, errors }, props.extraErrors);\n            errorSchema = merged.errorSchema;\n            errors = merged.errors;\n        }\n        const idSchema = schemaUtils.toIdSchema(_retrievedSchema, uiSchema['ui:rootFieldId'], formData, props.idPrefix, props.idSeparator);\n        const nextState = {\n            schemaUtils,\n            schema,\n            uiSchema,\n            idSchema,\n            formData,\n            edit,\n            errors,\n            errorSchema,\n            schemaValidationErrors,\n            schemaValidationErrorSchema,\n            retrievedSchema: _retrievedSchema,\n        };\n        return nextState;\n    }\n    /** React lifecycle method that is used to determine whether component should be updated.\n     *\n     * @param nextProps - The next version of the props\n     * @param nextState - The next version of the state\n     * @returns - True if the component should be updated, false otherwise\n     */\n    shouldComponentUpdate(nextProps, nextState) {\n        return shouldRender(this, nextProps, nextState);\n    }\n    /** Gets the previously raised customValidate errors.\n     *\n     * @returns the previous customValidate errors\n     */\n    getPreviousCustomValidateErrors() {\n        const { customValidate, uiSchema } = this.props;\n        const prevFormData = this.state.formData;\n        let customValidateErrors = {};\n        if (typeof customValidate === 'function') {\n            const errorHandler = customValidate(prevFormData, createErrorHandler(prevFormData), uiSchema);\n            const userErrorSchema = unwrapErrorHandler(errorHandler);\n            customValidateErrors = userErrorSchema;\n        }\n        return customValidateErrors;\n    }\n    /** Validates the `formData` against the `schema` using the `altSchemaUtils` (if provided otherwise it uses the\n     * `schemaUtils` in the state), returning the results.\n     *\n     * @param formData - The new form data to validate\n     * @param schema - The schema used to validate against\n     * @param altSchemaUtils - The alternate schemaUtils to use for validation\n     */\n    validate(formData, schema = this.props.schema, altSchemaUtils, retrievedSchema) {\n        const schemaUtils = altSchemaUtils ? altSchemaUtils : this.state.schemaUtils;\n        const { customValidate, transformErrors, uiSchema } = this.props;\n        const resolvedSchema = retrievedSchema ?? schemaUtils.retrieveSchema(schema, formData);\n        return schemaUtils\n            .getValidator()\n            .validateFormData(formData, resolvedSchema, customValidate, transformErrors, uiSchema);\n    }\n    /** Renders any errors contained in the `state` in using the `ErrorList`, if not disabled by `showErrorList`. */\n    renderErrors(registry) {\n        const { errors, errorSchema, schema, uiSchema } = this.state;\n        const { formContext } = this.props;\n        const options = getUiOptions(uiSchema);\n        const ErrorListTemplate = getTemplate('ErrorListTemplate', registry, options);\n        if (errors && errors.length) {\n            return (_jsx(ErrorListTemplate, { errors: errors, errorSchema: errorSchema || {}, schema: schema, uiSchema: uiSchema, formContext: formContext, registry: registry }));\n        }\n        return null;\n    }\n    /** Returns the `formData` with only the elements specified in the `fields` list\n     *\n     * @param formData - The data for the `Form`\n     * @param fields - The fields to keep while filtering\n     */\n    getUsedFormData = (formData, fields) => {\n        // For the case of a single input form\n        if (fields.length === 0 && typeof formData !== 'object') {\n            return formData;\n        }\n        // _pick has incorrect type definition, it works with string[][], because lodash/hasIn supports it\n        const data = _pick(formData, fields);\n        if (Array.isArray(formData)) {\n            return Object.keys(data).map((key) => data[key]);\n        }\n        return data;\n    };\n    /** Returns the list of field names from inspecting the `pathSchema` as well as using the `formData`\n     *\n     * @param pathSchema - The `PathSchema` object for the form\n     * @param [formData] - The form data to use while checking for empty objects/arrays\n     */\n    getFieldNames = (pathSchema, formData) => {\n        const getAllPaths = (_obj, acc = [], paths = [[]]) => {\n            Object.keys(_obj).forEach((key) => {\n                if (typeof _obj[key] === 'object') {\n                    const newPaths = paths.map((path) => [...path, key]);\n                    // If an object is marked with additionalProperties, all its keys are valid\n                    if (_obj[key][RJSF_ADDITIONAL_PROPERTIES_FLAG] && _obj[key][NAME_KEY] !== '') {\n                        acc.push(_obj[key][NAME_KEY]);\n                    }\n                    else {\n                        getAllPaths(_obj[key], acc, newPaths);\n                    }\n                }\n                else if (key === NAME_KEY && _obj[key] !== '') {\n                    paths.forEach((path) => {\n                        const formValue = _get(formData, path);\n                        // adds path to fieldNames if it points to a value\n                        // or an empty object/array\n                        if (typeof formValue !== 'object' ||\n                            _isEmpty(formValue) ||\n                            (Array.isArray(formValue) && formValue.every((val) => typeof val !== 'object'))) {\n                            acc.push(path);\n                        }\n                    });\n                }\n            });\n            return acc;\n        };\n        return getAllPaths(pathSchema);\n    };\n    /** Returns the `formData` after filtering to remove any extra data not in a form field\n     *\n     * @param formData - The data for the `Form`\n     * @returns The `formData` after omitting extra data\n     */\n    omitExtraData = (formData) => {\n        const { schema, schemaUtils } = this.state;\n        const retrievedSchema = schemaUtils.retrieveSchema(schema, formData);\n        const pathSchema = schemaUtils.toPathSchema(retrievedSchema, '', formData);\n        const fieldNames = this.getFieldNames(pathSchema, formData);\n        const newFormData = this.getUsedFormData(formData, fieldNames);\n        return newFormData;\n    };\n    // Filtering errors based on your retrieved schema to only show errors for properties in the selected branch.\n    filterErrorsBasedOnSchema(schemaErrors, resolvedSchema, formData) {\n        const { retrievedSchema, schemaUtils } = this.state;\n        const _retrievedSchema = resolvedSchema ?? retrievedSchema;\n        const pathSchema = schemaUtils.toPathSchema(_retrievedSchema, '', formData);\n        const fieldNames = this.getFieldNames(pathSchema, formData);\n        const filteredErrors = _pick(schemaErrors, fieldNames);\n        // If the root schema is of a primitive type, do not filter out the __errors\n        if (resolvedSchema?.type !== 'object' && resolvedSchema?.type !== 'array') {\n            filteredErrors.__errors = schemaErrors.__errors;\n        }\n        const prevCustomValidateErrors = this.getPreviousCustomValidateErrors();\n        // Filtering out the previous raised customValidate errors so that they are cleared when no longer valid.\n        const filterPreviousCustomErrors = (errors = [], prevCustomErrors) => {\n            if (errors.length === 0) {\n                return errors;\n            }\n            return errors.filter((error) => {\n                return !prevCustomErrors.includes(error);\n            });\n        };\n        // Removing undefined, null and empty errors.\n        const filterNilOrEmptyErrors = (errors, previousCustomValidateErrors = {}) => {\n            _forEach(errors, (errorAtKey, errorKey) => {\n                const prevCustomValidateErrorAtKey = previousCustomValidateErrors[errorKey];\n                if (_isNil(errorAtKey) || (Array.isArray(errorAtKey) && errorAtKey.length === 0)) {\n                    delete errors[errorKey];\n                }\n                else if (isObject(errorAtKey) &&\n                    isObject(prevCustomValidateErrorAtKey) &&\n                    Array.isArray(prevCustomValidateErrorAtKey?.__errors)) {\n                    // if previous customValidate error is an object and has __errors array, filter out the errors previous customValidate errors.\n                    errors[errorKey] = filterPreviousCustomErrors(errorAtKey.__errors, prevCustomValidateErrorAtKey.__errors);\n                }\n                else if (typeof errorAtKey === 'object' && !Array.isArray(errorAtKey.__errors)) {\n                    filterNilOrEmptyErrors(errorAtKey, previousCustomValidateErrors[errorKey]);\n                }\n            });\n            return errors;\n        };\n        return filterNilOrEmptyErrors(filteredErrors, prevCustomValidateErrors);\n    }\n    /** Function to handle changes made to a field in the `Form`. This handler receives an entirely new copy of the\n     * `formData` along with a new `ErrorSchema`. It will first update the `formData` with any missing default fields and\n     * then, if `omitExtraData` and `liveOmit` are turned on, the `formData` will be filtered to remove any extra data not\n     * in a form field. Then, the resulting formData will be validated if required. The state will be updated with the new\n     * updated (potentially filtered) `formData`, any errors that resulted from validation. Finally the `onChange`\n     * callback will be called if specified with the updated state.\n     *\n     * @param formData - The new form data from a change to a field\n     * @param newErrorSchema - The new `ErrorSchema` based on the field change\n     * @param id - The id of the field that caused the change\n     */\n    onChange = (formData, newErrorSchema, id) => {\n        const { extraErrors, omitExtraData, liveOmit, noValidate, liveValidate, onChange } = this.props;\n        const { schemaUtils, schema } = this.state;\n        let retrievedSchema = this.state.retrievedSchema;\n        if (isObject(formData) || Array.isArray(formData)) {\n            const newState = this.getStateFromProps(this.props, formData);\n            formData = newState.formData;\n            retrievedSchema = newState.retrievedSchema;\n        }\n        const mustValidate = !noValidate && liveValidate;\n        let state = { formData, schema };\n        let newFormData = formData;\n        if (omitExtraData === true && liveOmit === true) {\n            newFormData = this.omitExtraData(formData);\n            state = {\n                formData: newFormData,\n            };\n        }\n        if (mustValidate) {\n            const schemaValidation = this.validate(newFormData, schema, schemaUtils, retrievedSchema);\n            let errors = schemaValidation.errors;\n            let errorSchema = schemaValidation.errorSchema;\n            const schemaValidationErrors = errors;\n            const schemaValidationErrorSchema = errorSchema;\n            if (extraErrors) {\n                const merged = validationDataMerge(schemaValidation, extraErrors);\n                errorSchema = merged.errorSchema;\n                errors = merged.errors;\n            }\n            // Merging 'newErrorSchema' into 'errorSchema' to display the custom raised errors.\n            if (newErrorSchema) {\n                const filteredErrors = this.filterErrorsBasedOnSchema(newErrorSchema, retrievedSchema, newFormData);\n                errorSchema = mergeObjects(errorSchema, filteredErrors, 'preventDuplicates');\n            }\n            state = {\n                formData: newFormData,\n                errors,\n                errorSchema,\n                schemaValidationErrors,\n                schemaValidationErrorSchema,\n            };\n        }\n        else if (!noValidate && newErrorSchema) {\n            const errorSchema = extraErrors\n                ? mergeObjects(newErrorSchema, extraErrors, 'preventDuplicates')\n                : newErrorSchema;\n            state = {\n                formData: newFormData,\n                errorSchema: errorSchema,\n                errors: toErrorList(errorSchema),\n            };\n        }\n        this.setState(state, () => onChange && onChange({ ...this.state, ...state }, id));\n    };\n    /**\n     * If the retrievedSchema has changed the new retrievedSchema is returned.\n     * Otherwise, the old retrievedSchema is returned to persist reference.\n     * -  This ensures that AJV retrieves the schema from the cache when it has not changed,\n     *    avoiding the performance cost of recompiling the schema.\n     *\n     * @param retrievedSchema The new retrieved schema.\n     * @returns The new retrieved schema if it has changed, else the old retrieved schema.\n     */\n    updateRetrievedSchema(retrievedSchema) {\n        const isTheSame = deepEquals(retrievedSchema, this.state?.retrievedSchema);\n        return isTheSame ? this.state.retrievedSchema : retrievedSchema;\n    }\n    /**\n     * Callback function to handle reset form data.\n     * - Reset all fields with default values.\n     * - Reset validations and errors\n     *\n     */\n    reset = () => {\n        const { onChange } = this.props;\n        const newState = this.getStateFromProps(this.props, undefined);\n        const newFormData = newState.formData;\n        const state = {\n            formData: newFormData,\n            errorSchema: {},\n            errors: [],\n            schemaValidationErrors: [],\n            schemaValidationErrorSchema: {},\n        };\n        this.setState(state, () => onChange && onChange({ ...this.state, ...state }));\n    };\n    /** Callback function to handle when a field on the form is blurred. Calls the `onBlur` callback for the `Form` if it\n     * was provided.\n     *\n     * @param id - The unique `id` of the field that was blurred\n     * @param data - The data associated with the field that was blurred\n     */\n    onBlur = (id, data) => {\n        const { onBlur } = this.props;\n        if (onBlur) {\n            onBlur(id, data);\n        }\n    };\n    /** Callback function to handle when a field on the form is focused. Calls the `onFocus` callback for the `Form` if it\n     * was provided.\n     *\n     * @param id - The unique `id` of the field that was focused\n     * @param data - The data associated with the field that was focused\n     */\n    onFocus = (id, data) => {\n        const { onFocus } = this.props;\n        if (onFocus) {\n            onFocus(id, data);\n        }\n    };\n    /** Callback function to handle when the form is submitted. First, it prevents the default event behavior. Nothing\n     * happens if the target and currentTarget of the event are not the same. It will omit any extra data in the\n     * `formData` in the state if `omitExtraData` is true. It will validate the resulting `formData`, reporting errors\n     * via the `onError()` callback unless validation is disabled. Finally, it will add in any `extraErrors` and then call\n     * back the `onSubmit` callback if it was provided.\n     *\n     * @param event - The submit HTML form event\n     */\n    onSubmit = (event) => {\n        event.preventDefault();\n        if (event.target !== event.currentTarget) {\n            return;\n        }\n        event.persist();\n        const { omitExtraData, extraErrors, noValidate, onSubmit } = this.props;\n        let { formData: newFormData } = this.state;\n        if (omitExtraData === true) {\n            newFormData = this.omitExtraData(newFormData);\n        }\n        if (noValidate || this.validateFormWithFormData(newFormData)) {\n            // There are no errors generated through schema validation.\n            // Check for user provided errors and update state accordingly.\n            const errorSchema = extraErrors || {};\n            const errors = extraErrors ? toErrorList(extraErrors) : [];\n            this.setState({\n                formData: newFormData,\n                errors,\n                errorSchema,\n                schemaValidationErrors: [],\n                schemaValidationErrorSchema: {},\n            }, () => {\n                if (onSubmit) {\n                    onSubmit({ ...this.state, formData: newFormData, status: 'submitted' }, event);\n                }\n            });\n        }\n    };\n    /** Returns the registry for the form */\n    getRegistry() {\n        const { translateString: customTranslateString, uiSchema = {} } = this.props;\n        const { schemaUtils } = this.state;\n        const { fields, templates, widgets, formContext, translateString } = getDefaultRegistry();\n        return {\n            fields: { ...fields, ...this.props.fields },\n            templates: {\n                ...templates,\n                ...this.props.templates,\n                ButtonTemplates: {\n                    ...templates.ButtonTemplates,\n                    ...this.props.templates?.ButtonTemplates,\n                },\n            },\n            widgets: { ...widgets, ...this.props.widgets },\n            rootSchema: this.props.schema,\n            formContext: this.props.formContext || formContext,\n            schemaUtils,\n            translateString: customTranslateString || translateString,\n            globalUiOptions: uiSchema[UI_GLOBAL_OPTIONS_KEY],\n        };\n    }\n    /** Provides a function that can be used to programmatically submit the `Form` */\n    submit = () => {\n        if (this.formElement.current) {\n            const submitCustomEvent = new CustomEvent('submit', {\n                cancelable: true,\n            });\n            submitCustomEvent.preventDefault();\n            this.formElement.current.dispatchEvent(submitCustomEvent);\n            this.formElement.current.requestSubmit();\n        }\n    };\n    /** Attempts to focus on the field associated with the `error`. Uses the `property` field to compute path of the error\n     * field, then, using the `idPrefix` and `idSeparator` converts that path into an id. Then the input element with that\n     * id is attempted to be found using the `formElement` ref. If it is located, then it is focused.\n     *\n     * @param error - The error on which to focus\n     */\n    focusOnError(error) {\n        const { idPrefix = 'root', idSeparator = '_' } = this.props;\n        const { property } = error;\n        const path = _toPath(property);\n        if (path[0] === '') {\n            // Most of the time the `.foo` property results in the first element being empty, so replace it with the idPrefix\n            path[0] = idPrefix;\n        }\n        else {\n            // Otherwise insert the idPrefix into the first location using unshift\n            path.unshift(idPrefix);\n        }\n        const elementId = path.join(idSeparator);\n        let field = this.formElement.current.elements[elementId];\n        if (!field) {\n            // if not an exact match, try finding an input starting with the element id (like radio buttons or checkboxes)\n            field = this.formElement.current.querySelector(`input[id^=\"${elementId}\"`);\n        }\n        if (field && field.length) {\n            // If we got a list with length > 0\n            field = field[0];\n        }\n        if (field) {\n            field.focus();\n        }\n    }\n    /** Validates the form using the given `formData`. For use on form submission or on programmatic validation.\n     * If `onError` is provided, then it will be called with the list of errors.\n     *\n     * @param formData - The form data to validate\n     * @returns - True if the form is valid, false otherwise.\n     */\n    validateFormWithFormData = (formData) => {\n        const { extraErrors, extraErrorsBlockSubmit, focusOnFirstError, onError } = this.props;\n        const { errors: prevErrors } = this.state;\n        const schemaValidation = this.validate(formData);\n        let errors = schemaValidation.errors;\n        let errorSchema = schemaValidation.errorSchema;\n        const schemaValidationErrors = errors;\n        const schemaValidationErrorSchema = errorSchema;\n        const hasError = errors.length > 0 || (extraErrors && extraErrorsBlockSubmit);\n        if (hasError) {\n            if (extraErrors) {\n                const merged = validationDataMerge(schemaValidation, extraErrors);\n                errorSchema = merged.errorSchema;\n                errors = merged.errors;\n            }\n            if (focusOnFirstError) {\n                if (typeof focusOnFirstError === 'function') {\n                    focusOnFirstError(errors[0]);\n                }\n                else {\n                    this.focusOnError(errors[0]);\n                }\n            }\n            this.setState({\n                errors,\n                errorSchema,\n                schemaValidationErrors,\n                schemaValidationErrorSchema,\n            }, () => {\n                if (onError) {\n                    onError(errors);\n                }\n                else {\n                    console.error('Form validation failed', errors);\n                }\n            });\n        }\n        else if (prevErrors.length > 0) {\n            this.setState({\n                errors: [],\n                errorSchema: {},\n                schemaValidationErrors: [],\n                schemaValidationErrorSchema: {},\n            });\n        }\n        return !hasError;\n    };\n    /** Programmatically validate the form.  If `omitExtraData` is true, the `formData` will first be filtered to remove\n     * any extra data not in a form field. If `onError` is provided, then it will be called with the list of errors the\n     * same way as would happen on form submission.\n     *\n     * @returns - True if the form is valid, false otherwise.\n     */\n    validateForm() {\n        const { omitExtraData } = this.props;\n        let { formData: newFormData } = this.state;\n        if (omitExtraData === true) {\n            newFormData = this.omitExtraData(newFormData);\n        }\n        return this.validateFormWithFormData(newFormData);\n    }\n    /** Renders the `Form` fields inside the <form> | `tagName` or `_internalFormWrapper`, rendering any errors if\n     * needed along with the submit button or any children of the form.\n     */\n    render() {\n        const { children, id, idPrefix, idSeparator, className = '', tagName, name, method, target, action, autoComplete, enctype, acceptcharset, acceptCharset, noHtml5Validate = false, disabled, readonly, formContext, showErrorList = 'top', _internalFormWrapper, } = this.props;\n        const { schema, uiSchema, formData, errorSchema, idSchema } = this.state;\n        const registry = this.getRegistry();\n        const { SchemaField: _SchemaField } = registry.fields;\n        const { SubmitButton } = registry.templates.ButtonTemplates;\n        // The `semantic-ui` and `material-ui` themes have `_internalFormWrapper`s that take an `as` prop that is the\n        // PropTypes.elementType to use for the inner tag, so we'll need to pass `tagName` along if it is provided.\n        // NOTE, the `as` prop is native to `semantic-ui` and is emulated in the `material-ui` theme\n        const as = _internalFormWrapper ? tagName : undefined;\n        const FormTag = _internalFormWrapper || tagName || 'form';\n        let { [SUBMIT_BTN_OPTIONS_KEY]: submitOptions = {} } = getUiOptions(uiSchema);\n        if (disabled) {\n            submitOptions = { ...submitOptions, props: { ...submitOptions.props, disabled: true } };\n        }\n        const submitUiSchema = { [UI_OPTIONS_KEY]: { [SUBMIT_BTN_OPTIONS_KEY]: submitOptions } };\n        return (_jsxs(FormTag, { className: className ? className : 'rjsf', id: id, name: name, method: method, target: target, action: action, autoComplete: autoComplete, encType: enctype, acceptCharset: acceptCharset || acceptcharset, noValidate: noHtml5Validate, onSubmit: this.onSubmit, as: as, ref: this.formElement, children: [showErrorList === 'top' && this.renderErrors(registry), _jsx(_SchemaField, { name: '', schema: schema, uiSchema: uiSchema, errorSchema: errorSchema, idSchema: idSchema, idPrefix: idPrefix, idSeparator: idSeparator, formContext: formContext, formData: formData, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, registry: registry, disabled: disabled, readonly: readonly }), children ? children : _jsx(SubmitButton, { uiSchema: submitUiSchema, registry: registry }), showErrorList === 'bottom' && this.renderErrors(registry)] }));\n    }\n}\n", "import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nexport default basePick;\n", "import basePick from './_basePick.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nexport default pick;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Component } from 'react';\nimport { getTemplate, getWidget, getUiOptions, isFixedItems, allowAdditionalItems, isCustomWidget, optionsList, TranslatableString, ITEMS_KEY, } from '@rjsf/utils';\nimport cloneDeep from 'lodash-es/cloneDeep.js';\nimport get from 'lodash-es/get.js';\nimport isObject from 'lodash-es/isObject.js';\nimport set from 'lodash-es/set.js';\nimport { nanoid } from 'nanoid';\n/** Used to generate a unique ID for an element in a row */\nfunction generateRowId() {\n    return nanoid();\n}\n/** Converts the `formData` into `KeyedFormDataType` data, using the `generateRowId()` function to create the key\n *\n * @param formData - The data for the form\n * @returns - The `formData` converted into a `KeyedFormDataType` element\n */\nfunction generateKeyedFormData(formData) {\n    return !Array.isArray(formData)\n        ? []\n        : formData.map((item) => {\n            return {\n                key: generateRowId(),\n                item,\n            };\n        });\n}\n/** Converts `KeyedFormDataType` data into the inner `formData`\n *\n * @param keyedFormData - The `KeyedFormDataType` to be converted\n * @returns - The inner `formData` item(s) in the `keyedFormData`\n */\nfunction keyedToPlainFormData(keyedFormData) {\n    if (Array.isArray(keyedFormData)) {\n        return keyedFormData.map((keyedItem) => keyedItem.item);\n    }\n    return [];\n}\n/** The `ArrayField` component is used to render a field in the schema that is of type `array`. It supports both normal\n * and fixed array, allowing user to add and remove elements from the array data.\n */\nclass ArrayField extends Component {\n    /** Constructs an `ArrayField` from the `props`, generating the initial keyed data from the `formData`\n     *\n     * @param props - The `FieldProps` for this template\n     */\n    constructor(props) {\n        super(props);\n        const { formData = [] } = props;\n        const keyedFormData = generateKeyedFormData(formData);\n        this.state = {\n            keyedFormData,\n            updatedKeyedFormData: false,\n        };\n    }\n    /** React lifecycle method that is called when the props are about to change allowing the state to be updated. It\n     * regenerates the keyed form data and returns it\n     *\n     * @param nextProps - The next set of props data\n     * @param prevState - The previous set of state data\n     */\n    static getDerivedStateFromProps(nextProps, prevState) {\n        // Don't call getDerivedStateFromProps if keyed formdata was just updated.\n        if (prevState.updatedKeyedFormData) {\n            return {\n                updatedKeyedFormData: false,\n            };\n        }\n        const nextFormData = Array.isArray(nextProps.formData) ? nextProps.formData : [];\n        const previousKeyedFormData = prevState.keyedFormData || [];\n        const newKeyedFormData = nextFormData.length === previousKeyedFormData.length\n            ? previousKeyedFormData.map((previousKeyedFormDatum, index) => {\n                return {\n                    key: previousKeyedFormDatum.key,\n                    item: nextFormData[index],\n                };\n            })\n            : generateKeyedFormData(nextFormData);\n        return {\n            keyedFormData: newKeyedFormData,\n        };\n    }\n    /** Returns the appropriate title for an item by getting first the title from the schema.items, then falling back to\n     * the description from the schema.items, and finally the string \"Item\"\n     */\n    get itemTitle() {\n        const { schema, registry } = this.props;\n        const { translateString } = registry;\n        return get(schema, [ITEMS_KEY, 'title'], get(schema, [ITEMS_KEY, 'description'], translateString(TranslatableString.ArrayItemTitle)));\n    }\n    /** Determines whether the item described in the schema is always required, which is determined by whether any item\n     * may be null.\n     *\n     * @param itemSchema - The schema for the item\n     * @return - True if the item schema type does not contain the \"null\" type\n     */\n    isItemRequired(itemSchema) {\n        if (Array.isArray(itemSchema.type)) {\n            // While we don't yet support composite/nullable jsonschema types, it's\n            // future-proof to check for requirement against these.\n            return !itemSchema.type.includes('null');\n        }\n        // All non-null array item types are inherently required by design\n        return itemSchema.type !== 'null';\n    }\n    /** Determines whether more items can be added to the array. If the uiSchema indicates the array doesn't allow adding\n     * then false is returned. Otherwise, if the schema indicates that there are a maximum number of items and the\n     * `formData` matches that value, then false is returned, otherwise true is returned.\n     *\n     * @param formItems - The list of items in the form\n     * @returns - True if the item is addable otherwise false\n     */\n    canAddItem(formItems) {\n        const { schema, uiSchema, registry } = this.props;\n        let { addable } = getUiOptions(uiSchema, registry.globalUiOptions);\n        if (addable !== false) {\n            // if ui:options.addable was not explicitly set to false, we can add\n            // another item if we have not exceeded maxItems yet\n            if (schema.maxItems !== undefined) {\n                addable = formItems.length < schema.maxItems;\n            }\n            else {\n                addable = true;\n            }\n        }\n        return addable;\n    }\n    /** Returns the default form information for an item based on the schema for that item. Deals with the possibility\n     * that the schema is fixed and allows additional items.\n     */\n    _getNewFormDataRow = () => {\n        const { schema, registry } = this.props;\n        const { schemaUtils } = registry;\n        let itemSchema = schema.items;\n        if (isFixedItems(schema) && allowAdditionalItems(schema)) {\n            itemSchema = schema.additionalItems;\n        }\n        // Cast this as a T to work around schema utils being for T[] caused by the FieldProps<T[], S, F> call on the class\n        return schemaUtils.getDefaultFormState(itemSchema);\n    };\n    /** Callback handler for when the user clicks on the add or add at index buttons. Creates a new row of keyed form data\n     * either at the end of the list (when index is not specified) or inserted at the `index` when it is, adding it into\n     * the state, and then returning `onChange()` with the plain form data converted from the keyed data\n     *\n     * @param event - The event for the click\n     * @param [index] - The optional index at which to add the new data\n     */\n    _handleAddClick(event, index) {\n        if (event) {\n            event.preventDefault();\n        }\n        const { onChange, errorSchema } = this.props;\n        const { keyedFormData } = this.state;\n        // refs #195: revalidate to ensure properly reindexing errors\n        let newErrorSchema;\n        if (errorSchema) {\n            newErrorSchema = {};\n            for (const idx in errorSchema) {\n                const i = parseInt(idx);\n                if (index === undefined || i < index) {\n                    set(newErrorSchema, [i], errorSchema[idx]);\n                }\n                else if (i >= index) {\n                    set(newErrorSchema, [i + 1], errorSchema[idx]);\n                }\n            }\n        }\n        const newKeyedFormDataRow = {\n            key: generateRowId(),\n            item: this._getNewFormDataRow(),\n        };\n        const newKeyedFormData = [...keyedFormData];\n        if (index !== undefined) {\n            newKeyedFormData.splice(index, 0, newKeyedFormDataRow);\n        }\n        else {\n            newKeyedFormData.push(newKeyedFormDataRow);\n        }\n        this.setState({\n            keyedFormData: newKeyedFormData,\n            updatedKeyedFormData: true,\n        }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n    }\n    /** Callback handler for when the user clicks on the add button. Creates a new row of keyed form data at the end of\n     * the list, adding it into the state, and then returning `onChange()` with the plain form data converted from the\n     * keyed data\n     *\n     * @param event - The event for the click\n     */\n    onAddClick = (event) => {\n        this._handleAddClick(event);\n    };\n    /** Callback handler for when the user clicks on the add button on an existing array element. Creates a new row of\n     * keyed form data inserted at the `index`, adding it into the state, and then returning `onChange()` with the plain\n     * form data converted from the keyed data\n     *\n     * @param index - The index at which the add button is clicked\n     */\n    onAddIndexClick = (index) => {\n        return (event) => {\n            this._handleAddClick(event, index);\n        };\n    };\n    /** Callback handler for when the user clicks on the copy button on an existing array element. Clones the row of\n     * keyed form data at the `index` into the next position in the state, and then returning `onChange()` with the plain\n     * form data converted from the keyed data\n     *\n     * @param index - The index at which the copy button is clicked\n     */\n    onCopyIndexClick = (index) => {\n        return (event) => {\n            if (event) {\n                event.preventDefault();\n            }\n            const { onChange, errorSchema } = this.props;\n            const { keyedFormData } = this.state;\n            // refs #195: revalidate to ensure properly reindexing errors\n            let newErrorSchema;\n            if (errorSchema) {\n                newErrorSchema = {};\n                for (const idx in errorSchema) {\n                    const i = parseInt(idx);\n                    if (i <= index) {\n                        set(newErrorSchema, [i], errorSchema[idx]);\n                    }\n                    else if (i > index) {\n                        set(newErrorSchema, [i + 1], errorSchema[idx]);\n                    }\n                }\n            }\n            const newKeyedFormDataRow = {\n                key: generateRowId(),\n                item: cloneDeep(keyedFormData[index].item),\n            };\n            const newKeyedFormData = [...keyedFormData];\n            if (index !== undefined) {\n                newKeyedFormData.splice(index + 1, 0, newKeyedFormDataRow);\n            }\n            else {\n                newKeyedFormData.push(newKeyedFormDataRow);\n            }\n            this.setState({\n                keyedFormData: newKeyedFormData,\n                updatedKeyedFormData: true,\n            }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n        };\n    };\n    /** Callback handler for when the user clicks on the remove button on an existing array element. Removes the row of\n     * keyed form data at the `index` in the state, and then returning `onChange()` with the plain form data converted\n     * from the keyed data\n     *\n     * @param index - The index at which the remove button is clicked\n     */\n    onDropIndexClick = (index) => {\n        return (event) => {\n            if (event) {\n                event.preventDefault();\n            }\n            const { onChange, errorSchema } = this.props;\n            const { keyedFormData } = this.state;\n            // refs #195: revalidate to ensure properly reindexing errors\n            let newErrorSchema;\n            if (errorSchema) {\n                newErrorSchema = {};\n                for (const idx in errorSchema) {\n                    const i = parseInt(idx);\n                    if (i < index) {\n                        set(newErrorSchema, [i], errorSchema[idx]);\n                    }\n                    else if (i > index) {\n                        set(newErrorSchema, [i - 1], errorSchema[idx]);\n                    }\n                }\n            }\n            const newKeyedFormData = keyedFormData.filter((_, i) => i !== index);\n            this.setState({\n                keyedFormData: newKeyedFormData,\n                updatedKeyedFormData: true,\n            }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n        };\n    };\n    /** Callback handler for when the user clicks on one of the move item buttons on an existing array element. Moves the\n     * row of keyed form data at the `index` to the `newIndex` in the state, and then returning `onChange()` with the\n     * plain form data converted from the keyed data\n     *\n     * @param index - The index of the item to move\n     * @param newIndex - The index to where the item is to be moved\n     */\n    onReorderClick = (index, newIndex) => {\n        return (event) => {\n            if (event) {\n                event.preventDefault();\n                event.currentTarget.blur();\n            }\n            const { onChange, errorSchema } = this.props;\n            let newErrorSchema;\n            if (errorSchema) {\n                newErrorSchema = {};\n                for (const idx in errorSchema) {\n                    const i = parseInt(idx);\n                    if (i == index) {\n                        set(newErrorSchema, [newIndex], errorSchema[index]);\n                    }\n                    else if (i == newIndex) {\n                        set(newErrorSchema, [index], errorSchema[newIndex]);\n                    }\n                    else {\n                        set(newErrorSchema, [idx], errorSchema[i]);\n                    }\n                }\n            }\n            const { keyedFormData } = this.state;\n            function reOrderArray() {\n                // Copy item\n                const _newKeyedFormData = keyedFormData.slice();\n                // Moves item from index to newIndex\n                _newKeyedFormData.splice(index, 1);\n                _newKeyedFormData.splice(newIndex, 0, keyedFormData[index]);\n                return _newKeyedFormData;\n            }\n            const newKeyedFormData = reOrderArray();\n            this.setState({\n                keyedFormData: newKeyedFormData,\n            }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n        };\n    };\n    /** Callback handler used to deal with changing the value of the data in the array at the `index`. Calls the\n     * `onChange` callback with the updated form data\n     *\n     * @param index - The index of the item being changed\n     */\n    onChangeForIndex = (index) => {\n        return (value, newErrorSchema, id) => {\n            const { formData, onChange, errorSchema } = this.props;\n            const arrayData = Array.isArray(formData) ? formData : [];\n            const newFormData = arrayData.map((item, i) => {\n                // We need to treat undefined items as nulls to have validation.\n                // See https://github.com/tdegrunt/jsonschema/issues/206\n                const jsonValue = typeof value === 'undefined' ? null : value;\n                return index === i ? jsonValue : item;\n            });\n            onChange(newFormData, errorSchema &&\n                errorSchema && {\n                ...errorSchema,\n                [index]: newErrorSchema,\n            }, id);\n        };\n    };\n    /** Callback handler used to change the value for a checkbox */\n    onSelectChange = (value) => {\n        const { onChange, idSchema } = this.props;\n        onChange(value, undefined, idSchema && idSchema.$id);\n    };\n    /** Renders the `ArrayField` depending on the specific needs of the schema and uischema elements\n     */\n    render() {\n        const { schema, uiSchema, idSchema, registry } = this.props;\n        const { schemaUtils, translateString } = registry;\n        if (!(ITEMS_KEY in schema)) {\n            const uiOptions = getUiOptions(uiSchema);\n            const UnsupportedFieldTemplate = getTemplate('UnsupportedFieldTemplate', registry, uiOptions);\n            return (_jsx(UnsupportedFieldTemplate, { schema: schema, idSchema: idSchema, reason: translateString(TranslatableString.MissingItems), registry: registry }));\n        }\n        if (schemaUtils.isMultiSelect(schema)) {\n            // If array has enum or uniqueItems set to true, call renderMultiSelect() to render the default multiselect widget or a custom widget, if specified.\n            return this.renderMultiSelect();\n        }\n        if (isCustomWidget(uiSchema)) {\n            return this.renderCustomWidget();\n        }\n        if (isFixedItems(schema)) {\n            return this.renderFixedArray();\n        }\n        if (schemaUtils.isFilesArray(schema, uiSchema)) {\n            return this.renderFiles();\n        }\n        return this.renderNormalArray();\n    }\n    /** Renders a normal array without any limitations of length\n     */\n    renderNormalArray() {\n        const { schema, uiSchema = {}, errorSchema, idSchema, name, title, disabled = false, readonly = false, autofocus = false, required = false, registry, onBlur, onFocus, idPrefix, idSeparator = '_', rawErrors, } = this.props;\n        const { keyedFormData } = this.state;\n        const fieldTitle = schema.title || title || name;\n        const { schemaUtils, formContext } = registry;\n        const uiOptions = getUiOptions(uiSchema);\n        const _schemaItems = isObject(schema.items) ? schema.items : {};\n        const itemsSchema = schemaUtils.retrieveSchema(_schemaItems);\n        const formData = keyedToPlainFormData(this.state.keyedFormData);\n        const canAdd = this.canAddItem(formData);\n        const arrayProps = {\n            canAdd,\n            items: keyedFormData.map((keyedItem, index) => {\n                const { key, item } = keyedItem;\n                // While we are actually dealing with a single item of type T, the types require a T[], so cast\n                const itemCast = item;\n                const itemSchema = schemaUtils.retrieveSchema(_schemaItems, itemCast);\n                const itemErrorSchema = errorSchema ? errorSchema[index] : undefined;\n                const itemIdPrefix = idSchema.$id + idSeparator + index;\n                const itemIdSchema = schemaUtils.toIdSchema(itemSchema, itemIdPrefix, itemCast, idPrefix, idSeparator);\n                return this.renderArrayFieldItem({\n                    key,\n                    index,\n                    name: name && `${name}-${index}`,\n                    title: fieldTitle ? `${fieldTitle}-${index + 1}` : undefined,\n                    canAdd,\n                    canMoveUp: index > 0,\n                    canMoveDown: index < formData.length - 1,\n                    itemSchema,\n                    itemIdSchema,\n                    itemErrorSchema,\n                    itemData: itemCast,\n                    itemUiSchema: uiSchema.items,\n                    autofocus: autofocus && index === 0,\n                    onBlur,\n                    onFocus,\n                    rawErrors,\n                    totalItems: keyedFormData.length,\n                });\n            }),\n            className: `field field-array field-array-of-${itemsSchema.type}`,\n            disabled,\n            idSchema,\n            uiSchema,\n            onAddClick: this.onAddClick,\n            readonly,\n            required,\n            schema,\n            title: fieldTitle,\n            formContext,\n            formData,\n            rawErrors,\n            registry,\n        };\n        const Template = getTemplate('ArrayFieldTemplate', registry, uiOptions);\n        return _jsx(Template, { ...arrayProps });\n    }\n    /** Renders an array using the custom widget provided by the user in the `uiSchema`\n     */\n    renderCustomWidget() {\n        const { schema, idSchema, uiSchema, disabled = false, readonly = false, autofocus = false, required = false, hideError, placeholder, onBlur, onFocus, formData: items = [], registry, rawErrors, name, } = this.props;\n        const { widgets, formContext, globalUiOptions, schemaUtils } = registry;\n        const { widget, title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget(schema, widget, widgets);\n        const label = uiTitle ?? schema.title ?? name;\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        return (_jsx(Widget, { id: idSchema.$id, name: name, multiple: true, onChange: this.onSelectChange, onBlur: onBlur, onFocus: onFocus, options: options, schema: schema, uiSchema: uiSchema, registry: registry, value: items, disabled: disabled, readonly: readonly, hideError: hideError, required: required, label: label, hideLabel: !displayLabel, placeholder: placeholder, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors }));\n    }\n    /** Renders an array as a set of checkboxes\n     */\n    renderMultiSelect() {\n        const { schema, idSchema, uiSchema, formData: items = [], disabled = false, readonly = false, autofocus = false, required = false, placeholder, onBlur, onFocus, registry, rawErrors, name, } = this.props;\n        const { widgets, schemaUtils, formContext, globalUiOptions } = registry;\n        const itemsSchema = schemaUtils.retrieveSchema(schema.items, items);\n        const enumOptions = optionsList(itemsSchema, uiSchema);\n        const { widget = 'select', title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget(schema, widget, widgets);\n        const label = uiTitle ?? schema.title ?? name;\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        return (_jsx(Widget, { id: idSchema.$id, name: name, multiple: true, onChange: this.onSelectChange, onBlur: onBlur, onFocus: onFocus, options: { ...options, enumOptions }, schema: schema, uiSchema: uiSchema, registry: registry, value: items, disabled: disabled, readonly: readonly, required: required, label: label, hideLabel: !displayLabel, placeholder: placeholder, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors }));\n    }\n    /** Renders an array of files using the `FileWidget`\n     */\n    renderFiles() {\n        const { schema, uiSchema, idSchema, name, disabled = false, readonly = false, autofocus = false, required = false, onBlur, onFocus, registry, formData: items = [], rawErrors, } = this.props;\n        const { widgets, formContext, globalUiOptions, schemaUtils } = registry;\n        const { widget = 'files', title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget(schema, widget, widgets);\n        const label = uiTitle ?? schema.title ?? name;\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        return (_jsx(Widget, { options: options, id: idSchema.$id, name: name, multiple: true, onChange: this.onSelectChange, onBlur: onBlur, onFocus: onFocus, schema: schema, uiSchema: uiSchema, value: items, disabled: disabled, readonly: readonly, required: required, registry: registry, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors, label: label, hideLabel: !displayLabel }));\n    }\n    /** Renders an array that has a maximum limit of items\n     */\n    renderFixedArray() {\n        const { schema, uiSchema = {}, formData = [], errorSchema, idPrefix, idSeparator = '_', idSchema, name, title, disabled = false, readonly = false, autofocus = false, required = false, registry, onBlur, onFocus, rawErrors, } = this.props;\n        const { keyedFormData } = this.state;\n        let { formData: items = [] } = this.props;\n        const fieldTitle = schema.title || title || name;\n        const uiOptions = getUiOptions(uiSchema);\n        const { schemaUtils, formContext } = registry;\n        const _schemaItems = isObject(schema.items) ? schema.items : [];\n        const itemSchemas = _schemaItems.map((item, index) => schemaUtils.retrieveSchema(item, formData[index]));\n        const additionalSchema = isObject(schema.additionalItems)\n            ? schemaUtils.retrieveSchema(schema.additionalItems, formData)\n            : null;\n        if (!items || items.length < itemSchemas.length) {\n            // to make sure at least all fixed items are generated\n            items = items || [];\n            items = items.concat(new Array(itemSchemas.length - items.length));\n        }\n        // These are the props passed into the render function\n        const canAdd = this.canAddItem(items) && !!additionalSchema;\n        const arrayProps = {\n            canAdd,\n            className: 'field field-array field-array-fixed-items',\n            disabled,\n            idSchema,\n            formData,\n            items: keyedFormData.map((keyedItem, index) => {\n                const { key, item } = keyedItem;\n                // While we are actually dealing with a single item of type T, the types require a T[], so cast\n                const itemCast = item;\n                const additional = index >= itemSchemas.length;\n                const itemSchema = (additional && isObject(schema.additionalItems)\n                    ? schemaUtils.retrieveSchema(schema.additionalItems, itemCast)\n                    : itemSchemas[index]) || {};\n                const itemIdPrefix = idSchema.$id + idSeparator + index;\n                const itemIdSchema = schemaUtils.toIdSchema(itemSchema, itemIdPrefix, itemCast, idPrefix, idSeparator);\n                const itemUiSchema = additional\n                    ? uiSchema.additionalItems || {}\n                    : Array.isArray(uiSchema.items)\n                        ? uiSchema.items[index]\n                        : uiSchema.items || {};\n                const itemErrorSchema = errorSchema ? errorSchema[index] : undefined;\n                return this.renderArrayFieldItem({\n                    key,\n                    index,\n                    name: name && `${name}-${index}`,\n                    title: fieldTitle ? `${fieldTitle}-${index + 1}` : undefined,\n                    canAdd,\n                    canRemove: additional,\n                    canMoveUp: index >= itemSchemas.length + 1,\n                    canMoveDown: additional && index < items.length - 1,\n                    itemSchema,\n                    itemData: itemCast,\n                    itemUiSchema,\n                    itemIdSchema,\n                    itemErrorSchema,\n                    autofocus: autofocus && index === 0,\n                    onBlur,\n                    onFocus,\n                    rawErrors,\n                    totalItems: keyedFormData.length,\n                });\n            }),\n            onAddClick: this.onAddClick,\n            readonly,\n            required,\n            registry,\n            schema,\n            uiSchema,\n            title: fieldTitle,\n            formContext,\n            errorSchema,\n            rawErrors,\n        };\n        const Template = getTemplate('ArrayFieldTemplate', registry, uiOptions);\n        return _jsx(Template, { ...arrayProps });\n    }\n    /** Renders the individual array item using a `SchemaField` along with the additional properties required to be send\n     * back to the `ArrayFieldItemTemplate`.\n     *\n     * @param props - The props for the individual array item to be rendered\n     */\n    renderArrayFieldItem(props) {\n        const { key, index, name, canAdd, canRemove = true, canMoveUp, canMoveDown, itemSchema, itemData, itemUiSchema, itemIdSchema, itemErrorSchema, autofocus, onBlur, onFocus, rawErrors, totalItems, title, } = props;\n        const { disabled, hideError, idPrefix, idSeparator, readonly, uiSchema, registry, formContext } = this.props;\n        const { fields: { ArraySchemaField, SchemaField }, globalUiOptions, } = registry;\n        const ItemSchemaField = ArraySchemaField || SchemaField;\n        const { orderable = true, removable = true, copyable = false } = getUiOptions(uiSchema, globalUiOptions);\n        const has = {\n            moveUp: orderable && canMoveUp,\n            moveDown: orderable && canMoveDown,\n            copy: copyable && canAdd,\n            remove: removable && canRemove,\n            toolbar: false,\n        };\n        has.toolbar = Object.keys(has).some((key) => has[key]);\n        return {\n            children: (_jsx(ItemSchemaField, { name: name, title: title, index: index, schema: itemSchema, uiSchema: itemUiSchema, formData: itemData, formContext: formContext, errorSchema: itemErrorSchema, idPrefix: idPrefix, idSeparator: idSeparator, idSchema: itemIdSchema, required: this.isItemRequired(itemSchema), onChange: this.onChangeForIndex(index), onBlur: onBlur, onFocus: onFocus, registry: registry, disabled: disabled, readonly: readonly, hideError: hideError, autofocus: autofocus, rawErrors: rawErrors })),\n            className: 'array-item',\n            disabled,\n            canAdd,\n            hasCopy: has.copy,\n            hasToolbar: has.toolbar,\n            hasMoveUp: has.moveUp,\n            hasMoveDown: has.moveDown,\n            hasRemove: has.remove,\n            index,\n            totalItems,\n            key,\n            onAddIndexClick: this.onAddIndexClick,\n            onCopyIndexClick: this.onCopyIndexClick,\n            onDropIndexClick: this.onDropIndexClick,\n            onReorderClick: this.onReorderClick,\n            readonly,\n            registry,\n            schema: itemSchema,\n            uiSchema: itemUiSchema,\n        };\n    }\n}\n/** `ArrayField` is `React.ComponentType<FieldProps<T[], S, F>>` (necessarily) but the `registry` requires things to be a\n * `Field` which is defined as `React.ComponentType<FieldProps<T, S, F>>`, so cast it to make `registry` happy.\n */\nexport default ArrayField;\n", "import { urlAlphabet } from './url-alphabet/index.js'\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    byte &= 63\n    if (byte < 36) {\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getWidget, getUiOptions, optionsList, TranslatableString, } from '@rjsf/utils';\nimport isObject from 'lodash-es/isObject.js';\n/** The `BooleanField` component is used to render a field in the schema is boolean. It constructs `enumOptions` for the\n * two boolean values based on the various alternatives in the schema.\n *\n * @param props - The `FieldProps` for this template\n */\nfunction BooleanField(props) {\n    const { schema, name, uiSchema, idSchema, formData, registry, required, disabled, readonly, hideError, autofocus, title, onChange, onFocus, onBlur, rawErrors, } = props;\n    const { title: schemaTitle } = schema;\n    const { widgets, formContext, translateString, globalUiOptions } = registry;\n    const { widget = 'checkbox', title: uiTitle, \n    // Unlike the other fields, don't use `getDisplayLabel()` since it always returns false for the boolean type\n    label: displayLabel = true, ...options } = getUiOptions(uiSchema, globalUiOptions);\n    const Widget = getWidget(schema, widget, widgets);\n    const yes = translateString(TranslatableString.YesLabel);\n    const no = translateString(TranslatableString.NoLabel);\n    let enumOptions;\n    const label = uiTitle ?? schemaTitle ?? title ?? name;\n    if (Array.isArray(schema.oneOf)) {\n        enumOptions = optionsList({\n            oneOf: schema.oneOf\n                .map((option) => {\n                if (isObject(option)) {\n                    return {\n                        ...option,\n                        title: option.title || (option.const === true ? yes : no),\n                    };\n                }\n                return undefined;\n            })\n                .filter((o) => o), // cast away the error that typescript can't grok is fixed\n        }, uiSchema);\n    }\n    else {\n        // We deprecated enumNames in v5. It's intentionally omitted from RSJFSchema type, so we need to cast here.\n        const schemaWithEnumNames = schema;\n        const enums = schema.enum ?? [true, false];\n        if (!schemaWithEnumNames.enumNames && enums.length === 2 && enums.every((v) => typeof v === 'boolean')) {\n            enumOptions = [\n                {\n                    value: enums[0],\n                    label: enums[0] ? yes : no,\n                },\n                {\n                    value: enums[1],\n                    label: enums[1] ? yes : no,\n                },\n            ];\n        }\n        else {\n            enumOptions = optionsList({\n                enum: enums,\n                // NOTE: enumNames is deprecated, but still supported for now.\n                enumNames: schemaWithEnumNames.enumNames,\n            }, uiSchema);\n        }\n    }\n    return (_jsx(Widget, { options: { ...options, enumOptions }, schema: schema, uiSchema: uiSchema, id: idSchema.$id, name: name, onChange: onChange, onFocus: onFocus, onBlur: onBlur, label: label, hideLabel: !displayLabel, value: formData, required: required, disabled: disabled, readonly: readonly, hideError: hideError, registry: registry, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors }));\n}\nexport default BooleanField;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Component } from 'react';\nimport get from 'lodash-es/get.js';\nimport isEmpty from 'lodash-es/isEmpty.js';\nimport omit from 'lodash-es/omit.js';\nimport { ANY_OF_KEY, deepEquals, ERRORS_KEY, getDiscriminatorFieldFromSchema, getUiOptions, getWidget, mergeSchemas, ONE_OF_KEY, TranslatableString, } from '@rjsf/utils';\n/** The `AnyOfField` component is used to render a field in the schema that is an `anyOf`, `allOf` or `oneOf`. It tracks\n * the currently selected option and cleans up any irrelevant data in `formData`.\n *\n * @param props - The `FieldProps` for this template\n */\nclass AnyOfField extends Component {\n    /** Constructs an `AnyOfField` with the given `props` to initialize the initially selected option in state\n     *\n     * @param props - The `FieldProps` for this template\n     */\n    constructor(props) {\n        super(props);\n        const { formData, options, registry: { schemaUtils }, } = this.props;\n        // cache the retrieved options in state in case they have $refs to save doing it later\n        const retrievedOptions = options.map((opt) => schemaUtils.retrieveSchema(opt, formData));\n        this.state = {\n            retrievedOptions,\n            selectedOption: this.getMatchingOption(0, formData, retrievedOptions),\n        };\n    }\n    /** React lifecycle method that is called when the props and/or state for this component is updated. It recomputes the\n     * currently selected option based on the overall `formData`\n     *\n     * @param prevProps - The previous `FieldProps` for this template\n     * @param prevState - The previous `AnyOfFieldState` for this template\n     */\n    componentDidUpdate(prevProps, prevState) {\n        const { formData, options, idSchema } = this.props;\n        const { selectedOption } = this.state;\n        let newState = this.state;\n        if (!deepEquals(prevProps.options, options)) {\n            const { registry: { schemaUtils }, } = this.props;\n            // re-cache the retrieved options in state in case they have $refs to save doing it later\n            const retrievedOptions = options.map((opt) => schemaUtils.retrieveSchema(opt, formData));\n            newState = { selectedOption, retrievedOptions };\n        }\n        if (!deepEquals(formData, prevProps.formData) && idSchema.$id === prevProps.idSchema.$id) {\n            const { retrievedOptions } = newState;\n            const matchingOption = this.getMatchingOption(selectedOption, formData, retrievedOptions);\n            if (prevState && matchingOption !== selectedOption) {\n                newState = { selectedOption: matchingOption, retrievedOptions };\n            }\n        }\n        if (newState !== this.state) {\n            this.setState(newState);\n        }\n    }\n    /** Determines the best matching option for the given `formData` and `options`.\n     *\n     * @param formData - The new formData\n     * @param options - The list of options to choose from\n     * @return - The index of the `option` that best matches the `formData`\n     */\n    getMatchingOption(selectedOption, formData, options) {\n        const { schema, registry: { schemaUtils }, } = this.props;\n        const discriminator = getDiscriminatorFieldFromSchema(schema);\n        const option = schemaUtils.getClosestMatchingOption(formData, options, selectedOption, discriminator);\n        return option;\n    }\n    /** Callback handler to remember what the currently selected option is. In addition to that the `formData` is updated\n     * to remove properties that are not part of the newly selected option schema, and then the updated data is passed to\n     * the `onChange` handler.\n     *\n     * @param option - The new option value being selected\n     */\n    onOptionChange = (option) => {\n        const { selectedOption, retrievedOptions } = this.state;\n        const { formData, onChange, registry } = this.props;\n        const { schemaUtils } = registry;\n        const intOption = option !== undefined ? parseInt(option, 10) : -1;\n        if (intOption === selectedOption) {\n            return;\n        }\n        const newOption = intOption >= 0 ? retrievedOptions[intOption] : undefined;\n        const oldOption = selectedOption >= 0 ? retrievedOptions[selectedOption] : undefined;\n        let newFormData = schemaUtils.sanitizeDataForNewSchema(newOption, oldOption, formData);\n        if (newOption) {\n            // Call getDefaultFormState to make sure defaults are populated on change. Pass \"excludeObjectChildren\"\n            // so that only the root objects themselves are created without adding undefined children properties\n            newFormData = schemaUtils.getDefaultFormState(newOption, newFormData, 'excludeObjectChildren');\n        }\n        this.setState({ selectedOption: intOption }, () => {\n            onChange(newFormData, undefined, this.getFieldId());\n        });\n    };\n    getFieldId() {\n        const { idSchema, schema } = this.props;\n        return `${idSchema.$id}${schema.oneOf ? '__oneof_select' : '__anyof_select'}`;\n    }\n    /** Renders the `AnyOfField` selector along with a `SchemaField` for the value of the `formData`\n     */\n    render() {\n        const { name, disabled = false, errorSchema = {}, formContext, onBlur, onFocus, readonly, registry, schema, uiSchema, } = this.props;\n        const { widgets, fields, translateString, globalUiOptions, schemaUtils } = registry;\n        const { SchemaField: _SchemaField } = fields;\n        const { selectedOption, retrievedOptions } = this.state;\n        const { widget = 'select', placeholder, autofocus, autocomplete, title = schema.title, ...uiOptions } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget({ type: 'number' }, widget, widgets);\n        const rawErrors = get(errorSchema, ERRORS_KEY, []);\n        const fieldErrorSchema = omit(errorSchema, [ERRORS_KEY]);\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        const option = selectedOption >= 0 ? retrievedOptions[selectedOption] || null : null;\n        let optionSchema;\n        if (option) {\n            // merge top level required field\n            const { required } = schema;\n            // Merge in all the non-oneOf/anyOf properties and also skip the special ADDITIONAL_PROPERTY_FLAG property\n            optionSchema = required ? mergeSchemas({ required }, option) : option;\n        }\n        // First we will check to see if there is an anyOf/oneOf override for the UI schema\n        let optionsUiSchema = [];\n        if (ONE_OF_KEY in schema && uiSchema && ONE_OF_KEY in uiSchema) {\n            if (Array.isArray(uiSchema[ONE_OF_KEY])) {\n                optionsUiSchema = uiSchema[ONE_OF_KEY];\n            }\n            else {\n                console.warn(`uiSchema.oneOf is not an array for \"${title || name}\"`);\n            }\n        }\n        else if (ANY_OF_KEY in schema && uiSchema && ANY_OF_KEY in uiSchema) {\n            if (Array.isArray(uiSchema[ANY_OF_KEY])) {\n                optionsUiSchema = uiSchema[ANY_OF_KEY];\n            }\n            else {\n                console.warn(`uiSchema.anyOf is not an array for \"${title || name}\"`);\n            }\n        }\n        // Then we pick the one that matches the selected option index, if one exists otherwise default to the main uiSchema\n        let optionUiSchema = uiSchema;\n        if (selectedOption >= 0 && optionsUiSchema.length > selectedOption) {\n            optionUiSchema = optionsUiSchema[selectedOption];\n        }\n        const translateEnum = title\n            ? TranslatableString.TitleOptionPrefix\n            : TranslatableString.OptionPrefix;\n        const translateParams = title ? [title] : [];\n        const enumOptions = retrievedOptions.map((opt, index) => {\n            // Also see if there is an override title in the uiSchema for each option, otherwise use the title from the option\n            const { title: uiTitle = opt.title } = getUiOptions(optionsUiSchema[index]);\n            return {\n                label: uiTitle || translateString(translateEnum, translateParams.concat(String(index + 1))),\n                value: index,\n            };\n        });\n        return (_jsxs(\"div\", { className: 'panel panel-default panel-body', children: [_jsx(\"div\", { className: 'form-group', children: _jsx(Widget, { id: this.getFieldId(), name: `${name}${schema.oneOf ? '__oneof_select' : '__anyof_select'}`, schema: { type: 'number', default: 0 }, onChange: this.onOptionChange, onBlur: onBlur, onFocus: onFocus, disabled: disabled || isEmpty(enumOptions), multiple: false, rawErrors: rawErrors, errorSchema: fieldErrorSchema, value: selectedOption >= 0 ? selectedOption : undefined, options: { enumOptions, ...uiOptions }, registry: registry, formContext: formContext, placeholder: placeholder, autocomplete: autocomplete, autofocus: autofocus, label: title ?? name, hideLabel: !displayLabel, readonly: readonly }) }), optionSchema && _jsx(_SchemaField, { ...this.props, schema: optionSchema, uiSchema: optionUiSchema })] }));\n    }\n}\nexport default AnyOfField;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useState, useCallback } from 'react';\nimport { asNumber } from '@rjsf/utils';\n// Matches a string that ends in a . character, optionally followed by a sequence of\n// digits followed by any number of 0 characters up until the end of the line.\n// Ensuring that there is at least one prefixed character is important so that\n// you don't incorrectly match against \"0\".\nconst trailingCharMatcherWithPrefix = /\\.([0-9]*0)*$/;\n// This is used for trimming the trailing 0 and . characters without affecting\n// the rest of the string. Its possible to use one RegEx with groups for this\n// functionality, but it is fairly complex compared to simply defining two\n// different matchers.\nconst trailingCharMatcher = /[0.]0*$/;\n/**\n * The NumberField class has some special handling for dealing with trailing\n * decimal points and/or zeroes. This logic is designed to allow trailing values\n * to be visible in the input element, but not be represented in the\n * corresponding form data.\n *\n * The algorithm is as follows:\n *\n * 1. When the input value changes the value is cached in the component state\n *\n * 2. The value is then normalized, removing trailing decimal points and zeros,\n *    then passed to the \"onChange\" callback\n *\n * 3. When the component is rendered, the formData value is checked against the\n *    value cached in the state. If it matches the cached value, the cached\n *    value is passed to the input instead of the formData value\n */\nfunction NumberField(props) {\n    const { registry, onChange, formData, value: initialValue } = props;\n    const [lastValue, setLastValue] = useState(initialValue);\n    const { StringField } = registry.fields;\n    let value = formData;\n    /** Handle the change from the `StringField` to properly convert to a number\n     *\n     * @param value - The current value for the change occurring\n     */\n    const handleChange = useCallback((value, errorSchema, id) => {\n        // Cache the original value in component state\n        setLastValue(value);\n        // Normalize decimals that don't start with a zero character in advance so\n        // that the rest of the normalization logic is simpler\n        if (`${value}`.charAt(0) === '.') {\n            value = `0${value}`;\n        }\n        // Check that the value is a string (this can happen if the widget used is a\n        // <select>, due to an enum declaration etc) then, if the value ends in a\n        // trailing decimal point or multiple zeroes, strip the trailing values\n        const processed = typeof value === 'string' && value.match(trailingCharMatcherWithPrefix)\n            ? asNumber(value.replace(trailingCharMatcher, ''))\n            : asNumber(value);\n        onChange(processed, errorSchema, id);\n    }, [onChange]);\n    if (typeof lastValue === 'string' && typeof value === 'number') {\n        // Construct a regular expression that checks for a string that consists\n        // of the formData value suffixed with zero or one '.' characters and zero\n        // or more '0' characters\n        const re = new RegExp(`^(${String(value).replace('.', '\\\\.')})?\\\\.?0*$`);\n        // If the cached \"lastValue\" is a match, use that instead of the formData\n        // value to prevent the input value from changing in the UI\n        if (lastValue.match(re)) {\n            value = lastValue;\n        }\n    }\n    return _jsx(StringField, { ...props, formData: value, onChange: handleChange });\n}\nexport default NumberField;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Component } from 'react';\nimport { getTemplate, getUiOptions, orderProperties, TranslatableString, ADDITIONAL_PROPERTY_FLAG, PROPERTIES_KEY, REF_KEY, ANY_OF_KEY, ONE_OF_KEY, } from '@rjsf/utils';\nimport Markdown from 'markdown-to-jsx';\nimport get from 'lodash-es/get.js';\nimport has from 'lodash-es/has.js';\nimport isObject from 'lodash-es/isObject.js';\nimport set from 'lodash-es/set.js';\nimport unset from 'lodash-es/unset.js';\n/** The `ObjectField` component is used to render a field in the schema that is of type `object`. It tracks whether an\n * additional property key was modified and what it was modified to\n *\n * @param props - The `FieldProps` for this template\n */\nclass ObjectField extends Component {\n    /** Set up the initial state */\n    state = {\n        wasPropertyKeyModified: false,\n        additionalProperties: {},\n    };\n    /** Returns a flag indicating whether the `name` field is required in the object schema\n     *\n     * @param name - The name of the field to check for required-ness\n     * @returns - True if the field `name` is required, false otherwise\n     */\n    isRequired(name) {\n        const { schema } = this.props;\n        return Array.isArray(schema.required) && schema.required.indexOf(name) !== -1;\n    }\n    /** Returns the `onPropertyChange` handler for the `name` field. Handles the special case where a user is attempting\n     * to clear the data for a field added as an additional property. Calls the `onChange()` handler with the updated\n     * formData.\n     *\n     * @param name - The name of the property\n     * @param addedByAdditionalProperties - Flag indicating whether this property is an additional property\n     * @returns - The onPropertyChange callback for the `name` property\n     */\n    onPropertyChange = (name, addedByAdditionalProperties = false) => {\n        return (value, newErrorSchema, id) => {\n            const { formData, onChange, errorSchema } = this.props;\n            if (value === undefined && addedByAdditionalProperties) {\n                // Don't set value = undefined for fields added by\n                // additionalProperties. Doing so removes them from the\n                // formData, which causes them to completely disappear\n                // (including the input field for the property name). Unlike\n                // fields which are \"mandated\" by the schema, these fields can\n                // be set to undefined by clicking a \"delete field\" button, so\n                // set empty values to the empty string.\n                value = '';\n            }\n            const newFormData = { ...formData, [name]: value };\n            onChange(newFormData, errorSchema &&\n                errorSchema && {\n                ...errorSchema,\n                [name]: newErrorSchema,\n            }, id);\n        };\n    };\n    /** Returns a callback to handle the onDropPropertyClick event for the given `key` which removes the old `key` data\n     * and calls the `onChange` callback with it\n     *\n     * @param key - The key for which the drop callback is desired\n     * @returns - The drop property click callback\n     */\n    onDropPropertyClick = (key) => {\n        return (event) => {\n            event.preventDefault();\n            const { onChange, formData } = this.props;\n            const copiedFormData = { ...formData };\n            unset(copiedFormData, key);\n            onChange(copiedFormData);\n        };\n    };\n    /** Computes the next available key name from the `preferredKey`, indexing through the already existing keys until one\n     * that is already not assigned is found.\n     *\n     * @param preferredKey - The preferred name of a new key\n     * @param [formData] - The form data in which to check if the desired key already exists\n     * @returns - The name of the next available key from `preferredKey`\n     */\n    getAvailableKey = (preferredKey, formData) => {\n        const { uiSchema, registry } = this.props;\n        const { duplicateKeySuffixSeparator = '-' } = getUiOptions(uiSchema, registry.globalUiOptions);\n        let index = 0;\n        let newKey = preferredKey;\n        while (has(formData, newKey)) {\n            newKey = `${preferredKey}${duplicateKeySuffixSeparator}${++index}`;\n        }\n        return newKey;\n    };\n    /** Returns a callback function that deals with the rename of a key for an additional property for a schema. That\n     * callback will attempt to rename the key and move the existing data to that key, calling `onChange` when it does.\n     *\n     * @param oldValue - The old value of a field\n     * @returns - The key change callback function\n     */\n    onKeyChange = (oldValue) => {\n        return (value, newErrorSchema) => {\n            if (oldValue === value) {\n                return;\n            }\n            const { formData, onChange, errorSchema } = this.props;\n            value = this.getAvailableKey(value, formData);\n            const newFormData = {\n                ...formData,\n            };\n            const newKeys = { [oldValue]: value };\n            const keyValues = Object.keys(newFormData).map((key) => {\n                const newKey = newKeys[key] || key;\n                return { [newKey]: newFormData[key] };\n            });\n            const renamedObj = Object.assign({}, ...keyValues);\n            this.setState({ wasPropertyKeyModified: true });\n            onChange(renamedObj, errorSchema &&\n                errorSchema && {\n                ...errorSchema,\n                [value]: newErrorSchema,\n            });\n        };\n    };\n    /** Returns a default value to be used for a new additional schema property of the given `type`\n     *\n     * @param type - The type of the new additional schema property\n     */\n    getDefaultValue(type) {\n        const { registry: { translateString }, } = this.props;\n        switch (type) {\n            case 'array':\n                return [];\n            case 'boolean':\n                return false;\n            case 'null':\n                return null;\n            case 'number':\n                return 0;\n            case 'object':\n                return {};\n            case 'string':\n            default:\n                // We don't have a datatype for some reason (perhaps additionalProperties was true)\n                return translateString(TranslatableString.NewStringDefault);\n        }\n    }\n    /** Handles the adding of a new additional property on the given `schema`. Calls the `onChange` callback once the new\n     * default data for that field has been added to the formData.\n     *\n     * @param schema - The schema element to which the new property is being added\n     */\n    handleAddClick = (schema) => () => {\n        if (!schema.additionalProperties) {\n            return;\n        }\n        const { formData, onChange, registry } = this.props;\n        const newFormData = { ...formData };\n        let type = undefined;\n        let constValue = undefined;\n        let defaultValue = undefined;\n        if (isObject(schema.additionalProperties)) {\n            type = schema.additionalProperties.type;\n            constValue = schema.additionalProperties.const;\n            defaultValue = schema.additionalProperties.default;\n            let apSchema = schema.additionalProperties;\n            if (REF_KEY in apSchema) {\n                const { schemaUtils } = registry;\n                apSchema = schemaUtils.retrieveSchema({ $ref: apSchema[REF_KEY] }, formData);\n                type = apSchema.type;\n                constValue = apSchema.const;\n                defaultValue = apSchema.default;\n            }\n            if (!type && (ANY_OF_KEY in apSchema || ONE_OF_KEY in apSchema)) {\n                type = 'object';\n            }\n        }\n        const newKey = this.getAvailableKey('newKey', newFormData);\n        const newValue = constValue ?? defaultValue ?? this.getDefaultValue(type);\n        // Cast this to make the `set` work properly\n        set(newFormData, newKey, newValue);\n        onChange(newFormData);\n    };\n    /** Renders the `ObjectField` from the given props\n     */\n    render() {\n        const { schema: rawSchema, uiSchema = {}, formData, errorSchema, idSchema, name, required = false, disabled, readonly, hideError, idPrefix, idSeparator, onBlur, onFocus, registry, title, } = this.props;\n        const { fields, formContext, schemaUtils, translateString, globalUiOptions } = registry;\n        const { SchemaField } = fields;\n        const schema = schemaUtils.retrieveSchema(rawSchema, formData);\n        const uiOptions = getUiOptions(uiSchema, globalUiOptions);\n        const { properties: schemaProperties = {} } = schema;\n        const templateTitle = uiOptions.title ?? schema.title ?? title ?? name;\n        const description = uiOptions.description ?? schema.description;\n        let orderedProperties;\n        try {\n            const properties = Object.keys(schemaProperties);\n            orderedProperties = orderProperties(properties, uiOptions.order);\n        }\n        catch (err) {\n            return (_jsxs(\"div\", { children: [_jsx(\"p\", { className: 'config-error', style: { color: 'red' }, children: _jsx(Markdown, { options: { disableParsingRawHTML: true }, children: translateString(TranslatableString.InvalidObjectField, [name || 'root', err.message]) }) }), _jsx(\"pre\", { children: JSON.stringify(schema) })] }));\n        }\n        const Template = getTemplate('ObjectFieldTemplate', registry, uiOptions);\n        const templateProps = {\n            // getDisplayLabel() always returns false for object types, so just check the `uiOptions.label`\n            title: uiOptions.label === false ? '' : templateTitle,\n            description: uiOptions.label === false ? undefined : description,\n            properties: orderedProperties.map((name) => {\n                const addedByAdditionalProperties = has(schema, [PROPERTIES_KEY, name, ADDITIONAL_PROPERTY_FLAG]);\n                const fieldUiSchema = addedByAdditionalProperties ? uiSchema.additionalProperties : uiSchema[name];\n                const hidden = getUiOptions(fieldUiSchema).widget === 'hidden';\n                const fieldIdSchema = get(idSchema, [name], {});\n                return {\n                    content: (_jsx(SchemaField, { name: name, required: this.isRequired(name), schema: get(schema, [PROPERTIES_KEY, name], {}), uiSchema: fieldUiSchema, errorSchema: get(errorSchema, name), idSchema: fieldIdSchema, idPrefix: idPrefix, idSeparator: idSeparator, formData: get(formData, name), formContext: formContext, wasPropertyKeyModified: this.state.wasPropertyKeyModified, onKeyChange: this.onKeyChange(name), onChange: this.onPropertyChange(name, addedByAdditionalProperties), onBlur: onBlur, onFocus: onFocus, registry: registry, disabled: disabled, readonly: readonly, hideError: hideError, onDropPropertyClick: this.onDropPropertyClick }, name)),\n                    name,\n                    readonly,\n                    disabled,\n                    required,\n                    hidden,\n                };\n            }),\n            readonly,\n            disabled,\n            required,\n            idSchema,\n            uiSchema,\n            errorSchema,\n            schema,\n            formData,\n            formContext,\n            registry,\n        };\n        return _jsx(Template, { ...templateProps, onAddClick: this.handleAddClick });\n    }\n}\nexport default ObjectField;\n", "/* @jsx h */\n/**\n * markdown-to-jsx is a fork of\n * [simple-markdown v0.2.2](https://github.com/Khan/simple-markdown)\n * from Khan Academy. Thank you <PERSON> devs for making such an awesome\n * and extensible parsing infra... without it, half of the\n * optimizations here wouldn't be feasible. 🙏🏼\n */\nimport * as React from 'react'\n\n/**\n * Analogous to `node.type`. Please note that the values here may change at any time,\n * so do not hard code against the value directly.\n */\nexport const RuleType = {\n  blockQuote: '0',\n  breakLine: '1',\n  breakThematic: '2',\n  codeBlock: '3',\n  codeFenced: '4',\n  codeInline: '5',\n  footnote: '6',\n  footnoteReference: '7',\n  gfmTask: '8',\n  heading: '9',\n  headingSetext: '10',\n  /** only available if not `disableHTMLParsing` */\n  htmlBlock: '11',\n  htmlComment: '12',\n  /** only available if not `disableHTMLParsing` */\n  htmlSelfClosing: '13',\n  image: '14',\n  link: '15',\n  /** emits a `link` 'node', does not render directly */\n  linkAngleBraceStyleDetector: '16',\n  /** emits a `link` 'node', does not render directly */\n  linkBareUrlDetector: '17',\n  /** emits a `link` 'node', does not render directly */\n  linkMailtoDetector: '18',\n  newlineCoalescer: '19',\n  orderedList: '20',\n  paragraph: '21',\n  ref: '22',\n  refImage: '23',\n  refLink: '24',\n  table: '25',\n  tableSeparator: '26',\n  text: '27',\n  textBolded: '28',\n  textEmphasized: '29',\n  textEscaped: '30',\n  textMarked: '31',\n  textStrikethroughed: '32',\n  unorderedList: '33',\n} as const\n\nif (process.env.NODE_ENV !== 'production') {\n  Object.keys(RuleType).forEach(key => (RuleType[key] = key))\n}\n\nexport type RuleType = (typeof RuleType)[keyof typeof RuleType]\n\nconst enum Priority {\n  /**\n   * anything that must scan the tree before everything else\n   */\n  MAX,\n  /**\n   * scans for block-level constructs\n   */\n  HIGH,\n  /**\n   * inline w/ more priority than other inline\n   */\n  MED,\n  /**\n   * inline elements\n   */\n  LOW,\n  /**\n   * bare text and stuff that is considered leftovers\n   */\n  MIN,\n}\n\n/** TODO: Drop for React 16? */\nconst ATTRIBUTE_TO_JSX_PROP_MAP = [\n  'allowFullScreen',\n  'allowTransparency',\n  'autoComplete',\n  'autoFocus',\n  'autoPlay',\n  'cellPadding',\n  'cellSpacing',\n  'charSet',\n  'classId',\n  'colSpan',\n  'contentEditable',\n  'contextMenu',\n  'crossOrigin',\n  'encType',\n  'formAction',\n  'formEncType',\n  'formMethod',\n  'formNoValidate',\n  'formTarget',\n  'frameBorder',\n  'hrefLang',\n  'inputMode',\n  'keyParams',\n  'keyType',\n  'marginHeight',\n  'marginWidth',\n  'maxLength',\n  'mediaGroup',\n  'minLength',\n  'noValidate',\n  'radioGroup',\n  'readOnly',\n  'rowSpan',\n  'spellCheck',\n  'srcDoc',\n  'srcLang',\n  'srcSet',\n  'tabIndex',\n  'useMap',\n].reduce(\n  (obj, x) => {\n    obj[x.toLowerCase()] = x\n    return obj\n  },\n  { class: 'className', for: 'htmlFor' }\n)\n\nconst namedCodesToUnicode = {\n  amp: '\\u0026',\n  apos: '\\u0027',\n  gt: '\\u003e',\n  lt: '\\u003c',\n  nbsp: '\\u00a0',\n  quot: '\\u201c',\n} as const\n\nconst DO_NOT_PROCESS_HTML_ELEMENTS = ['style', 'script']\n\n/**\n * the attribute extractor regex looks for a valid attribute name,\n * followed by an equal sign (whitespace around the equal sign is allowed), followed\n * by one of the following:\n *\n * 1. a single quote-bounded string, e.g. 'foo'\n * 2. a double quote-bounded string, e.g. \"bar\"\n * 3. an interpolation, e.g. {something}\n *\n * JSX can be be interpolated into itself and is passed through the compiler using\n * the same options and setup as the current run.\n *\n * <Something children={<SomeOtherThing />} />\n *                      ==================\n *                              ↳ children: [<SomeOtherThing />]\n *\n * Otherwise, interpolations are handled as strings or simple booleans\n * unless HTML syntax is detected.\n *\n * <Something color={green} disabled={true} />\n *                   =====            ====\n *                     ↓                ↳ disabled: true\n *                     ↳ color: \"green\"\n *\n * Numbers are not parsed at this time due to complexities around int, float,\n * and the upcoming bigint functionality that would make handling it unwieldy.\n * Parse the string in your component as desired.\n *\n * <Something someBigNumber={123456789123456789} />\n *                           ==================\n *                                   ↳ someBigNumber: \"123456789123456789\"\n */\nconst ATTR_EXTRACTOR_R =\n  /([-A-Z0-9_:]+)(?:\\s*=\\s*(?:(?:\"((?:\\\\.|[^\"])*)\")|(?:'((?:\\\\.|[^'])*)')|(?:\\{((?:\\\\.|{[^}]*?}|[^}])*)\\})))?/gi\n\n/** TODO: Write explainers for each of these */\n\nconst AUTOLINK_MAILTO_CHECK_R = /mailto:/i\nconst BLOCK_END_R = /\\n{2,}$/\nconst BLOCKQUOTE_R = /^(\\s*>[\\s\\S]*?)(?=\\n\\n|$)/\nconst BLOCKQUOTE_TRIM_LEFT_MULTILINE_R = /^ *> ?/gm\nconst BLOCKQUOTE_ALERT_R = /^(?:\\[!([^\\]]*)\\]\\n)?([\\s\\S]*)/\nconst BREAK_LINE_R = /^ {2,}\\n/\nconst BREAK_THEMATIC_R = /^(?:( *[-*_])){3,} *(?:\\n *)+\\n/\nconst CODE_BLOCK_FENCED_R =\n  /^(?: {1,3})?(`{3,}|~{3,}) *(\\S+)? *([^\\n]*?)?\\n([\\s\\S]*?)(?:\\1\\n?|$)/\nconst CODE_BLOCK_R = /^(?: {4}[^\\n]+\\n*)+(?:\\n *)+\\n?/\nconst CODE_INLINE_R = /^(`+)((?:\\\\`|[^`])+)\\1/\nconst CONSECUTIVE_NEWLINE_R = /^(?:\\n *)*\\n/\nconst CR_NEWLINE_R = /\\r\\n?/g\n\n/**\n * Matches footnotes on the format:\n *\n * [^key]: value\n *\n * Matches multiline footnotes\n *\n * [^key]: row\n * row\n * row\n *\n * And empty lines in indented multiline footnotes\n *\n * [^key]: indented with\n *     row\n *\n *     row\n *\n * Explanation:\n *\n * 1. Look for the starting tag, eg: [^key]\n *    ^\\[\\^([^\\]]+)]\n *\n * 2. The first line starts with a colon, and continues for the rest of the line\n *   :(.*)\n *\n * 3. Parse as many additional lines as possible. Matches new non-empty lines that doesn't begin with a new footnote definition.\n *    (\\n(?!\\[\\^).+)\n *\n * 4. ...or allows for repeated newlines if the next line begins with at least four whitespaces.\n *    (\\n+ {4,}.*)\n */\nconst FOOTNOTE_R = /^\\[\\^([^\\]]+)](:(.*)((\\n+ {4,}.*)|(\\n(?!\\[\\^).+))*)/\n\nconst FOOTNOTE_REFERENCE_R = /^\\[\\^([^\\]]+)]/\nconst FORMFEED_R = /\\f/g\nconst FRONT_MATTER_R = /^---[ \\t]*\\n(.|\\n)*\\n---[ \\t]*\\n/\nconst GFM_TASK_R = /^\\s*?\\[(x|\\s)\\]/\nconst HEADING_R = /^ *(#{1,6}) *([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_ATX_COMPLIANT_R =\n  /^ *(#{1,6}) +([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_SETEXT_R = /^([^\\n]+)\\n *(=|-){3,} *(?:\\n *)+\\n/\n\n/**\n * Explanation:\n *\n * 1. Look for a starting tag, preceded by any amount of spaces\n *    ^ *<\n *\n * 2. Capture the tag name (capture 1)\n *    ([^ >/]+)\n *\n * 3. Ignore a space after the starting tag and capture the attribute portion of the tag (capture 2)\n *     ?([^>]*)>\n *\n * 4. Ensure a matching closing tag is present in the rest of the input string\n *    (?=[\\s\\S]*<\\/\\1>)\n *\n * 5. Capture everything until the matching closing tag -- this might include additional pairs\n *    of the same tag type found in step 2 (capture 3)\n *    ((?:[\\s\\S]*?(?:<\\1[^>]*>[\\s\\S]*?<\\/\\1>)*[\\s\\S]*?)*?)<\\/\\1>\n *\n * 6. Capture excess newlines afterward\n *    \\n*\n */\nconst HTML_BLOCK_ELEMENT_R =\n  /^ *(?!<[a-z][^ >/]* ?\\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\\n?(\\s*(?:<\\1[^>]*?>[\\s\\S]*?<\\/\\1>|(?!<\\1\\b)[\\s\\S])*?)<\\/\\1>(?!<\\/\\1>)\\n*/i\n\nconst HTML_CHAR_CODE_R = /&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi\n\nconst HTML_COMMENT_R = /^<!--[\\s\\S]*?(?:-->)/\n\n/**\n * borrowed from React 15(https://github.com/facebook/react/blob/894d20744cba99383ffd847dbd5b6e0800355a5c/src/renderers/dom/shared/HTMLDOMPropertyConfig.js)\n */\nconst HTML_CUSTOM_ATTR_R = /^(data|aria|x)-[a-z_][a-z\\d_.-]*$/\n\nconst HTML_SELF_CLOSING_ELEMENT_R =\n  /^ *<([a-z][a-z0-9:]*)(?:\\s+((?:<.*?>|[^>])*))?\\/?>(?!<\\/\\1>)(\\s*\\n)?/i\nconst INTERPOLATION_R = /^\\{.*\\}$/\nconst LINK_AUTOLINK_BARE_URL_R = /^(https?:\\/\\/[^\\s<]+[^<.,:;\"')\\]\\s])/\nconst LINK_AUTOLINK_MAILTO_R = /^<([^ >]+@[^ >]+)>/\nconst LINK_AUTOLINK_R = /^<([^ >]+:\\/[^ >]+)>/\nconst CAPTURE_LETTER_AFTER_HYPHEN = /-([a-z])?/gi\nconst NP_TABLE_R = /^(\\|.*)\\n(?: *(\\|? *[-:]+ *\\|[-| :]*)\\n((?:.*\\|.*\\n)*))?\\n?/\nconst PARAGRAPH_R = /^[^\\n]+(?:  \\n|\\n{2,})/\nconst REFERENCE_IMAGE_OR_LINK = /^\\[([^\\]]*)\\]:\\s+<?([^\\s>]+)>?\\s*(\"([^\"]*)\")?/\nconst REFERENCE_IMAGE_R = /^!\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst REFERENCE_LINK_R = /^\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst SHOULD_RENDER_AS_BLOCK_R = /(\\n|^[-*]\\s|^#|^ {2,}|^-{2,}|^>\\s)/\nconst TAB_R = /\\t/g\nconst TABLE_TRIM_PIPES = /(^ *\\||\\| *$)/g\nconst TABLE_CENTER_ALIGN = /^ *:-+: *$/\nconst TABLE_LEFT_ALIGN = /^ *:-+ *$/\nconst TABLE_RIGHT_ALIGN = /^ *-+: *$/\n\n/**\n * For inline formatting, this partial attempts to ignore characters that\n * may appear in nested formatting that could prematurely trigger detection\n * and therefore miss content that should have been included.\n */\nconst INLINE_SKIP_R =\n  '((?:\\\\[.*?\\\\][([].*?[)\\\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\\\\\\\1|[\\\\s\\\\S])+?)'\n\n/**\n * Detect a sequence like **foo** or __foo__. Note that bold has a higher priority\n * than emphasized to support nesting of both since they share a delimiter.\n */\nconst TEXT_BOLD_R = new RegExp(`^([*_])\\\\1${INLINE_SKIP_R}\\\\1\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like *foo* or _foo_.\n */\nconst TEXT_EMPHASIZED_R = new RegExp(`^([*_])${INLINE_SKIP_R}\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like ==foo==.\n */\nconst TEXT_MARKED_R = new RegExp(`^(==)${INLINE_SKIP_R}\\\\1`)\n\n/**\n * Detect a sequence like ~~foo~~.\n */\nconst TEXT_STRIKETHROUGHED_R = new RegExp(`^(~~)${INLINE_SKIP_R}\\\\1`)\n\nconst TEXT_ESCAPED_R = /^\\\\([^0-9A-Za-z\\s])/\nconst TEXT_UNESCAPE_R = /\\\\([^0-9A-Za-z\\s])/g\n\n/**\n * Always take the first character, then eagerly take text until a double space\n * (potential line break) or some markdown-like punctuation is reached.\n */\nconst TEXT_PLAIN_R = /^([\\s\\S](?:(?!  |[0-9]\\.)[^*_~\\-\\n<`\\\\\\[!])*)/\n\nconst TRIM_STARTING_NEWLINES = /^\\n+/\n\nconst HTML_LEFT_TRIM_AMOUNT_R = /^([ \\t]*)/\n\nconst UNESCAPE_URL_R = /\\\\([^\\\\])/g\n\ntype LIST_TYPE = 1 | 2\nconst ORDERED: LIST_TYPE = 1\nconst UNORDERED: LIST_TYPE = 2\n\nconst LIST_ITEM_END_R = / *\\n+$/\nconst LIST_LOOKBEHIND_R = /(?:^|\\n)( *)$/\n\n// recognize a `*` `-`, `+`, `1.`, `2.`... list bullet\nconst ORDERED_LIST_BULLET = '(?:\\\\d+\\\\.)'\nconst UNORDERED_LIST_BULLET = '(?:[*+-])'\n\nfunction generateListItemPrefix(type: LIST_TYPE) {\n  return (\n    '( *)(' +\n    (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n    ') +'\n  )\n}\n\n// recognize the start of a list item:\n// leading space plus a bullet plus a space (`   * `)\nconst ORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(UNORDERED)\n\nfunction generateListItemPrefixRegex(type: LIST_TYPE) {\n  return new RegExp(\n    '^' +\n      (type === ORDERED ? ORDERED_LIST_ITEM_PREFIX : UNORDERED_LIST_ITEM_PREFIX)\n  )\n}\n\nconst ORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(UNORDERED)\n\nfunction generateListItemRegex(type: LIST_TYPE) {\n  // recognize an individual list item:\n  //  * hi\n  //    this is part of the same item\n  //\n  //    as is this, which is a new paragraph in the same item\n  //\n  //  * but this is not part of the same item\n  return new RegExp(\n    '^' +\n      (type === ORDERED\n        ? ORDERED_LIST_ITEM_PREFIX\n        : UNORDERED_LIST_ITEM_PREFIX) +\n      '[^\\\\n]*(?:\\\\n' +\n      '(?!\\\\1' +\n      (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n      ' )[^\\\\n]*)*(\\\\n|$)',\n    'gm'\n  )\n}\n\nconst ORDERED_LIST_ITEM_R = generateListItemRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_R = generateListItemRegex(UNORDERED)\n\n// check whether a list item has paragraphs: if it does,\n// we leave the newlines at the end\nfunction generateListRegex(type: LIST_TYPE) {\n  const bullet = type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET\n\n  return new RegExp(\n    '^( *)(' +\n      bullet +\n      ') ' +\n      '[\\\\s\\\\S]+?(?:\\\\n{2,}(?! )' +\n      '(?!\\\\1' +\n      bullet +\n      ' (?!' +\n      bullet +\n      ' ))\\\\n*' +\n      // the \\\\s*$ here is so that we can parse the inside of nested\n      // lists, where our content might end before we receive two `\\n`s\n      '|\\\\s*\\\\n*$)'\n  )\n}\n\nconst ORDERED_LIST_R = generateListRegex(ORDERED)\nconst UNORDERED_LIST_R = generateListRegex(UNORDERED)\n\nfunction generateListRule(\n  h: any,\n  type: LIST_TYPE\n): MarkdownToJSX.Rule<\n  MarkdownToJSX.OrderedListNode | MarkdownToJSX.UnorderedListNode\n> {\n  const ordered = type === ORDERED\n  const LIST_R = ordered ? ORDERED_LIST_R : UNORDERED_LIST_R\n  const LIST_ITEM_R = ordered ? ORDERED_LIST_ITEM_R : UNORDERED_LIST_ITEM_R\n  const LIST_ITEM_PREFIX_R = ordered\n    ? ORDERED_LIST_ITEM_PREFIX_R\n    : UNORDERED_LIST_ITEM_PREFIX_R\n\n  return {\n    match: allowInline(function (source, state) {\n      // We only want to break into a list if we are at the start of a\n      // line. This is to avoid parsing \"hi * there\" with \"* there\"\n      // becoming a part of a list.\n      // You might wonder, \"but that's inline, so of course it wouldn't\n      // start a list?\". You would be correct! Except that some of our\n      // lists can be inline, because they might be inside another list,\n      // in which case we can parse with inline scope, but need to allow\n      // nested lists inside this inline scope.\n      const isStartOfLine = LIST_LOOKBEHIND_R.exec(state.prevCapture)\n      const isListAllowed = state.list || (!state.inline && !state.simple)\n\n      if (isStartOfLine && isListAllowed) {\n        source = isStartOfLine[1] + source\n\n        return LIST_R.exec(source)\n      } else {\n        return null\n      }\n    }),\n    order: Priority.HIGH,\n    parse(capture, parse, state) {\n      const bullet = capture[2]\n      const start = ordered ? +bullet : undefined\n      const items = capture[0]\n        // recognize the end of a paragraph block inside a list item:\n        // two or more newlines at end end of the item\n        .replace(BLOCK_END_R, '\\n')\n        .match(LIST_ITEM_R)\n\n      let lastItemWasAParagraph = false\n\n      const itemContent = items.map(function (item, i) {\n        // We need to see how far indented the item is:\n        const space = LIST_ITEM_PREFIX_R.exec(item)[0].length\n\n        // And then we construct a regex to \"unindent\" the subsequent\n        // lines of the items by that amount:\n        const spaceRegex = new RegExp('^ {1,' + space + '}', 'gm')\n\n        // Before processing the item, we need a couple things\n        const content = item\n          // remove indents on trailing lines:\n          .replace(spaceRegex, '')\n          // remove the bullet:\n          .replace(LIST_ITEM_PREFIX_R, '')\n\n        // Handling \"loose\" lists, like:\n        //\n        //  * this is wrapped in a paragraph\n        //\n        //  * as is this\n        //\n        //  * as is this\n        const isLastItem = i === items.length - 1\n        const containsBlocks = content.indexOf('\\n\\n') !== -1\n\n        // Any element in a list is a block if it contains multiple\n        // newlines. The last element in the list can also be a block\n        // if the previous item in the list was a block (this is\n        // because non-last items in the list can end with \\n\\n, but\n        // the last item can't, so we just \"inherit\" this property\n        // from our previous element).\n        const thisItemIsAParagraph =\n          containsBlocks || (isLastItem && lastItemWasAParagraph)\n        lastItemWasAParagraph = thisItemIsAParagraph\n\n        // backup our state for delta afterwards. We're going to\n        // want to set state.list to true, and state.inline depending\n        // on our list's looseness.\n        const oldStateInline = state.inline\n        const oldStateList = state.list\n        state.list = true\n\n        // Parse inline if we're in a tight list, or block if we're in\n        // a loose list.\n        let adjustedContent\n        if (thisItemIsAParagraph) {\n          state.inline = false\n          adjustedContent = trimEnd(content) + '\\n\\n'\n        } else {\n          state.inline = true\n          adjustedContent = trimEnd(content)\n        }\n\n        const result = parse(adjustedContent, state)\n\n        // Restore our state before returning\n        state.inline = oldStateInline\n        state.list = oldStateList\n\n        return result\n      })\n\n      return {\n        items: itemContent,\n        ordered: ordered,\n        start: start,\n      }\n    },\n    render(node, output, state) {\n      const Tag = node.ordered ? 'ol' : 'ul'\n\n      return (\n        <Tag\n          key={state.key}\n          start={node.type === RuleType.orderedList ? node.start : undefined}\n        >\n          {node.items.map(function generateListItem(item, i) {\n            return <li key={i}>{output(item, state)}</li>\n          })}\n        </Tag>\n      )\n    },\n  }\n}\n\nconst LINK_INSIDE = '(?:\\\\[[^\\\\]]*\\\\]|[^\\\\[\\\\]]|\\\\](?=[^\\\\[]*\\\\]))*'\nconst LINK_HREF_AND_TITLE =\n  '\\\\s*<?((?:\\\\([^)]*\\\\)|[^\\\\s\\\\\\\\]|\\\\\\\\.)*?)>?(?:\\\\s+[\\'\"]([\\\\s\\\\S]*?)[\\'\"])?\\\\s*'\nconst LINK_R = new RegExp(\n  '^\\\\[(' + LINK_INSIDE + ')\\\\]\\\\(' + LINK_HREF_AND_TITLE + '\\\\)'\n)\nconst IMAGE_R = /^!\\[(.*?)\\]\\( *((?:\\([^)]*\\)|[^() ])*) *\"?([^)\"]*)?\"?\\)/\n\nconst NON_PARAGRAPH_BLOCK_SYNTAXES = [\n  BLOCKQUOTE_R,\n  CODE_BLOCK_FENCED_R,\n  CODE_BLOCK_R,\n  HEADING_R,\n  HEADING_SETEXT_R,\n  HEADING_ATX_COMPLIANT_R,\n  NP_TABLE_R,\n  ORDERED_LIST_R,\n  UNORDERED_LIST_R,\n]\n\nconst BLOCK_SYNTAXES = [\n  ...NON_PARAGRAPH_BLOCK_SYNTAXES,\n  PARAGRAPH_R,\n  HTML_BLOCK_ELEMENT_R,\n  HTML_COMMENT_R,\n  HTML_SELF_CLOSING_ELEMENT_R,\n]\n\nfunction trimEnd(str: string) {\n  let end = str.length\n  while (end > 0 && str[end - 1] <= ' ') end--\n  return str.slice(0, end)\n}\n\nfunction containsBlockSyntax(input: string) {\n  return BLOCK_SYNTAXES.some(r => r.test(input))\n}\n\n/** Remove symmetrical leading and trailing quotes */\nfunction unquote(str: string) {\n  const first = str[0]\n  if (\n    (first === '\"' || first === \"'\") &&\n    str.length >= 2 &&\n    str[str.length - 1] === first\n  ) {\n    return str.slice(1, -1)\n  }\n  return str\n}\n\n// based on https://stackoverflow.com/a/18123682/1141611\n// not complete, but probably good enough\nexport function slugify(str: string) {\n  return str\n    .replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g, 'a')\n    .replace(/[çÇ]/g, 'c')\n    .replace(/[ðÐ]/g, 'd')\n    .replace(/[ÈÉÊËéèêë]/g, 'e')\n    .replace(/[ÏïÎîÍíÌì]/g, 'i')\n    .replace(/[Ññ]/g, 'n')\n    .replace(/[øØœŒÕõÔôÓóÒò]/g, 'o')\n    .replace(/[ÜüÛûÚúÙù]/g, 'u')\n    .replace(/[ŸÿÝý]/g, 'y')\n    .replace(/[^a-z0-9- ]/gi, '')\n    .replace(/ /gi, '-')\n    .toLowerCase()\n}\n\nfunction parseTableAlignCapture(alignCapture: string) {\n  if (TABLE_RIGHT_ALIGN.test(alignCapture)) {\n    return 'right'\n  } else if (TABLE_CENTER_ALIGN.test(alignCapture)) {\n    return 'center'\n  } else if (TABLE_LEFT_ALIGN.test(alignCapture)) {\n    return 'left'\n  }\n\n  return null\n}\n\nfunction parseTableRow(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State,\n  tableOutput: boolean\n): MarkdownToJSX.ParserResult[][] {\n  const prevInTable = state.inTable\n\n  state.inTable = true\n\n  let cells: MarkdownToJSX.ParserResult[][] = [[]]\n  let acc = ''\n\n  function flush() {\n    if (!acc) return\n\n    const cell = cells[cells.length - 1]\n    cell.push.apply(cell, parse(acc, state))\n    acc = ''\n  }\n\n  source\n    .trim()\n    // isolate situations where a pipe should be ignored (inline code, escaped, etc)\n    .split(/(`[^`]*`|\\\\\\||\\|)/)\n    .filter(Boolean)\n    .forEach((fragment, i, arr) => {\n      if (fragment.trim() === '|') {\n        flush()\n\n        if (tableOutput) {\n          if (i !== 0 && i !== arr.length - 1) {\n            // Split the current row\n            cells.push([])\n          }\n\n          return\n        }\n      }\n\n      acc += fragment\n    })\n\n  flush()\n\n  state.inTable = prevInTable\n\n  return cells\n}\n\nfunction parseTableAlign(source: string /*, parse, state*/) {\n  const alignText = source.replace(TABLE_TRIM_PIPES, '').split('|')\n\n  return alignText.map(parseTableAlignCapture)\n}\n\nfunction parseTableCells(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  const rowsText = source.trim().split('\\n')\n\n  return rowsText.map(function (rowText) {\n    return parseTableRow(rowText, parse, state, true)\n  })\n}\n\nfunction parseTable(\n  capture: RegExpMatchArray,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  /**\n   * The table syntax makes some other parsing angry so as a bit of a hack even if alignment and/or cell rows are missing,\n   * we'll still run a detected first row through the parser and then just emit a paragraph.\n   */\n  state.inline = true\n  const align = capture[2] ? parseTableAlign(capture[2]) : []\n  const cells = capture[3] ? parseTableCells(capture[3], parse, state) : []\n  const header = parseTableRow(capture[1], parse, state, !!cells.length)\n  state.inline = false\n\n  return cells.length\n    ? {\n        align: align,\n        cells: cells,\n        header: header,\n        type: RuleType.table,\n      }\n    : {\n        children: header,\n        type: RuleType.paragraph,\n      }\n}\n\nfunction getTableStyle(node, colIndex) {\n  return node.align[colIndex] == null\n    ? {}\n    : {\n        textAlign: node.align[colIndex],\n      }\n}\n\n/** TODO: remove for react 16 */\nfunction normalizeAttributeKey(key) {\n  const hyphenIndex = key.indexOf('-')\n\n  if (hyphenIndex !== -1 && key.match(HTML_CUSTOM_ATTR_R) === null) {\n    key = key.replace(CAPTURE_LETTER_AFTER_HYPHEN, function (_, letter) {\n      return letter.toUpperCase()\n    })\n  }\n\n  return key\n}\n\nfunction attributeValueToJSXPropValue(\n  tag: MarkdownToJSX.HTMLTags,\n  key: keyof React.AllHTMLAttributes<Element>,\n  value: string,\n  sanitizeUrlFn: MarkdownToJSX.Options['sanitizer']\n): any {\n  if (key === 'style') {\n    return value.split(/;\\s?/).reduce(function (styles, kvPair) {\n      const key = kvPair.slice(0, kvPair.indexOf(':'))\n\n      // snake-case to camelCase\n      // also handles PascalCasing vendor prefixes\n      const camelCasedKey = key\n        .trim()\n        .replace(/(-[a-z])/g, substr => substr[1].toUpperCase())\n\n      // key.length + 1 to skip over the colon\n      styles[camelCasedKey] = kvPair.slice(key.length + 1).trim()\n\n      return styles\n    }, {})\n  } else if (key === 'href' || key === 'src') {\n    return sanitizeUrlFn(value, tag, key)\n  } else if (value.match(INTERPOLATION_R)) {\n    // return as a string and let the consumer decide what to do with it\n    value = value.slice(1, value.length - 1)\n  }\n\n  if (value === 'true') {\n    return true\n  } else if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n\nfunction normalizeWhitespace(source: string): string {\n  return source\n    .replace(CR_NEWLINE_R, '\\n')\n    .replace(FORMFEED_R, '')\n    .replace(TAB_R, '    ')\n}\n\n/**\n * Creates a parser for a given set of rules, with the precedence\n * specified as a list of rules.\n *\n * @rules: an object containing\n * rule type -> {match, order, parse} objects\n * (lower order is higher precedence)\n * (Note: `order` is added to defaultRules after creation so that\n *  the `order` of defaultRules in the source matches the `order`\n *  of defaultRules in terms of `order` fields.)\n *\n * @returns The resulting parse function, with the following parameters:\n *   @source: the input source string to be parsed\n *   @state: an optional object to be threaded through parse\n *     calls. Allows clients to add stateful operations to\n *     parsing, such as keeping track of how many levels deep\n *     some nesting is. For an example use-case, see passage-ref\n *     parsing in src/widgets/passage/passage-markdown.jsx\n */\nfunction parserFor(\n  rules: MarkdownToJSX.Rules\n): (\n  source: string,\n  state: MarkdownToJSX.State\n) => ReturnType<MarkdownToJSX.NestedParser> {\n  // Sorts rules in order of increasing order, then\n  // ascending rule name in case of ties.\n  let ruleList = Object.keys(rules)\n\n  if (process.env.NODE_ENV !== 'production') {\n    ruleList.forEach(function (type) {\n      let order = rules[type].order\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        (typeof order !== 'number' || !isFinite(order))\n      ) {\n        console.warn(\n          'markdown-to-jsx: Invalid order for rule `' + type + '`: ' + order\n        )\n      }\n    })\n  }\n\n  ruleList.sort(function (typeA, typeB) {\n    let orderA = rules[typeA].order\n    let orderB = rules[typeB].order\n\n    // Sort based on increasing order\n    if (orderA !== orderB) {\n      return orderA - orderB\n    } else if (typeA < typeB) {\n      return -1\n    }\n\n    return 1\n  })\n\n  function nestedParse(\n    source: string,\n    state: MarkdownToJSX.State\n  ): MarkdownToJSX.ParserResult[] {\n    let result = []\n    let rule\n    let ruleType = ''\n    let parsed\n    let currCaptureString = ''\n\n    state.prevCapture = state.prevCapture || ''\n\n    // We store the previous capture so that match functions can\n    // use some limited amount of lookbehind. Lists use this to\n    // ensure they don't match arbitrary '- ' or '* ' in inline\n    // text (see the list rule for more information).\n    while (source) {\n      let i = 0\n      while (i < ruleList.length) {\n        ruleType = ruleList[i]\n        rule = rules[ruleType]\n\n        if (state.inline && !rule.match.inline) {\n          i++\n          continue\n        }\n\n        const capture = rule.match(source, state)\n\n        if (capture) {\n          currCaptureString = capture[0]\n\n          // retain what's been processed so far for lookbacks\n          state.prevCapture += currCaptureString\n\n          source = source.substring(currCaptureString.length)\n\n          parsed = rule.parse(capture, nestedParse, state)\n\n          // We also let rules override the default type of\n          // their parsed node if they would like to, so that\n          // there can be a single output function for all links,\n          // even if there are several rules to parse them.\n          if (parsed.type == null) {\n            parsed.type = ruleType as unknown as RuleType\n          }\n\n          result.push(parsed)\n          break\n        }\n\n        i++\n      }\n    }\n\n    // reset on exit\n    state.prevCapture = ''\n\n    return result\n  }\n\n  return function outerParse(source, state) {\n    return nestedParse(normalizeWhitespace(source), state)\n  }\n}\n\n/**\n * Marks a matcher function as eligible for being run inside an inline context;\n * allows us to do a little less work in the nested parser.\n */\nfunction allowInline<T extends Function & { inline?: 0 | 1 }>(fn: T) {\n  fn.inline = 1\n\n  return fn\n}\n\n// Creates a match function for an inline scoped or simple element from a regex\nfunction inlineRegex(regex: RegExp) {\n  return allowInline(function match(source, state: MarkdownToJSX.State) {\n    if (state.inline) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// basically any inline element except links\nfunction simpleInlineRegex(regex: RegExp) {\n  return allowInline(function match(\n    source: string,\n    state: MarkdownToJSX.State\n  ) {\n    if (state.inline || state.simple) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// Creates a match function for a block scoped element from a regex\nfunction blockRegex(regex: RegExp) {\n  return function match(source: string, state: MarkdownToJSX.State) {\n    if (state.inline || state.simple) {\n      return null\n    } else {\n      return regex.exec(source)\n    }\n  }\n}\n\n// Creates a match function from a regex, ignoring block/inline scope\nfunction anyScopeRegex(regex: RegExp) {\n  return allowInline(function match(source: string /*, state*/) {\n    return regex.exec(source)\n  })\n}\n\nfunction matchParagraph(source: string, state: MarkdownToJSX.State) {\n  if (state.inline || state.simple) {\n    return null\n  }\n\n  let match = ''\n\n  source.split('\\n').every(line => {\n    line += '\\n'\n\n    // bail out on first sign of non-paragraph block\n    if (NON_PARAGRAPH_BLOCK_SYNTAXES.some(regex => regex.test(line))) {\n      return false\n    }\n\n    match += line\n\n    return !!line.trim()\n  })\n\n  const captured = trimEnd(match)\n  if (captured == '') {\n    return null\n  }\n\n  // parseCaptureInline expects the inner content to be at index 2\n  // because index 1 is the delimiter for text formatting syntaxes\n  return [match, , captured]\n}\n\nexport function sanitizer(url: string): string {\n  try {\n    const decoded = decodeURIComponent(url).replace(/[^A-Za-z0-9/:]/g, '')\n\n    if (decoded.match(/^\\s*(javascript|vbscript|data(?!:image)):/i)) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'Anchor URL contains an unsafe JavaScript/VBScript/data expression, it will not be rendered.',\n          decoded\n        )\n      }\n\n      return null\n    }\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(\n        'Anchor URL could not be decoded due to malformed syntax or characters, it will not be rendered.',\n        url\n      )\n    }\n\n    // decodeURIComponent sometimes throws a URIError\n    // See `decodeURIComponent('a%AFc');`\n    // http://stackoverflow.com/questions/9064536/javascript-decodeuricomponent-malformed-uri-exception\n    return null\n  }\n\n  return url\n}\n\nfunction unescapeUrl(rawUrlString: string): string {\n  return rawUrlString.replace(UNESCAPE_URL_R, '$1')\n}\n\n/**\n * Everything inline, including links.\n */\nfunction parseInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = true\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\n/**\n * Anything inline that isn't a link.\n */\nfunction parseSimpleInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = false\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\nfunction parseBlock(\n  parse,\n  children,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  state.inline = false\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  return result\n}\n\nconst parseCaptureInline: MarkdownToJSX.Parser<{\n  children: MarkdownToJSX.ParserResult[]\n}> = (capture, parse, state: MarkdownToJSX.State) => {\n  return {\n    children: parseInline(parse, capture[2], state),\n  }\n}\n\nfunction captureNothing() {\n  return {}\n}\n\nfunction renderNothing() {\n  return null\n}\n\nfunction reactFor(render) {\n  return function patchedRender(\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State = {}\n  ): React.ReactNode[] {\n    if (Array.isArray(ast)) {\n      const oldKey = state.key\n      const result = []\n\n      // map nestedOutput over the ast, except group any text\n      // nodes together into a single string output.\n      let lastWasString = false\n\n      for (let i = 0; i < ast.length; i++) {\n        state.key = i\n\n        const nodeOut = patchedRender(ast[i], state)\n        const isString = typeof nodeOut === 'string'\n\n        if (isString && lastWasString) {\n          result[result.length - 1] += nodeOut\n        } else if (nodeOut !== null) {\n          result.push(nodeOut)\n        }\n\n        lastWasString = isString\n      }\n\n      state.key = oldKey\n\n      return result\n    }\n\n    return render(ast, patchedRender, state)\n  }\n}\n\nfunction createRenderer(\n  rules: MarkdownToJSX.Rules,\n  userRender?: MarkdownToJSX.Options['renderRule']\n) {\n  return function renderRule(\n    ast: MarkdownToJSX.ParserResult,\n    render: MarkdownToJSX.RuleOutput,\n    state: MarkdownToJSX.State\n  ): React.ReactNode {\n    const renderer = rules[ast.type].render as MarkdownToJSX.Rule['render']\n\n    return userRender\n      ? userRender(() => renderer(ast, render, state), ast, render, state)\n      : renderer(ast, render, state)\n  }\n}\n\nfunction cx(...args) {\n  return args.filter(Boolean).join(' ')\n}\n\nfunction get(src: Object, path: string, fb?: any) {\n  let ptr = src\n  const frags = path.split('.')\n\n  while (frags.length) {\n    ptr = ptr[frags[0]]\n\n    if (ptr === undefined) break\n    else frags.shift()\n  }\n\n  return ptr || fb\n}\n\nfunction getTag(tag: string, overrides: MarkdownToJSX.Overrides) {\n  const override = get(overrides, tag)\n\n  if (!override) return tag\n\n  return typeof override === 'function' ||\n    (typeof override === 'object' && 'render' in override)\n    ? override\n    : get(overrides, `${tag}.component`, tag)\n}\n\nexport function compiler(\n  markdown: string = '',\n  options: MarkdownToJSX.Options = {}\n) {\n  options.overrides = options.overrides || {}\n  options.sanitizer = options.sanitizer || sanitizer\n  options.slugify = options.slugify || slugify\n  options.namedCodesToUnicode = options.namedCodesToUnicode\n    ? { ...namedCodesToUnicode, ...options.namedCodesToUnicode }\n    : namedCodesToUnicode\n\n  options.createElement = options.createElement || React.createElement\n\n  // JSX custom pragma\n  // eslint-disable-next-line no-unused-vars\n  function h(\n    // locally we always will render a known string tag\n    tag: MarkdownToJSX.HTMLTags,\n    props: Parameters<MarkdownToJSX.CreateElement>[1] & {\n      className?: string\n      id?: string\n    },\n    ...children\n  ) {\n    const overrideProps = get(options.overrides, `${tag}.props`, {})\n\n    return options.createElement(\n      getTag(tag, options.overrides),\n      {\n        ...props,\n        ...overrideProps,\n        className: cx(props?.className, overrideProps.className) || undefined,\n      },\n      ...children\n    )\n  }\n\n  function compile(input: string): React.JSX.Element {\n    input = input.replace(FRONT_MATTER_R, '')\n\n    let inline = false\n\n    if (options.forceInline) {\n      inline = true\n    } else if (!options.forceBlock) {\n      /**\n       * should not contain any block-level markdown like newlines, lists, headings,\n       * thematic breaks, blockquotes, tables, etc\n       */\n      inline = SHOULD_RENDER_AS_BLOCK_R.test(input) === false\n    }\n\n    const arr = emitter(\n      parser(\n        inline\n          ? input\n          : `${trimEnd(input).replace(TRIM_STARTING_NEWLINES, '')}\\n\\n`,\n        {\n          inline,\n        }\n      )\n    )\n\n    while (\n      typeof arr[arr.length - 1] === 'string' &&\n      !arr[arr.length - 1].trim()\n    ) {\n      arr.pop()\n    }\n\n    if (options.wrapper === null) {\n      return arr\n    }\n\n    const wrapper = options.wrapper || (inline ? 'span' : 'div')\n    let jsx\n\n    if (arr.length > 1 || options.forceWrapper) {\n      jsx = arr\n    } else if (arr.length === 1) {\n      jsx = arr[0]\n\n      // TODO: remove this for React 16\n      if (typeof jsx === 'string') {\n        return <span key=\"outer\">{jsx}</span>\n      } else {\n        return jsx\n      }\n    } else {\n      // TODO: return null for React 16\n      jsx = null\n    }\n\n    return options.createElement(\n      wrapper,\n      { key: 'outer' },\n      jsx\n    ) as React.JSX.Element\n  }\n\n  function attrStringToMap(\n    tag: MarkdownToJSX.HTMLTags,\n    str: string\n  ): React.JSX.IntrinsicAttributes {\n    const attributes = str.match(ATTR_EXTRACTOR_R)\n    if (!attributes) {\n      return null\n    }\n\n    return attributes.reduce(function (map, raw) {\n      const delimiterIdx = raw.indexOf('=')\n\n      if (delimiterIdx !== -1) {\n        const key = normalizeAttributeKey(raw.slice(0, delimiterIdx)).trim()\n        const value = unquote(raw.slice(delimiterIdx + 1).trim())\n\n        const mappedKey = ATTRIBUTE_TO_JSX_PROP_MAP[key] || key\n\n        // bail out, not supported\n        if (mappedKey === 'ref') return map\n\n        const normalizedValue = (map[mappedKey] = attributeValueToJSXPropValue(\n          tag,\n          key,\n          value,\n          options.sanitizer\n        ))\n\n        if (\n          typeof normalizedValue === 'string' &&\n          (HTML_BLOCK_ELEMENT_R.test(normalizedValue) ||\n            HTML_SELF_CLOSING_ELEMENT_R.test(normalizedValue))\n        ) {\n          map[mappedKey] = compile(normalizedValue.trim())\n        }\n      } else if (raw !== 'style') {\n        map[ATTRIBUTE_TO_JSX_PROP_MAP[raw] || raw] = true\n      }\n\n      return map\n    }, {})\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof markdown !== 'string') {\n      throw new Error(`markdown-to-jsx: the first argument must be\n                             a string`)\n    }\n\n    if (\n      Object.prototype.toString.call(options.overrides) !== '[object Object]'\n    ) {\n      throw new Error(`markdown-to-jsx: options.overrides (second argument property) must be\n                             undefined or an object literal with shape:\n                             {\n                                htmltagname: {\n                                    component: string|ReactComponent(optional),\n                                    props: object(optional)\n                                }\n                             }`)\n    }\n  }\n\n  const footnotes: { footnote: string; identifier: string }[] = []\n  const refs: { [key: string]: { target: string; title: string } } = {}\n\n  /**\n   * each rule's react() output function goes through our custom\n   * h() JSX pragma; this allows the override functionality to be\n   * automatically applied\n   */\n  // @ts-ignore\n  const rules: MarkdownToJSX.Rules = {\n    [RuleType.blockQuote]: {\n      match: blockRegex(BLOCKQUOTE_R),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        const [, alert, content] = capture[0]\n          .replace(BLOCKQUOTE_TRIM_LEFT_MULTILINE_R, '')\n          .match(BLOCKQUOTE_ALERT_R)\n\n        return {\n          alert,\n          children: parse(content, state),\n        }\n      },\n      render(node, output, state) {\n        const props = {\n          key: state.key,\n        } as Record<string, unknown>\n\n        if (node.alert) {\n          props.className =\n            'markdown-alert-' +\n            options.slugify(node.alert.toLowerCase(), slugify)\n\n          node.children.unshift({\n            attrs: {},\n            children: [{ type: RuleType.text, text: node.alert }],\n            noInnerParse: true,\n            type: RuleType.htmlBlock,\n            tag: 'header',\n          })\n        }\n\n        return h('blockquote', props, output(node.children, state))\n      },\n    },\n\n    [RuleType.breakLine]: {\n      match: anyScopeRegex(BREAK_LINE_R),\n      order: Priority.HIGH,\n      parse: captureNothing,\n      render(_, __, state) {\n        return <br key={state.key} />\n      },\n    },\n\n    [RuleType.breakThematic]: {\n      match: blockRegex(BREAK_THEMATIC_R),\n      order: Priority.HIGH,\n      parse: captureNothing,\n      render(_, __, state) {\n        return <hr key={state.key} />\n      },\n    },\n\n    [RuleType.codeBlock]: {\n      match: blockRegex(CODE_BLOCK_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          lang: undefined,\n          text: trimEnd(capture[0].replace(/^ {4}/gm, '')).replace(\n            TEXT_UNESCAPE_R,\n            '$1'\n          ),\n        }\n      },\n\n      render(node, output, state) {\n        return (\n          <pre key={state.key}>\n            <code\n              {...node.attrs}\n              className={node.lang ? `lang-${node.lang}` : ''}\n            >\n              {node.text}\n            </code>\n          </pre>\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      attrs?: ReturnType<typeof attrStringToMap>\n      lang?: string\n      text: string\n    }>,\n\n    [RuleType.codeFenced]: {\n      match: blockRegex(CODE_BLOCK_FENCED_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          // if capture[3] it's additional metadata\n          attrs: attrStringToMap('code', capture[3] || ''),\n          lang: capture[2] || undefined,\n          text: capture[4].replace(TEXT_UNESCAPE_R, '$1'),\n          type: RuleType.codeBlock,\n        }\n      },\n    },\n\n    [RuleType.codeInline]: {\n      match: simpleInlineRegex(CODE_INLINE_R),\n      order: Priority.LOW,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[2].replace(TEXT_UNESCAPE_R, '$1'),\n        }\n      },\n      render(node, output, state) {\n        return <code key={state.key}>{node.text}</code>\n      },\n    },\n\n    /**\n     * footnotes are emitted at the end of compilation in a special <footer> block\n     */\n    [RuleType.footnote]: {\n      match: blockRegex(FOOTNOTE_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        footnotes.push({\n          footnote: capture[2],\n          identifier: capture[1],\n        })\n\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.footnoteReference]: {\n      match: inlineRegex(FOOTNOTE_REFERENCE_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse*/) {\n        return {\n          target: `#${options.slugify(capture[1], slugify)}`,\n          text: capture[1],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <a key={state.key} href={options.sanitizer(node.target, 'a', 'href')}>\n            <sup key={state.key}>{node.text}</sup>\n          </a>\n        )\n      },\n    } as MarkdownToJSX.Rule<{ target: string; text: string }>,\n\n    [RuleType.gfmTask]: {\n      match: inlineRegex(GFM_TASK_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          completed: capture[1].toLowerCase() === 'x',\n        }\n      },\n      render(node, output, state) {\n        return (\n          <input\n            checked={node.completed}\n            key={state.key}\n            readOnly\n            type=\"checkbox\"\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{ completed: boolean }>,\n\n    [RuleType.heading]: {\n      match: blockRegex(\n        options.enforceAtxHeadings ? HEADING_ATX_COMPLIANT_R : HEADING_R\n      ),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[2], state),\n          id: options.slugify(capture[2], slugify),\n          level: capture[1].length as MarkdownToJSX.HeadingNode['level'],\n        }\n      },\n      render(node, output, state) {\n        return h(\n          `h${node.level}`,\n          { id: node.id, key: state.key },\n          output(node.children, state)\n        )\n      },\n    },\n\n    [RuleType.headingSetext]: {\n      match: blockRegex(HEADING_SETEXT_R),\n      order: Priority.MAX,\n      parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[1], state),\n          level: capture[2] === '=' ? 1 : 2,\n          type: RuleType.heading,\n        }\n      },\n    },\n\n    [RuleType.htmlBlock]: {\n      /**\n       * find the first matching end tag and process the interior\n       */\n      match: anyScopeRegex(HTML_BLOCK_ELEMENT_R),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        const [, whitespace] = capture[3].match(HTML_LEFT_TRIM_AMOUNT_R)\n\n        const trimmer = new RegExp(`^${whitespace}`, 'gm')\n        const trimmed = capture[3].replace(trimmer, '')\n\n        const parseFunc = containsBlockSyntax(trimmed)\n          ? parseBlock\n          : parseInline\n\n        const tagName = capture[1].toLowerCase() as MarkdownToJSX.HTMLTags\n        const noInnerParse =\n          DO_NOT_PROCESS_HTML_ELEMENTS.indexOf(tagName) !== -1\n\n        const tag = (\n          noInnerParse ? tagName : capture[1]\n        ).trim() as MarkdownToJSX.HTMLTags\n\n        const ast = {\n          attrs: attrStringToMap(tag, capture[2]),\n          noInnerParse: noInnerParse,\n          tag,\n        } as {\n          attrs: ReturnType<typeof attrStringToMap>\n          children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n          noInnerParse: Boolean\n          tag: MarkdownToJSX.HTMLTags\n          text?: string | undefined\n        }\n\n        state.inAnchor = state.inAnchor || tagName === 'a'\n\n        if (noInnerParse) {\n          ast.text = capture[3]\n        } else {\n          ast.children = parseFunc(parse, trimmed, state)\n        }\n\n        /**\n         * if another html block is detected within, parse as block,\n         * otherwise parse as inline to pick up any further markdown\n         */\n        state.inAnchor = false\n\n        return ast\n      },\n      render(node, output, state) {\n        return (\n          <node.tag key={state.key} {...node.attrs}>\n            {node.text || (node.children ? output(node.children, state) : '')}\n          </node.tag>\n        )\n      },\n    },\n\n    [RuleType.htmlSelfClosing]: {\n      /**\n       * find the first matching end tag and process the interior\n       */\n      match: anyScopeRegex(HTML_SELF_CLOSING_ELEMENT_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        const tag = capture[1].trim() as MarkdownToJSX.HTMLTags\n\n        return {\n          attrs: attrStringToMap(tag, capture[2] || ''),\n          tag,\n        }\n      },\n      render(node, output, state) {\n        return <node.tag {...node.attrs} key={state.key} />\n      },\n    },\n\n    [RuleType.htmlComment]: {\n      match: anyScopeRegex(HTML_COMMENT_R),\n      order: Priority.HIGH,\n      parse() {\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.image]: {\n      match: simpleInlineRegex(IMAGE_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          alt: capture[1],\n          target: unescapeUrl(capture[2]),\n          title: capture[3],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <img\n            key={state.key}\n            alt={node.alt || undefined}\n            title={node.title || undefined}\n            src={options.sanitizer(node.target, 'img', 'src')}\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      alt?: string\n      target: string\n      title?: string\n    }>,\n\n    [RuleType.link]: {\n      match: inlineRegex(LINK_R),\n      order: Priority.LOW,\n      parse(capture, parse, state) {\n        return {\n          children: parseSimpleInline(parse, capture[1], state),\n          target: unescapeUrl(capture[2]),\n          title: capture[3],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <a\n            key={state.key}\n            href={options.sanitizer(node.target, 'a', 'href')}\n            title={node.title}\n          >\n            {output(node.children, state)}\n          </a>\n        )\n      },\n    },\n\n    // https://daringfireball.net/projects/markdown/syntax#autolink\n    [RuleType.linkAngleBraceStyleDetector]: {\n      match: inlineRegex(LINK_AUTOLINK_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkBareUrlDetector]: {\n      match: allowInline((source, state) => {\n        if (state.inAnchor || options.disableAutoLink) {\n          return null\n        }\n\n        return inlineRegex(LINK_AUTOLINK_BARE_URL_R)(source, state)\n      }),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          title: undefined,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkMailtoDetector]: {\n      match: inlineRegex(LINK_AUTOLINK_MAILTO_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        let address = capture[1]\n        let target = capture[1]\n\n        // Check for a `mailto:` already existing in the link:\n        if (!AUTOLINK_MAILTO_CHECK_R.test(target)) {\n          target = 'mailto:' + target\n        }\n\n        return {\n          children: [\n            {\n              text: address.replace('mailto:', ''),\n              type: RuleType.text,\n            },\n          ],\n          target: target,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.orderedList]: generateListRule(\n      h,\n      ORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.OrderedListNode>,\n\n    [RuleType.unorderedList]: generateListRule(\n      h,\n      UNORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.UnorderedListNode>,\n\n    [RuleType.newlineCoalescer]: {\n      match: blockRegex(CONSECUTIVE_NEWLINE_R),\n      order: Priority.LOW,\n      parse: captureNothing,\n      render(/*node, output, state*/) {\n        return '\\n'\n      },\n    },\n\n    [RuleType.paragraph]: {\n      match: allowInline(matchParagraph),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <p key={state.key}>{output(node.children, state)}</p>\n      },\n    } as MarkdownToJSX.Rule<ReturnType<typeof parseCaptureInline>>,\n\n    [RuleType.ref]: {\n      match: inlineRegex(REFERENCE_IMAGE_OR_LINK),\n      order: Priority.MAX,\n      parse(capture /*, parse*/) {\n        refs[capture[1]] = {\n          target: capture[2],\n          title: capture[4],\n        }\n\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.refImage]: {\n      match: simpleInlineRegex(REFERENCE_IMAGE_R),\n      order: Priority.MAX,\n      parse(capture) {\n        return {\n          alt: capture[1] || undefined,\n          ref: capture[2],\n        }\n      },\n      render(node, output, state) {\n        return refs[node.ref] ? (\n          <img\n            key={state.key}\n            alt={node.alt}\n            src={options.sanitizer(refs[node.ref].target, 'img', 'src')}\n            title={refs[node.ref].title}\n          />\n        ) : null\n      },\n    } as MarkdownToJSX.Rule<{ alt?: string; ref: string }>,\n\n    [RuleType.refLink]: {\n      match: inlineRegex(REFERENCE_LINK_R),\n      order: Priority.MAX,\n      parse(capture, parse, state) {\n        return {\n          children: parse(capture[1], state),\n          fallbackChildren: capture[0],\n          ref: capture[2],\n        }\n      },\n      render(node, output, state) {\n        return refs[node.ref] ? (\n          <a\n            key={state.key}\n            href={options.sanitizer(refs[node.ref].target, 'a', 'href')}\n            title={refs[node.ref].title}\n          >\n            {output(node.children, state)}\n          </a>\n        ) : (\n          <span key={state.key}>{node.fallbackChildren}</span>\n        )\n      },\n    },\n\n    [RuleType.table]: {\n      match: blockRegex(NP_TABLE_R),\n      order: Priority.HIGH,\n      parse: parseTable,\n      render(node, output, state) {\n        const table = node as MarkdownToJSX.TableNode\n        return (\n          <table key={state.key}>\n            <thead>\n              <tr>\n                {table.header.map(function generateHeaderCell(content, i) {\n                  return (\n                    <th key={i} style={getTableStyle(table, i)}>\n                      {output(content, state)}\n                    </th>\n                  )\n                })}\n              </tr>\n            </thead>\n\n            <tbody>\n              {table.cells.map(function generateTableRow(row, i) {\n                return (\n                  <tr key={i}>\n                    {row.map(function generateTableCell(content, c) {\n                      return (\n                        <td key={c} style={getTableStyle(table, c)}>\n                          {output(content, state)}\n                        </td>\n                      )\n                    })}\n                  </tr>\n                )\n              })}\n            </tbody>\n          </table>\n        )\n      },\n    },\n\n    [RuleType.text]: {\n      // Here we look for anything followed by non-symbols,\n      // double newlines, or double-space-newlines\n      // We break on any symbol characters so that this grammar\n      // is easy to extend without needing to modify this regex\n      match: anyScopeRegex(TEXT_PLAIN_R),\n      order: Priority.MIN,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[0]\n            // nbsp -> unicode equivalent for named chars\n            .replace(HTML_CHAR_CODE_R, (full, inner) => {\n              return options.namedCodesToUnicode[inner]\n                ? options.namedCodesToUnicode[inner]\n                : full\n            }),\n        }\n      },\n      render(node /*, output, state*/) {\n        return node.text\n      },\n    },\n\n    [RuleType.textBolded]: {\n      match: simpleInlineRegex(TEXT_BOLD_R),\n      order: Priority.MED,\n      parse(capture, parse, state) {\n        return {\n          // capture[1] -> the syntax control character\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      render(node, output, state) {\n        return <strong key={state.key}>{output(node.children, state)}</strong>\n      },\n    },\n\n    [RuleType.textEmphasized]: {\n      match: simpleInlineRegex(TEXT_EMPHASIZED_R),\n      order: Priority.LOW,\n      parse(capture, parse, state) {\n        return {\n          // capture[1] -> opening * or _\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      render(node, output, state) {\n        return <em key={state.key}>{output(node.children, state)}</em>\n      },\n    },\n\n    [RuleType.textEscaped]: {\n      // We don't allow escaping numbers, letters, or spaces here so that\n      // backslashes used in plain text still get rendered. But allowing\n      // escaping anything else provides a very flexible escape mechanism,\n      // regardless of how this grammar is extended.\n      match: simpleInlineRegex(TEXT_ESCAPED_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[1],\n          type: RuleType.text,\n        }\n      },\n    },\n\n    [RuleType.textMarked]: {\n      match: simpleInlineRegex(TEXT_MARKED_R),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <mark key={state.key}>{output(node.children, state)}</mark>\n      },\n    },\n\n    [RuleType.textStrikethroughed]: {\n      match: simpleInlineRegex(TEXT_STRIKETHROUGHED_R),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <del key={state.key}>{output(node.children, state)}</del>\n      },\n    },\n  }\n\n  // Object.keys(rules).forEach(key => {\n  //   let { match: match, parse: parse } = rules[key]\n\n  //   // rules[key].match = (...args) => {\n  //   //   const start = performance.now()\n  //   //   const result = match(...args)\n  //   //   const delta = performance.now() - start\n\n  //   //   if (delta > 5)\n  //   //     console.warn(\n  //   //       `Slow match for ${key}: ${delta.toFixed(3)}ms, input: ${args[0]}`\n  //   //     )\n\n  //   //   return result\n  //   // }\n\n  //   rules[key].parse = (...args) => {\n  //     const start = performance.now()\n  //     const result = parse(...args)\n  //     const delta = performance.now() - start\n\n  //     console[delta > 5 ? 'warn' : 'log'](\n  //       `${key}:parse`,\n  //       `${delta.toFixed(3)}ms`,\n  //       args[0]\n  //     )\n\n  //     return result\n  //   }\n  // })\n\n  if (options.disableParsingRawHTML === true) {\n    delete rules[RuleType.htmlBlock]\n    delete rules[RuleType.htmlSelfClosing]\n  }\n\n  const parser = parserFor(rules)\n  const emitter: Function = reactFor(createRenderer(rules, options.renderRule))\n\n  const jsx = compile(markdown)\n\n  if (footnotes.length) {\n    return (\n      <div>\n        {jsx}\n        <footer key=\"footer\">\n          {footnotes.map(function createFootnote(def) {\n            return (\n              <div\n                id={options.slugify(def.identifier, slugify)}\n                key={def.identifier}\n              >\n                {def.identifier}\n                {emitter(parser(def.footnote, { inline: true }))}\n              </div>\n            )\n          })}\n        </footer>\n      </div>\n    )\n  }\n\n  return jsx\n}\n\n/**\n * A simple HOC for easy React use. Feed the markdown content as a direct child\n * and the rest is taken care of automatically.\n */\nconst Markdown: React.FC<\n  Omit<React.HTMLAttributes<Element>, 'children'> & {\n    children: string\n    options?: MarkdownToJSX.Options\n  }\n> = ({ children = '', options, ...props }) => {\n  if (process.env.NODE_ENV !== 'production' && typeof children !== 'string') {\n    console.error(\n      'markdown-to-jsx: <Markdown> component only accepts a single string as a child, received:',\n      children\n    )\n  }\n\n  return React.cloneElement(\n    compiler(children, options),\n    props as React.JSX.IntrinsicAttributes\n  )\n}\n\nexport namespace MarkdownToJSX {\n  /**\n   * RequireAtLeastOne<{ ... }> <- only requires at least one key\n   */\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>\n    }[Keys]\n\n  export type CreateElement = typeof React.createElement\n\n  export type HTMLTags = keyof React.JSX.IntrinsicElements\n\n  export type State = {\n    /** true if the current content is inside anchor link grammar */\n    inAnchor?: boolean\n    /** true if parsing in an inline context (subset of rules around formatting and links) */\n    inline?: boolean\n    /** true if in a table */\n    inTable?: boolean\n    /** use this for the `key` prop */\n    key?: React.Key\n    /** true if in a list */\n    list?: boolean\n    /** used for lookbacks */\n    prevCapture?: string\n    /** true if parsing in inline context w/o links */\n    simple?: boolean\n  }\n\n  export interface BlockQuoteNode {\n    alert?: string\n    children: MarkdownToJSX.ParserResult[]\n    type: typeof RuleType.blockQuote\n  }\n\n  export interface BreakLineNode {\n    type: typeof RuleType.breakLine\n  }\n\n  export interface BreakThematicNode {\n    type: typeof RuleType.breakThematic\n  }\n\n  export interface CodeBlockNode {\n    type: typeof RuleType.codeBlock\n    attrs?: React.JSX.IntrinsicAttributes\n    lang?: string\n    text: string\n  }\n\n  export interface CodeFencedNode {\n    type: typeof RuleType.codeFenced\n  }\n\n  export interface CodeInlineNode {\n    type: typeof RuleType.codeInline\n    text: string\n  }\n\n  export interface FootnoteNode {\n    type: typeof RuleType.footnote\n  }\n\n  export interface FootnoteReferenceNode {\n    type: typeof RuleType.footnoteReference\n    target: string\n    text: string\n  }\n\n  export interface GFMTaskNode {\n    type: typeof RuleType.gfmTask\n    completed: boolean\n  }\n\n  export interface HeadingNode {\n    type: typeof RuleType.heading\n    children: MarkdownToJSX.ParserResult[]\n    id: string\n    level: 1 | 2 | 3 | 4 | 5 | 6\n  }\n\n  export interface HeadingSetextNode {\n    type: typeof RuleType.headingSetext\n  }\n\n  export interface HTMLCommentNode {\n    type: typeof RuleType.htmlComment\n  }\n\n  export interface ImageNode {\n    type: typeof RuleType.image\n    alt?: string\n    target: string\n    title?: string\n  }\n\n  export interface LinkNode {\n    type: typeof RuleType.link\n    children: MarkdownToJSX.ParserResult[]\n    target: string\n    title?: string\n  }\n\n  export interface LinkAngleBraceNode {\n    type: typeof RuleType.linkAngleBraceStyleDetector\n  }\n\n  export interface LinkBareURLNode {\n    type: typeof RuleType.linkBareUrlDetector\n  }\n\n  export interface LinkMailtoNode {\n    type: typeof RuleType.linkMailtoDetector\n  }\n\n  export interface OrderedListNode {\n    type: typeof RuleType.orderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: true\n    start?: number\n  }\n\n  export interface UnorderedListNode {\n    type: typeof RuleType.unorderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: false\n  }\n\n  export interface NewlineNode {\n    type: typeof RuleType.newlineCoalescer\n  }\n\n  export interface ParagraphNode {\n    type: typeof RuleType.paragraph\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ReferenceNode {\n    type: typeof RuleType.ref\n  }\n\n  export interface ReferenceImageNode {\n    type: typeof RuleType.refImage\n    alt?: string\n    ref: string\n  }\n\n  export interface ReferenceLinkNode {\n    type: typeof RuleType.refLink\n    children: MarkdownToJSX.ParserResult[]\n    fallbackChildren: string\n    ref: string\n  }\n\n  export interface TableNode {\n    type: typeof RuleType.table\n    /**\n     * alignment for each table column\n     */\n    align: ('left' | 'right' | 'center')[]\n    cells: MarkdownToJSX.ParserResult[][][]\n    header: MarkdownToJSX.ParserResult[][]\n  }\n\n  export interface TableSeparatorNode {\n    type: typeof RuleType.tableSeparator\n  }\n\n  export interface TextNode {\n    type: typeof RuleType.text\n    text: string\n  }\n\n  export interface BoldTextNode {\n    type: typeof RuleType.textBolded\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ItalicTextNode {\n    type: typeof RuleType.textEmphasized\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface EscapedTextNode {\n    type: typeof RuleType.textEscaped\n  }\n\n  export interface MarkedTextNode {\n    type: typeof RuleType.textMarked\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface StrikethroughTextNode {\n    type: typeof RuleType.textStrikethroughed\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface HTMLNode {\n    type: typeof RuleType.htmlBlock\n    attrs: React.JSX.IntrinsicAttributes\n    children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n    noInnerParse: Boolean\n    tag: MarkdownToJSX.HTMLTags\n    text?: string | undefined\n  }\n\n  export interface HTMLSelfClosingNode {\n    type: typeof RuleType.htmlSelfClosing\n    attrs: React.JSX.IntrinsicAttributes\n    tag: string\n  }\n\n  export type ParserResult =\n    | BlockQuoteNode\n    | BreakLineNode\n    | BreakThematicNode\n    | CodeBlockNode\n    | CodeFencedNode\n    | CodeInlineNode\n    | FootnoteNode\n    | FootnoteReferenceNode\n    | GFMTaskNode\n    | HeadingNode\n    | HeadingSetextNode\n    | HTMLCommentNode\n    | ImageNode\n    | LinkNode\n    | LinkAngleBraceNode\n    | LinkBareURLNode\n    | LinkMailtoNode\n    | OrderedListNode\n    | UnorderedListNode\n    | NewlineNode\n    | ParagraphNode\n    | ReferenceNode\n    | ReferenceImageNode\n    | ReferenceLinkNode\n    | TableNode\n    | TableSeparatorNode\n    | TextNode\n    | BoldTextNode\n    | ItalicTextNode\n    | EscapedTextNode\n    | MarkedTextNode\n    | StrikethroughTextNode\n    | HTMLNode\n    | HTMLSelfClosingNode\n\n  export type NestedParser = (\n    input: string,\n    state?: MarkdownToJSX.State\n  ) => MarkdownToJSX.ParserResult[]\n\n  export type Parser<ParserOutput> = (\n    capture: RegExpMatchArray,\n    nestedParse: NestedParser,\n    state?: MarkdownToJSX.State\n  ) => ParserOutput\n\n  export type RuleOutput = (\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State\n  ) => React.JSX.Element\n\n  export type Rule<ParserOutput = MarkdownToJSX.ParserResult> = {\n    match: (\n      source: string,\n      state: MarkdownToJSX.State,\n      prevCapturedString?: string\n    ) => RegExpMatchArray\n    order: Priority\n    parse: MarkdownToJSX.Parser<Omit<ParserOutput, 'type'>>\n    render?: (\n      node: ParserOutput,\n      /**\n       * Continue rendering AST nodes if applicable.\n       */\n      render: RuleOutput,\n      state?: MarkdownToJSX.State\n    ) => React.ReactNode\n  }\n\n  export type Rules = {\n    [K in ParserResult['type']]: K extends typeof RuleType.table\n      ? Rule<Extract<ParserResult, { type: K | typeof RuleType.paragraph }>>\n      : Rule<Extract<ParserResult, { type: K }>>\n  }\n\n  export type Override =\n    | RequireAtLeastOne<{\n        component: React.ElementType\n        props: Object\n      }>\n    | React.ElementType\n\n  export type Overrides = {\n    [tag in HTMLTags]?: Override\n  } & {\n    [customComponent: string]: Override\n  }\n\n  export type Options = Partial<{\n    /**\n     * Ultimate control over the output of all rendered JSX.\n     */\n    createElement: (\n      tag: Parameters<CreateElement>[0],\n      props: React.JSX.IntrinsicAttributes,\n      ...children: React.ReactNode[]\n    ) => React.ReactNode\n\n    /**\n     * The library automatically generates an anchor tag for bare URLs included in the markdown\n     * document, but this behavior can be disabled if desired.\n     */\n    disableAutoLink: boolean\n\n    /**\n     * Disable the compiler's best-effort transcription of provided raw HTML\n     * into JSX-equivalent. This is the functionality that prevents the need to\n     * use `dangerouslySetInnerHTML` in React.\n     */\n    disableParsingRawHTML: boolean\n\n    /**\n     * Forces the compiler to have space between hash sign and the header text which\n     * is explicitly stated in the most of the markdown specs.\n     * https://github.github.com/gfm/#atx-heading\n     * `The opening sequence of # characters must be followed by a space or by the end of line.`\n     */\n    enforceAtxHeadings: boolean\n\n    /**\n     * Forces the compiler to always output content with a block-level wrapper\n     * (`<p>` or any block-level syntax your markdown already contains.)\n     */\n    forceBlock: boolean\n\n    /**\n     * Forces the compiler to always output content with an inline wrapper (`<span>`)\n     */\n    forceInline: boolean\n\n    /**\n     * Forces the compiler to wrap results, even if there is only a single\n     * child or no children.\n     */\n    forceWrapper: boolean\n\n    /**\n     * Supply additional HTML entity: unicode replacement mappings.\n     *\n     * Pass only the inner part of the entity as the key,\n     * e.g. `&le;` -> `{ \"le\": \"\\u2264\" }`\n     *\n     * By default\n     * the following entities are replaced with their unicode equivalents:\n     *\n     * ```\n     * &amp;\n     * &apos;\n     * &gt;\n     * &lt;\n     * &nbsp;\n     * &quot;\n     * ```\n     */\n    namedCodesToUnicode: {\n      [key: string]: string\n    }\n\n    /**\n     * Selectively control the output of particular HTML tags as they would be\n     * emitted by the compiler.\n     */\n    overrides: Overrides\n\n    /**\n     * Allows for full control over rendering of particular rules.\n     * For example, to implement a LaTeX renderer such as `react-katex`:\n     *\n     * ```\n     * renderRule(next, node, renderChildren, state) {\n     *   if (node.type === RuleType.codeBlock && node.lang === 'latex') {\n     *     return (\n     *       <TeX as=\"div\" key={state.key}>\n     *         {String.raw`${node.text}`}\n     *       </TeX>\n     *     )\n     *   }\n     *\n     *   return next();\n     * }\n     * ```\n     *\n     * Thar be dragons obviously, but you can do a lot with this\n     * (have fun!) To see how things work internally, check the `render`\n     * method in source for a particular rule.\n     */\n    renderRule: (\n      /** Resume normal processing, call this function as a fallback if you are not returning custom JSX. */\n      next: () => React.ReactNode,\n      /** the current AST node, use `RuleType` against `node.type` for identification */\n      node: ParserResult,\n      /** use as `renderChildren(node.children)` for block nodes */\n      renderChildren: RuleOutput,\n      /** contains `key` which should be supplied to the topmost JSX element */\n      state: State\n    ) => React.ReactNode\n\n    /**\n     * Override the built-in sanitizer function for URLs, etc if desired. The built-in version is available as a library export called `sanitizer`.\n     */\n    sanitizer: (\n      value: string,\n      tag: HTMLTags,\n      attribute: string\n    ) => string | null\n\n    /**\n     * Override normalization of non-URI-safe characters for use in generating\n     * HTML IDs for anchor linking purposes.\n     */\n    slugify: (input: string, defaultFn: (input: string) => string) => string\n\n    /**\n     * Declare the type of the wrapper to be used when there are multiple\n     * children to render. Set to `null` to get an array of children back\n     * without any wrapper, or use `React.Fragment` to get a React element\n     * that won't show up in the DOM.\n     */\n    wrapper: React.ElementType | null\n  }>\n}\n\nexport default Markdown\n", "import baseUnset from './_baseUnset.js';\n\n/**\n * Removes the property at `path` of `object`.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 7 } }] };\n * _.unset(object, 'a[0].b.c');\n * // => true\n *\n * console.log(object);\n * // => { 'a': [{ 'b': {} }] };\n *\n * _.unset(object, ['a', '0', 'b', 'c']);\n * // => true\n *\n * console.log(object);\n * // => { 'a': [{ 'b': {} }] };\n */\nfunction unset(object, path) {\n  return object == null ? true : baseUnset(object, path);\n}\n\nexport default unset;\n", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, Component } from 'react';\nimport { ADDITIONAL_PROPERTY_FLAG, deepEquals, descriptionId, getSchemaType, getTemplate, getUiOptions, ID_KEY, mergeObjects, TranslatableString, UI_OPTIONS_KEY, } from '@rjsf/utils';\nimport isObject from 'lodash-es/isObject.js';\nimport omit from 'lodash-es/omit.js';\nimport Markdown from 'markdown-to-jsx';\n/** The map of component type to FieldName */\nconst COMPONENT_TYPES = {\n    array: 'ArrayField',\n    boolean: 'BooleanField',\n    integer: 'NumberField',\n    number: 'NumberField',\n    object: 'ObjectField',\n    string: 'StringField',\n    null: 'NullField',\n};\n/** Computes and returns which `Field` implementation to return in order to render the field represented by the\n * `schema`. The `uiOptions` are used to alter what potential `Field` implementation is actually returned. If no\n * appropriate `Field` implementation can be found then a wrapper around `UnsupportedFieldTemplate` is used.\n *\n * @param schema - The schema from which to obtain the type\n * @param uiOptions - The UI Options that may affect the component decision\n * @param idSchema - The id that is passed to the `UnsupportedFieldTemplate`\n * @param registry - The registry from which fields and templates are obtained\n * @returns - The `Field` component that is used to render the actual field data\n */\nfunction getFieldComponent(schema, uiOptions, idSchema, registry) {\n    const field = uiOptions.field;\n    const { fields, translateString } = registry;\n    if (typeof field === 'function') {\n        return field;\n    }\n    if (typeof field === 'string' && field in fields) {\n        return fields[field];\n    }\n    const schemaType = getSchemaType(schema);\n    const type = Array.isArray(schemaType) ? schemaType[0] : schemaType || '';\n    const schemaId = schema.$id;\n    let componentName = COMPONENT_TYPES[type];\n    if (schemaId && schemaId in fields) {\n        componentName = schemaId;\n    }\n    // If the type is not defined and the schema uses 'anyOf' or 'oneOf', don't\n    // render a field and let the MultiSchemaField component handle the form display\n    if (!componentName && (schema.anyOf || schema.oneOf)) {\n        return () => null;\n    }\n    return componentName in fields\n        ? fields[componentName]\n        : () => {\n            const UnsupportedFieldTemplate = getTemplate('UnsupportedFieldTemplate', registry, uiOptions);\n            return (_jsx(UnsupportedFieldTemplate, { schema: schema, idSchema: idSchema, reason: translateString(TranslatableString.UnknownFieldType, [String(schema.type)]), registry: registry }));\n        };\n}\n/** The `SchemaFieldRender` component is the work-horse of react-jsonschema-form, determining what kind of real field to\n * render based on the `schema`, `uiSchema` and all the other props. It also deals with rendering the `anyOf` and\n * `oneOf` fields.\n *\n * @param props - The `FieldProps` for this component\n */\nfunction SchemaFieldRender(props) {\n    const { schema: _schema, idSchema: _idSchema, uiSchema, formData, errorSchema, idPrefix, idSeparator, name, onChange, onKeyChange, onDropPropertyClick, required, registry, wasPropertyKeyModified = false, } = props;\n    const { formContext, schemaUtils, globalUiOptions } = registry;\n    const uiOptions = getUiOptions(uiSchema, globalUiOptions);\n    const FieldTemplate = getTemplate('FieldTemplate', registry, uiOptions);\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, uiOptions);\n    const FieldHelpTemplate = getTemplate('FieldHelpTemplate', registry, uiOptions);\n    const FieldErrorTemplate = getTemplate('FieldErrorTemplate', registry, uiOptions);\n    const schema = schemaUtils.retrieveSchema(_schema, formData);\n    const fieldId = _idSchema[ID_KEY];\n    const idSchema = mergeObjects(schemaUtils.toIdSchema(schema, fieldId, formData, idPrefix, idSeparator), _idSchema);\n    /** Intermediary `onChange` handler for field components that will inject the `id` of the current field into the\n     * `onChange` chain if it is not already being provided from a deeper level in the hierarchy\n     */\n    const handleFieldComponentChange = useCallback((formData, newErrorSchema, id) => {\n        const theId = id || fieldId;\n        return onChange(formData, newErrorSchema, theId);\n    }, [fieldId, onChange]);\n    const FieldComponent = getFieldComponent(schema, uiOptions, idSchema, registry);\n    const disabled = Boolean(uiOptions.disabled ?? props.disabled);\n    const readonly = Boolean(uiOptions.readonly ?? (props.readonly || props.schema.readOnly || schema.readOnly));\n    const uiSchemaHideError = uiOptions.hideError;\n    // Set hideError to the value provided in the uiSchema, otherwise stick with the prop to propagate to children\n    const hideError = uiSchemaHideError === undefined ? props.hideError : Boolean(uiSchemaHideError);\n    const autofocus = Boolean(uiOptions.autofocus ?? props.autofocus);\n    if (Object.keys(schema).length === 0) {\n        return null;\n    }\n    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n    const { __errors, ...fieldErrorSchema } = errorSchema || {};\n    // See #439: uiSchema: Don't pass consumed class names or style to child components\n    const fieldUiSchema = omit(uiSchema, ['ui:classNames', 'classNames', 'ui:style']);\n    if (UI_OPTIONS_KEY in fieldUiSchema) {\n        fieldUiSchema[UI_OPTIONS_KEY] = omit(fieldUiSchema[UI_OPTIONS_KEY], ['classNames', 'style']);\n    }\n    const field = (_jsx(FieldComponent, { ...props, onChange: handleFieldComponentChange, idSchema: idSchema, schema: schema, uiSchema: fieldUiSchema, disabled: disabled, readonly: readonly, hideError: hideError, autofocus: autofocus, errorSchema: fieldErrorSchema, formContext: formContext, rawErrors: __errors }));\n    const id = idSchema[ID_KEY];\n    // If this schema has a title defined, but the user has set a new key/label, retain their input.\n    let label;\n    if (wasPropertyKeyModified) {\n        label = name;\n    }\n    else {\n        label =\n            ADDITIONAL_PROPERTY_FLAG in schema\n                ? name\n                : uiOptions.title || props.schema.title || schema.title || props.title || name;\n    }\n    const description = uiOptions.description || props.schema.description || schema.description || '';\n    const richDescription = uiOptions.enableMarkdownInDescription ? (_jsx(Markdown, { options: { disableParsingRawHTML: true }, children: description })) : (description);\n    const help = uiOptions.help;\n    const hidden = uiOptions.widget === 'hidden';\n    const classNames = ['form-group', 'field', `field-${getSchemaType(schema)}`];\n    if (!hideError && __errors && __errors.length > 0) {\n        classNames.push('field-error has-error has-danger');\n    }\n    if (uiSchema?.classNames) {\n        if (process.env.NODE_ENV !== 'production') {\n            console.warn(\"'uiSchema.classNames' is deprecated and may be removed in a major release; Use 'ui:classNames' instead.\");\n        }\n        classNames.push(uiSchema.classNames);\n    }\n    if (uiOptions.classNames) {\n        classNames.push(uiOptions.classNames);\n    }\n    const helpComponent = (_jsx(FieldHelpTemplate, { help: help, idSchema: idSchema, schema: schema, uiSchema: uiSchema, hasErrors: !hideError && __errors && __errors.length > 0, registry: registry }));\n    /*\n     * AnyOf/OneOf errors handled by child schema\n     * unless it can be rendered as select control\n     */\n    const errorsComponent = hideError || ((schema.anyOf || schema.oneOf) && !schemaUtils.isSelect(schema)) ? undefined : (_jsx(FieldErrorTemplate, { errors: __errors, errorSchema: errorSchema, idSchema: idSchema, schema: schema, uiSchema: uiSchema, registry: registry }));\n    const fieldProps = {\n        description: (_jsx(DescriptionFieldTemplate, { id: descriptionId(id), description: richDescription, schema: schema, uiSchema: uiSchema, registry: registry })),\n        rawDescription: description,\n        help: helpComponent,\n        rawHelp: typeof help === 'string' ? help : undefined,\n        errors: errorsComponent,\n        rawErrors: hideError ? undefined : __errors,\n        id,\n        label,\n        hidden,\n        onChange,\n        onKeyChange,\n        onDropPropertyClick,\n        required,\n        disabled,\n        readonly,\n        hideError,\n        displayLabel,\n        classNames: classNames.join(' ').trim(),\n        style: uiOptions.style,\n        formContext,\n        formData,\n        schema,\n        uiSchema,\n        registry,\n    };\n    const _AnyOfField = registry.fields.AnyOfField;\n    const _OneOfField = registry.fields.OneOfField;\n    const isReplacingAnyOrOneOf = uiSchema?.['ui:field'] && uiSchema?.['ui:fieldReplacesAnyOrOneOf'] === true;\n    return (_jsx(FieldTemplate, { ...fieldProps, children: _jsxs(_Fragment, { children: [field, schema.anyOf && !isReplacingAnyOrOneOf && !schemaUtils.isSelect(schema) && (_jsx(_AnyOfField, { name: name, disabled: disabled, readonly: readonly, hideError: hideError, errorSchema: errorSchema, formData: formData, formContext: formContext, idPrefix: idPrefix, idSchema: idSchema, idSeparator: idSeparator, onBlur: props.onBlur, onChange: props.onChange, onFocus: props.onFocus, options: schema.anyOf.map((_schema) => schemaUtils.retrieveSchema(isObject(_schema) ? _schema : {}, formData)), registry: registry, required: required, schema: schema, uiSchema: uiSchema })), schema.oneOf && !isReplacingAnyOrOneOf && !schemaUtils.isSelect(schema) && (_jsx(_OneOfField, { name: name, disabled: disabled, readonly: readonly, hideError: hideError, errorSchema: errorSchema, formData: formData, formContext: formContext, idPrefix: idPrefix, idSchema: idSchema, idSeparator: idSeparator, onBlur: props.onBlur, onChange: props.onChange, onFocus: props.onFocus, options: schema.oneOf.map((_schema) => schemaUtils.retrieveSchema(isObject(_schema) ? _schema : {}, formData)), registry: registry, required: required, schema: schema, uiSchema: uiSchema }))] }) }));\n}\n/** The `SchemaField` component determines whether it is necessary to rerender the component based on any props changes\n * and if so, calls the `SchemaFieldRender` component with the props.\n */\nclass SchemaField extends Component {\n    shouldComponentUpdate(nextProps) {\n        return !deepEquals(this.props, nextProps);\n    }\n    render() {\n        return _jsx(SchemaFieldRender, { ...this.props });\n    }\n}\nexport default SchemaField;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getWidget, getUiOptions, optionsList, hasWidget, } from '@rjsf/utils';\n/** The `StringField` component is used to render a schema field that represents a string type\n *\n * @param props - The `FieldProps` for this template\n */\nfunction StringField(props) {\n    const { schema, name, uiSchema, idSchema, formData, required, disabled = false, readonly = false, autofocus = false, onChange, onBlur, onFocus, registry, rawErrors, hideError, } = props;\n    const { title, format } = schema;\n    const { widgets, formContext, schemaUtils, globalUiOptions } = registry;\n    const enumOptions = schemaUtils.isSelect(schema) ? optionsList(schema, uiSchema) : undefined;\n    let defaultWidget = enumOptions ? 'select' : 'text';\n    if (format && hasWidget(schema, format, widgets)) {\n        defaultWidget = format;\n    }\n    const { widget = defaultWidget, placeholder = '', title: uiTitle, ...options } = getUiOptions(uiSchema);\n    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n    const label = uiTitle ?? title ?? name;\n    const Widget = getWidget(schema, widget, widgets);\n    return (_jsx(Widget, { options: { ...options, enumOptions }, schema: schema, uiSchema: uiSchema, id: idSchema.$id, name: name, label: label, hideLabel: !displayLabel, hideError: hideError, value: formData, onChange: onChange, onBlur: onBlur, onFocus: onFocus, required: required, disabled: disabled, readonly: readonly, formContext: formContext, autofocus: autofocus, registry: registry, placeholder: placeholder, rawErrors: rawErrors }));\n}\nexport default StringField;\n", "import { useEffect } from 'react';\n/** The `NullField` component is used to render a field in the schema is null. It also ensures that the `formData` is\n * also set to null if it has no value.\n *\n * @param props - The `FieldProps` for this template\n */\nfunction NullField(props) {\n    const { formData, onChange } = props;\n    useEffect(() => {\n        if (formData === undefined) {\n            onChange(null);\n        }\n    }, [formData, onChange]);\n    return null;\n}\nexport default NullField;\n", "import ArrayField from './ArrayField.js';\nimport BooleanField from './BooleanField.js';\nimport MultiSchemaField from './MultiSchemaField.js';\nimport NumberField from './NumberField.js';\nimport ObjectField from './ObjectField.js';\nimport SchemaField from './SchemaField.js';\nimport StringField from './StringField.js';\nimport NullField from './NullField.js';\nfunction fields() {\n    return {\n        AnyOfField: MultiSchemaField,\n        ArrayField: ArrayField,\n        // ArrayField falls back to SchemaField if ArraySchemaField is not defined, which it isn't by default\n        BooleanField,\n        NumberField,\n        ObjectField,\n        OneOfField: MultiSchemaField,\n        SchemaField,\n        StringField,\n        NullField,\n    };\n}\nexport default fields;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { descriptionId, getTemplate, getUiOptions, } from '@rjsf/utils';\n/** The `ArrayFieldDescriptionTemplate` component renders a `DescriptionFieldTemplate` with an `id` derived from\n * the `idSchema`.\n *\n * @param props - The `ArrayFieldDescriptionProps` for the component\n */\nexport default function ArrayFieldDescriptionTemplate(props) {\n    const { idSchema, description, registry, schema, uiSchema } = props;\n    const options = getUiOptions(uiSchema, registry.globalUiOptions);\n    const { label: displayLabel = true } = options;\n    if (!description || !displayLabel) {\n        return null;\n    }\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options);\n    return (_jsx(DescriptionFieldTemplate, { id: descriptionId(idSchema), description: description, schema: schema, uiSchema: uiSchema, registry: registry }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/** The `ArrayFieldItemTemplate` component is the template used to render an items of an array.\n *\n * @param props - The `ArrayFieldTemplateItemType` props for the component\n */\nexport default function ArrayFieldItemTemplate(props) {\n    const { children, className, disabled, hasToolbar, hasMoveDown, hasMoveUp, hasRemove, hasCopy, index, onCopyIndexClick, onDropIndexClick, onReorderClick, readonly, registry, uiSchema, } = props;\n    const { CopyButton, MoveDownButton, MoveUpButton, RemoveButton } = registry.templates.ButtonTemplates;\n    const btnStyle = {\n        flex: 1,\n        paddingLeft: 6,\n        paddingRight: 6,\n        fontWeight: 'bold',\n    };\n    return (_jsxs(\"div\", { className: className, children: [_jsx(\"div\", { className: hasToolbar ? 'col-xs-9' : 'col-xs-12', children: children }), hasToolbar && (_jsx(\"div\", { className: 'col-xs-3 array-item-toolbox', children: _jsxs(\"div\", { className: 'btn-group', style: {\n                        display: 'flex',\n                        justifyContent: 'space-around',\n                    }, children: [(hasMoveUp || hasMoveDown) && (_jsx(MoveUpButton, { style: btnStyle, disabled: disabled || readonly || !hasMoveUp, onClick: onReorderClick(index, index - 1), uiSchema: uiSchema, registry: registry })), (hasMoveUp || hasMoveDown) && (_jsx(MoveDownButton, { style: btnStyle, disabled: disabled || readonly || !hasMoveDown, onClick: onReorderClick(index, index + 1), uiSchema: uiSchema, registry: registry })), hasCopy && (_jsx(CopyButton, { style: btnStyle, disabled: disabled || readonly, onClick: onCopyIndexClick(index), uiSchema: uiSchema, registry: registry })), hasRemove && (_jsx(RemoveButton, { style: btnStyle, disabled: disabled || readonly, onClick: onDropIndexClick(index), uiSchema: uiSchema, registry: registry }))] }) }))] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { getTemplate, getUiOptions, } from '@rjsf/utils';\n/** The `ArrayFieldTemplate` component is the template used to render all items in an array.\n *\n * @param props - The `ArrayFieldTemplateItemType` props for the component\n */\nexport default function ArrayFieldTemplate(props) {\n    const { canAdd, className, disabled, idSchema, uiSchema, items, onAddClick, readonly, registry, required, schema, title, } = props;\n    const uiOptions = getUiOptions(uiSchema);\n    const ArrayFieldDescriptionTemplate = getTemplate('ArrayFieldDescriptionTemplate', registry, uiOptions);\n    const ArrayFieldItemTemplate = getTemplate('ArrayFieldItemTemplate', registry, uiOptions);\n    const ArrayFieldTitleTemplate = getTemplate('ArrayFieldTitleTemplate', registry, uiOptions);\n    // Button templates are not overridden in the uiSchema\n    const { ButtonTemplates: { AddButton }, } = registry.templates;\n    return (_jsxs(\"fieldset\", { className: className, id: idSchema.$id, children: [_jsx(ArrayFieldTitleTemplate, { idSchema: idSchema, title: uiOptions.title || title, required: required, schema: schema, uiSchema: uiSchema, registry: registry }), _jsx(ArrayFieldDescriptionTemplate, { idSchema: idSchema, description: uiOptions.description || schema.description, schema: schema, uiSchema: uiSchema, registry: registry }), _jsx(\"div\", { className: 'row array-item-list', children: items &&\n                    items.map(({ key, ...itemProps }) => (_jsx(ArrayFieldItemTemplate, { ...itemProps }, key))) }), canAdd && (_jsx(AddButton, { className: 'array-item-add', onClick: onAddClick, disabled: disabled || readonly, uiSchema: uiSchema, registry: registry }))] }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate, getUiOptions, titleId, } from '@rjsf/utils';\n/** The `ArrayFieldTitleTemplate` component renders a `TitleFieldTemplate` with an `id` derived from\n * the `idSchema`.\n *\n * @param props - The `ArrayFieldTitleProps` for the component\n */\nexport default function ArrayFieldTitleTemplate(props) {\n    const { idSchema, title, schema, uiSchema, required, registry } = props;\n    const options = getUiOptions(uiSchema, registry.globalUiOptions);\n    const { label: displayLabel = true } = options;\n    if (!title || !displayLabel) {\n        return null;\n    }\n    const TitleFieldTemplate = getTemplate('TitleFieldTemplate', registry, options);\n    return (_jsx(TitleFieldTemplate, { id: titleId(idSchema), title: title, required: required, schema: schema, uiSchema: uiSchema, registry: registry }));\n}\n", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, examplesId, getInputProps, } from '@rjsf/utils';\n/** The `BaseInputTemplate` is the template to use to render the basic `<input>` component for the `core` theme.\n * It is used as the template for rendering many of the <input> based widgets that differ by `type` and callbacks only.\n * It can be customized/overridden for other themes or individual implementations as needed.\n *\n * @param props - The `WidgetProps` for this template\n */\nexport default function BaseInputTemplate(props) {\n    const { id, name, // remove this from ...rest\n    value, readonly, disabled, autofocus, onBlur, onFocus, onChange, onChangeOverride, options, schema, uiSchema, formContext, registry, rawErrors, type, hideLabel, // remove this from ...rest\n    hideError, // remove this from ...rest\n    ...rest } = props;\n    // Note: since React 15.2.0 we can't forward unknown element attributes, so we\n    // exclude the \"options\" and \"schema\" ones here.\n    if (!id) {\n        console.log('No id for', props);\n        throw new Error(`no id for props ${JSON.stringify(props)}`);\n    }\n    const inputProps = {\n        ...rest,\n        ...getInputProps(schema, type, options),\n    };\n    let inputValue;\n    if (inputProps.type === 'number' || inputProps.type === 'integer') {\n        inputValue = value || value === 0 ? value : '';\n    }\n    else {\n        inputValue = value == null ? '' : value;\n    }\n    const _onChange = useCallback(({ target: { value } }) => onChange(value === '' ? options.emptyValue : value), [onChange, options]);\n    const _onBlur = useCallback(({ target }) => onBlur(id, target && target.value), [onBlur, id]);\n    const _onFocus = useCallback(({ target }) => onFocus(id, target && target.value), [onFocus, id]);\n    return (_jsxs(_Fragment, { children: [_jsx(\"input\", { id: id, name: id, className: 'form-control', readOnly: readonly, disabled: disabled, autoFocus: autofocus, value: inputValue, ...inputProps, list: schema.examples ? examplesId(id) : undefined, onChange: onChangeOverride || _onChange, onBlur: _onBlur, onFocus: _onFocus, \"aria-describedby\": ariaDescribedByIds(id, !!schema.examples) }), Array.isArray(schema.examples) && (_jsx(\"datalist\", { id: examplesId(id), children: schema.examples\n                    .concat(schema.default && !schema.examples.includes(schema.default) ? [schema.default] : [])\n                    .map((example) => {\n                    return _jsx(\"option\", { value: example }, example);\n                }) }, `datalist_${id}`))] }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getSubmitButtonOptions } from '@rjsf/utils';\n/** The `SubmitButton` renders a button that represent the `Submit` action on a form\n */\nexport default function SubmitButton({ uiSchema }) {\n    const { submitText, norender, props: submitButtonProps = {} } = getSubmitButtonOptions(uiSchema);\n    if (norender) {\n        return null;\n    }\n    return (_jsx(\"div\", { children: _jsx(\"button\", { type: 'submit', ...submitButtonProps, className: `btn btn-info ${submitButtonProps.className || ''}`, children: submitText }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { TranslatableString } from '@rjsf/utils';\nimport IconButton from './IconButton.js';\n/** The `AddButton` renders a button that represent the `Add` action on a form\n */\nexport default function AddButton({ className, onClick, disabled, registry, }) {\n    const { translateString } = registry;\n    return (_jsx(\"div\", { className: 'row', children: _jsx(\"p\", { className: `col-xs-3 col-xs-offset-9 text-right ${className}`, children: _jsx(IconButton, { iconType: 'info', icon: 'plus', className: 'btn-add col-xs-12', title: translateString(TranslatableString.AddButton), onClick: onClick, disabled: disabled, registry: registry }) }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { TranslatableString } from '@rjsf/utils';\nexport default function IconButton(props) {\n    const { iconType = 'default', icon, className, uiSchema, registry, ...otherProps } = props;\n    return (_jsx(\"button\", { type: 'button', className: `btn btn-${iconType} ${className}`, ...otherProps, children: _jsx(\"i\", { className: `glyphicon glyphicon-${icon}` }) }));\n}\nexport function CopyButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.CopyButton), className: 'array-item-copy', ...props, icon: 'copy' }));\n}\nexport function MoveDownButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.MoveDownButton), className: 'array-item-move-down', ...props, icon: 'arrow-down' }));\n}\nexport function MoveUpButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.MoveUpButton), className: 'array-item-move-up', ...props, icon: 'arrow-up' }));\n}\nexport function RemoveButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.RemoveButton), className: 'array-item-remove', ...props, iconType: 'danger', icon: 'remove' }));\n}\n", "import SubmitButton from './SubmitButton.js';\nimport AddButton from './AddButton.js';\nimport { CopyButton, MoveDownButton, MoveUpButton, RemoveButton } from './IconButton.js';\nfunction buttonTemplates() {\n    return {\n        SubmitButton,\n        AddButton,\n        CopyButton,\n        MoveDownButton,\n        MoveUpButton,\n        RemoveButton,\n    };\n}\nexport default buttonTemplates;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/** The `DescriptionField` is the template to use to render the description of a field\n *\n * @param props - The `DescriptionFieldProps` for this component\n */\nexport default function DescriptionField(props) {\n    const { id, description } = props;\n    if (!description) {\n        return null;\n    }\n    if (typeof description === 'string') {\n        return (_jsx(\"p\", { id: id, className: 'field-description', children: description }));\n    }\n    else {\n        return (_jsx(\"div\", { id: id, className: 'field-description', children: description }));\n    }\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TranslatableString, } from '@rjsf/utils';\n/** The `ErrorList` component is the template that renders the all the errors associated with the fields in the `Form`\n *\n * @param props - The `ErrorListProps` for this component\n */\nexport default function ErrorList({ errors, registry, }) {\n    const { translateString } = registry;\n    return (_jsxs(\"div\", { className: 'panel panel-danger errors', children: [_jsx(\"div\", { className: 'panel-heading', children: _jsx(\"h3\", { className: 'panel-title', children: translateString(TranslatableString.ErrorsLabel) }) }), _jsx(\"ul\", { className: 'list-group', children: errors.map((error, i) => {\n                    return (_jsx(\"li\", { className: 'list-group-item text-danger', children: error.stack }, i));\n                }) })] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { getTemplate, getUiOptions, } from '@rjsf/utils';\nimport Label from './Label.js';\n/** The `FieldTemplate` component is the template used by `<PERSON><PERSON><PERSON><PERSON>ield` to render any field. It renders the field\n * content, (label, description, children, errors and help) inside of a `WrapIfAdditional` component.\n *\n * @param props - The `FieldTemplateProps` for this component\n */\nexport default function FieldTemplate(props) {\n    const { id, label, children, errors, help, description, hidden, required, displayLabel, registry, uiSchema } = props;\n    const uiOptions = getUiOptions(uiSchema);\n    const WrapIfAdditionalTemplate = getTemplate('WrapIfAdditionalTemplate', registry, uiOptions);\n    if (hidden) {\n        return _jsx(\"div\", { className: 'hidden', children: children });\n    }\n    return (_jsxs(WrapIfAdditionalTemplate, { ...props, children: [displayLabel && _jsx(Label, { label: label, required: required, id: id }), displayLabel && description ? description : null, children, errors, help] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst REQUIRED_FIELD_SYMBOL = '*';\n/** Renders a label for a field\n *\n * @param props - The `LabelProps` for this component\n */\nexport default function Label(props) {\n    const { label, required, id } = props;\n    if (!label) {\n        return null;\n    }\n    return (_jsxs(\"label\", { className: 'control-label', htmlFor: id, children: [label, required && _jsx(\"span\", { className: 'required', children: REQUIRED_FIELD_SYMBOL })] }));\n}\n", "import FieldTemplate from './FieldTemplate.js';\nexport default FieldTemplate;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { errorId } from '@rjsf/utils';\n/** The `FieldErrorTemplate` component renders the errors local to the particular field\n *\n * @param props - The `FieldErrorProps` for the errors being rendered\n */\nexport default function FieldErrorTemplate(props) {\n    const { errors = [], idSchema } = props;\n    if (errors.length === 0) {\n        return null;\n    }\n    const id = errorId(idSchema);\n    return (_jsx(\"div\", { children: _jsx(\"ul\", { id: id, className: 'error-detail bs-callout bs-callout-info', children: errors\n                .filter((elem) => !!elem)\n                .map((error, index) => {\n                return (_jsx(\"li\", { className: 'text-danger', children: error }, index));\n            }) }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { helpId } from '@rjsf/utils';\n/** The `FieldHelpTemplate` component renders any help desired for a field\n *\n * @param props - The `FieldHelpProps` to be rendered\n */\nexport default function FieldHelpTemplate(props) {\n    const { idSchema, help } = props;\n    if (!help) {\n        return null;\n    }\n    const id = helpId(idSchema);\n    if (typeof help === 'string') {\n        return (_jsx(\"p\", { id: id, className: 'help-block', children: help }));\n    }\n    return (_jsx(\"div\", { id: id, className: 'help-block', children: help }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { canExpand, descriptionId, getTemplate, getUiOptions, titleId, } from '@rjsf/utils';\n/** The `ObjectFieldTemplate` is the template to use to render all the inner properties of an object along with the\n * title and description if available. If the object is expandable, then an `AddButton` is also rendered after all\n * the properties.\n *\n * @param props - The `ObjectFieldTemplateProps` for this component\n */\nexport default function ObjectFieldTemplate(props) {\n    const { description, disabled, formData, idSchema, onAddClick, properties, readonly, registry, required, schema, title, uiSchema, } = props;\n    const options = getUiOptions(uiSchema);\n    const TitleFieldTemplate = getTemplate('TitleFieldTemplate', registry, options);\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options);\n    // Button templates are not overridden in the uiSchema\n    const { ButtonTemplates: { AddButton }, } = registry.templates;\n    return (_jsxs(\"fieldset\", { id: idSchema.$id, children: [title && (_jsx(TitleFieldTemplate, { id: titleId(idSchema), title: title, required: required, schema: schema, uiSchema: uiSchema, registry: registry })), description && (_jsx(DescriptionFieldTemplate, { id: descriptionId(idSchema), description: description, schema: schema, uiSchema: uiSchema, registry: registry })), properties.map((prop) => prop.content), canExpand(schema, uiSchema, formData) && (_jsx(AddButton, { className: 'object-property-expand', onClick: onAddClick(schema), disabled: disabled || readonly, uiSchema: uiSchema, registry: registry }))] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst REQUIRED_FIELD_SYMBOL = '*';\n/** The `TitleField` is the template to use to render the title of a field\n *\n * @param props - The `TitleFieldProps` for this component\n */\nexport default function TitleField(props) {\n    const { id, title, required } = props;\n    return (_jsxs(\"legend\", { id: id, children: [title, required && _jsx(\"span\", { className: 'required', children: REQUIRED_FIELD_SYMBOL })] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TranslatableString } from '@rjsf/utils';\nimport Markdown from 'markdown-to-jsx';\n/** The `UnsupportedField` component is used to render a field in the schema is one that is not supported by\n * react-jsonschema-form.\n *\n * @param props - The `FieldProps` for this template\n */\nfunction UnsupportedField(props) {\n    const { schema, idSchema, reason, registry } = props;\n    const { translateString } = registry;\n    let translateEnum = TranslatableString.UnsupportedField;\n    const translateParams = [];\n    if (idSchema && idSchema.$id) {\n        translateEnum = TranslatableString.UnsupportedFieldWithId;\n        translateParams.push(idSchema.$id);\n    }\n    if (reason) {\n        translateEnum =\n            translateEnum === TranslatableString.UnsupportedField\n                ? TranslatableString.UnsupportedFieldWithReason\n                : TranslatableString.UnsupportedFieldWithIdAndReason;\n        translateParams.push(reason);\n    }\n    return (_jsxs(\"div\", { className: 'unsupported-field', children: [_jsx(\"p\", { children: _jsx(Markdown, { options: { disableParsingRawHTML: true }, children: translateString(translateEnum, translateParams) }) }), schema && _jsx(\"pre\", { children: JSON.stringify(schema, null, 2) })] }));\n}\nexport default UnsupportedField;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { ADDITIONAL_PROPERTY_FLAG, TranslatableString, } from '@rjsf/utils';\nimport Label from './FieldTemplate/Label.js';\n/** The `WrapIfAdditional` component is used by the `FieldTemplate` to rename, or remove properties that are\n * part of an `additionalProperties` part of a schema.\n *\n * @param props - The `WrapIfAdditionalProps` for this component\n */\nexport default function WrapIfAdditionalTemplate(props) {\n    const { id, classNames, style, disabled, label, onKeyChange, onDropPropertyClick, readonly, required, schema, children, uiSchema, registry, } = props;\n    const { templates, translateString } = registry;\n    // Button templates are not overridden in the uiSchema\n    const { RemoveButton } = templates.ButtonTemplates;\n    const keyLabel = translateString(TranslatableString.KeyLabel, [label]);\n    const additional = ADDITIONAL_PROPERTY_FLAG in schema;\n    if (!additional) {\n        return (_jsx(\"div\", { className: classNames, style: style, children: children }));\n    }\n    return (_jsx(\"div\", { className: classNames, style: style, children: _jsxs(\"div\", { className: 'row', children: [_jsx(\"div\", { className: 'col-xs-5 form-additional', children: _jsxs(\"div\", { className: 'form-group', children: [_jsx(Label, { label: keyLabel, required: required, id: `${id}-key` }), _jsx(\"input\", { className: 'form-control', type: 'text', id: `${id}-key`, onBlur: ({ target }) => onKeyChange(target && target.value), defaultValue: label })] }) }), _jsx(\"div\", { className: 'form-additional form-group col-xs-5', children: children }), _jsx(\"div\", { className: 'col-xs-2', children: _jsx(RemoveButton, { className: 'array-item-remove btn-block', style: { border: '0' }, disabled: disabled || readonly, onClick: onDropPropertyClick(label), uiSchema: uiSchema, registry: registry }) })] }) }));\n}\n", "import ArrayFieldDescriptionTemplate from './ArrayFieldDescriptionTemplate.js';\nimport ArrayFieldItemTemplate from './ArrayFieldItemTemplate.js';\nimport ArrayFieldTemplate from './ArrayFieldTemplate.js';\nimport ArrayFieldTitleTemplate from './ArrayFieldTitleTemplate.js';\nimport BaseInputTemplate from './BaseInputTemplate.js';\nimport ButtonTemplates from './ButtonTemplates/index.js';\nimport DescriptionField from './DescriptionField.js';\nimport ErrorList from './ErrorList.js';\nimport FieldTemplate from './FieldTemplate/index.js';\nimport FieldErrorTemplate from './FieldErrorTemplate.js';\nimport FieldHelpTemplate from './FieldHelpTemplate.js';\nimport ObjectFieldTemplate from './ObjectFieldTemplate.js';\nimport TitleField from './TitleField.js';\nimport UnsupportedField from './UnsupportedField.js';\nimport WrapIfAdditionalTemplate from './WrapIfAdditionalTemplate.js';\nfunction templates() {\n    return {\n        ArrayFieldDescriptionTemplate,\n        ArrayFieldItemTemplate,\n        ArrayFieldTemplate,\n        ArrayFieldTitleTemplate,\n        ButtonTemplates: ButtonTemplates(),\n        BaseInputTemplate,\n        DescriptionFieldTemplate: DescriptionField,\n        ErrorListTemplate: ErrorList,\n        FieldTemplate,\n        FieldErrorTemplate,\n        FieldHelpTemplate,\n        ObjectFieldTemplate,\n        TitleFieldTemplate: TitleField,\n        UnsupportedFieldTemplate: UnsupportedField,\n        WrapIfAdditionalTemplate,\n    };\n}\nexport default templates;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, useEffect, useReducer, useState } from 'react';\nimport { ariaDescribedByIds, dateRangeOptions, parseDateString, toDateString, TranslatableString, getDateElementProps, } from '@rjsf/utils';\nfunction readyForChange(state) {\n    return Object.values(state).every((value) => value !== -1);\n}\nfunction DateElement({ type, range, value, select, rootId, name, disabled, readonly, autofocus, registry, onBlur, onFocus, }) {\n    const id = rootId + '_' + type;\n    const { SelectWidget } = registry.widgets;\n    return (_jsx(SelectWidget, { schema: { type: 'integer' }, id: id, name: name, className: 'form-control', options: { enumOptions: dateRangeOptions(range[0], range[1]) }, placeholder: type, value: value, disabled: disabled, readonly: readonly, autofocus: autofocus, onChange: (value) => select(type, value), onBlur: onBlur, onFocus: onFocus, registry: registry, label: '', \"aria-describedby\": ariaDescribedByIds(rootId) }));\n}\n/** The `AltDateWidget` is an alternative widget for rendering date properties.\n * @param props - The `WidgetProps` for this component\n */\nfunction AltDateWidget({ time = false, disabled = false, readonly = false, autofocus = false, options, id, name, registry, onBlur, onFocus, onChange, value, }) {\n    const { translateString } = registry;\n    const [lastValue, setLastValue] = useState(value);\n    const [state, setState] = useReducer((state, action) => {\n        return { ...state, ...action };\n    }, parseDateString(value, time));\n    useEffect(() => {\n        const stateValue = toDateString(state, time);\n        if (readyForChange(state) && stateValue !== value) {\n            // The user changed the date to a new valid data via the comboboxes, so call onChange\n            onChange(stateValue);\n        }\n        else if (lastValue !== value) {\n            // We got a new value in the props\n            setLastValue(value);\n            setState(parseDateString(value, time));\n        }\n    }, [time, value, onChange, state, lastValue]);\n    const handleChange = useCallback((property, value) => {\n        setState({ [property]: value });\n    }, []);\n    const handleSetNow = useCallback((event) => {\n        event.preventDefault();\n        if (disabled || readonly) {\n            return;\n        }\n        const nextState = parseDateString(new Date().toJSON(), time);\n        onChange(toDateString(nextState, time));\n    }, [disabled, readonly, time]);\n    const handleClear = useCallback((event) => {\n        event.preventDefault();\n        if (disabled || readonly) {\n            return;\n        }\n        onChange(undefined);\n    }, [disabled, readonly, onChange]);\n    return (_jsxs(\"ul\", { className: 'list-inline', children: [getDateElementProps(state, time, options.yearsRange, options.format).map((elemProps, i) => (_jsx(\"li\", { className: 'list-inline-item', children: _jsx(DateElement, { rootId: id, name: name, select: handleChange, ...elemProps, disabled: disabled, readonly: readonly, registry: registry, onBlur: onBlur, onFocus: onFocus, autofocus: autofocus && i === 0 }) }, i))), (options.hideNowButton !== 'undefined' ? !options.hideNowButton : true) && (_jsx(\"li\", { className: 'list-inline-item', children: _jsx(\"a\", { href: '#', className: 'btn btn-info btn-now', onClick: handleSetNow, children: translateString(TranslatableString.NowLabel) }) })), (options.hideClearButton !== 'undefined' ? !options.hideClearButton : true) && (_jsx(\"li\", { className: 'list-inline-item', children: _jsx(\"a\", { href: '#', className: 'btn btn-warning btn-clear', onClick: handleClear, children: translateString(TranslatableString.ClearLabel) }) }))] }));\n}\nexport default AltDateWidget;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/** The `AltDateTimeWidget` is an alternative widget for rendering datetime properties.\n *  It uses the AltDateWidget for rendering, with the `time` prop set to true by default.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction AltDateTimeWidget({ time = true, ...props }) {\n    const { AltDateWidget } = props.registry.widgets;\n    return _jsx(AltDateWidget, { time: time, ...props });\n}\nexport default AltDateTimeWidget;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, descriptionId, getTemplate, labelValue, schemaRequiresTrueValue, } from '@rjsf/utils';\n/** The `CheckBoxWidget` is a widget for rendering boolean properties.\n *  It is typically used to represent a boolean.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction CheckboxWidget({ schema, uiSchema, options, id, value, disabled, readonly, label, hideLabel, autofocus = false, onBlur, onFocus, onChange, registry, }) {\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options);\n    // Because an unchecked checkbox will cause html5 validation to fail, only add\n    // the \"required\" attribute if the field value must be \"true\", due to the\n    // \"const\" or \"enum\" keywords\n    const required = schemaRequiresTrueValue(schema);\n    const handleChange = useCallback((event) => onChange(event.target.checked), [onChange]);\n    const handleBlur = useCallback((event) => onBlur(id, event.target.checked), [onBlur, id]);\n    const handleFocus = useCallback((event) => onFocus(id, event.target.checked), [onFocus, id]);\n    const description = options.description ?? schema.description;\n    return (_jsxs(\"div\", { className: `checkbox ${disabled || readonly ? 'disabled' : ''}`, children: [!hideLabel && !!description && (_jsx(DescriptionFieldTemplate, { id: descriptionId(id), description: description, schema: schema, uiSchema: uiSchema, registry: registry })), _jsxs(\"label\", { children: [_jsx(\"input\", { type: 'checkbox', id: id, name: id, checked: typeof value === 'undefined' ? false : value, required: required, disabled: disabled || readonly, autoFocus: autofocus, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, \"aria-describedby\": ariaDescribedByIds(id) }), labelValue(_jsx(\"span\", { children: label }), hideLabel)] })] }));\n}\nexport default CheckboxWidget;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, enumOptionsDeselectValue, enumOptionsIsSelected, enumOptionsSelectValue, enumOptionsValueForIndex, optionId, } from '@rjsf/utils';\n/** The `CheckboxesWidget` is a widget for rendering checkbox groups.\n *  It is typically used to represent an array of enums.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction CheckboxesWidget({ id, disabled, options: { inline = false, enumOptions, enumDisabled, emptyValue }, value, autofocus = false, readonly, onChange, onBlur, onFocus, }) {\n    const checkboxesValues = Array.isArray(value) ? value : [value];\n    const handleBlur = useCallback(({ target }) => onBlur(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onBlur, id]);\n    const handleFocus = useCallback(({ target }) => onFocus(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onFocus, id]);\n    return (_jsx(\"div\", { className: 'checkboxes', id: id, children: Array.isArray(enumOptions) &&\n            enumOptions.map((option, index) => {\n                const checked = enumOptionsIsSelected(option.value, checkboxesValues);\n                const itemDisabled = Array.isArray(enumDisabled) && enumDisabled.indexOf(option.value) !== -1;\n                const disabledCls = disabled || itemDisabled || readonly ? 'disabled' : '';\n                const handleChange = (event) => {\n                    if (event.target.checked) {\n                        onChange(enumOptionsSelectValue(index, checkboxesValues, enumOptions));\n                    }\n                    else {\n                        onChange(enumOptionsDeselectValue(index, checkboxesValues, enumOptions));\n                    }\n                };\n                const checkbox = (_jsxs(\"span\", { children: [_jsx(\"input\", { type: 'checkbox', id: optionId(id, index), name: id, checked: checked, value: String(index), disabled: disabled || itemDisabled || readonly, autoFocus: autofocus && index === 0, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, \"aria-describedby\": ariaDescribedByIds(id) }), _jsx(\"span\", { children: option.label })] }));\n                return inline ? (_jsx(\"label\", { className: `checkbox-inline ${disabledCls}`, children: checkbox }, index)) : (_jsx(\"div\", { className: `checkbox ${disabledCls}`, children: _jsx(\"label\", { children: checkbox }) }, index));\n            }) }));\n}\nexport default CheckboxesWidget;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `ColorWidget` component uses the `BaseInputTemplate` changing the type to `color` and disables it when it is\n * either disabled or readonly.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function ColorWidget(props) {\n    const { disabled, readonly, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'color', ...props, disabled: disabled || readonly });\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { getTemplate } from '@rjsf/utils';\n/** The `DateWidget` component uses the `BaseInputTemplate` changing the type to `date` and transforms\n * the value to undefined when it is falsy during the `onChange` handling.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function DateWidget(props) {\n    const { onChange, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    const handleChange = useCallback((value) => onChange(value || undefined), [onChange]);\n    return _jsx(BaseInputTemplate, { type: 'date', ...props, onChange: handleChange });\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate, localToUTC, utcToLocal, } from '@rjsf/utils';\n/** The `DateTimeWidget` component uses the `BaseInputTemplate` changing the type to `datetime-local` and transforms\n * the value to/from utc using the appropriate utility functions.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function DateTimeWidget(props) {\n    const { onChange, value, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return (_jsx(BaseInputTemplate, { type: 'datetime-local', ...props, value: utcToLocal(value), onChange: (value) => onChange(localToUTC(value)) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `EmailWidget` component uses the `BaseInputTemplate` changing the type to `email`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function EmailWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'email', ...props });\n}\n", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, useMemo } from 'react';\nimport { dataURItoBlob, getTemplate, TranslatableString, } from '@rjsf/utils';\nimport Markdown from 'markdown-to-jsx';\nfunction addNameToDataURL(dataURL, name) {\n    if (dataURL === null) {\n        return null;\n    }\n    return dataURL.replace(';base64', `;name=${encodeURIComponent(name)};base64`);\n}\nfunction processFile(file) {\n    const { name, size, type } = file;\n    return new Promise((resolve, reject) => {\n        const reader = new window.FileReader();\n        reader.onerror = reject;\n        reader.onload = (event) => {\n            if (typeof event.target?.result === 'string') {\n                resolve({\n                    dataURL: addNameToDataURL(event.target.result, name),\n                    name,\n                    size,\n                    type,\n                });\n            }\n            else {\n                resolve({\n                    dataURL: null,\n                    name,\n                    size,\n                    type,\n                });\n            }\n        };\n        reader.readAsDataURL(file);\n    });\n}\nfunction processFiles(files) {\n    return Promise.all(Array.from(files).map(processFile));\n}\nfunction FileInfoPreview({ fileInfo, registry, }) {\n    const { translateString } = registry;\n    const { dataURL, type, name } = fileInfo;\n    if (!dataURL) {\n        return null;\n    }\n    // If type is JPEG or PNG then show image preview.\n    // Originally, any type of image was supported, but this was changed into a whitelist\n    // since SVGs and animated GIFs are also images, which are generally considered a security risk.\n    if (['image/jpeg', 'image/png'].includes(type)) {\n        return _jsx(\"img\", { src: dataURL, style: { maxWidth: '100%' }, className: 'file-preview' });\n    }\n    // otherwise, let users download file\n    return (_jsxs(_Fragment, { children: [' ', _jsx(\"a\", { download: `preview-${name}`, href: dataURL, className: 'file-download', children: translateString(TranslatableString.PreviewLabel) })] }));\n}\nfunction FilesInfo({ filesInfo, registry, preview, onRemove, options, }) {\n    if (filesInfo.length === 0) {\n        return null;\n    }\n    const { translateString } = registry;\n    const { RemoveButton } = getTemplate('ButtonTemplates', registry, options);\n    return (_jsx(\"ul\", { className: 'file-info', children: filesInfo.map((fileInfo, key) => {\n            const { name, size, type } = fileInfo;\n            const handleRemove = () => onRemove(key);\n            return (_jsxs(\"li\", { children: [_jsx(Markdown, { children: translateString(TranslatableString.FilesInfo, [name, type, String(size)]) }), preview && _jsx(FileInfoPreview, { fileInfo: fileInfo, registry: registry }), _jsx(RemoveButton, { onClick: handleRemove, registry: registry })] }, key));\n        }) }));\n}\nfunction extractFileInfo(dataURLs) {\n    return dataURLs.reduce((acc, dataURL) => {\n        if (!dataURL) {\n            return acc;\n        }\n        try {\n            const { blob, name } = dataURItoBlob(dataURL);\n            return [\n                ...acc,\n                {\n                    dataURL,\n                    name: name,\n                    size: blob.size,\n                    type: blob.type,\n                },\n            ];\n        }\n        catch (e) {\n            // Invalid dataURI, so just ignore it.\n            return acc;\n        }\n    }, []);\n}\n/**\n *  The `FileWidget` is a widget for rendering file upload fields.\n *  It is typically used with a string property with data-url format.\n */\nfunction FileWidget(props) {\n    const { disabled, readonly, required, multiple, onChange, value, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    const handleChange = useCallback((event) => {\n        if (!event.target.files) {\n            return;\n        }\n        // Due to variances in themes, dealing with multiple files for the array case now happens one file at a time.\n        // This is because we don't pass `multiple` into the `BaseInputTemplate` anymore. Instead, we deal with the single\n        // file in each event and concatenate them together ourselves\n        processFiles(event.target.files).then((filesInfoEvent) => {\n            const newValue = filesInfoEvent.map((fileInfo) => fileInfo.dataURL);\n            if (multiple) {\n                onChange(value.concat(newValue));\n            }\n            else {\n                onChange(newValue[0]);\n            }\n        });\n    }, [multiple, value, onChange]);\n    const filesInfo = useMemo(() => extractFileInfo(Array.isArray(value) ? value : [value]), [value]);\n    const rmFile = useCallback((index) => {\n        if (multiple) {\n            const newValue = value.filter((_, i) => i !== index);\n            onChange(newValue);\n        }\n        else {\n            onChange(undefined);\n        }\n    }, [multiple, value, onChange]);\n    return (_jsxs(\"div\", { children: [_jsx(BaseInputTemplate, { ...props, disabled: disabled || readonly, type: 'file', required: value ? false : required, onChangeOverride: handleChange, value: '', accept: options.accept ? String(options.accept) : undefined }), _jsx(FilesInfo, { filesInfo: filesInfo, onRemove: rmFile, registry: registry, preview: options.filePreview, options: options })] }));\n}\nexport default FileWidget;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/** The `HiddenWidget` is a widget for rendering a hidden input field.\n *  It is typically used by setting type to \"hidden\".\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction HiddenWidget({ id, value, }) {\n    return _jsx(\"input\", { type: 'hidden', id: id, name: id, value: typeof value === 'undefined' ? '' : value });\n}\nexport default HiddenWidget;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `PasswordWidget` component uses the `BaseInputTemplate` changing the type to `password`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function PasswordWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'password', ...props });\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, enumOptionsIsSelected, enumOptionsValueForIndex, optionId, } from '@rjsf/utils';\n/** The `RadioWidget` is a widget for rendering a radio group.\n *  It is typically used with a string property constrained with enum options.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction RadioWidget({ options, value, required, disabled, readonly, autofocus = false, onBlur, onFocus, onChange, id, }) {\n    const { enumOptions, enumDisabled, inline, emptyValue } = options;\n    const handleBlur = useCallback(({ target }) => onBlur(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onBlur, id]);\n    const handleFocus = useCallback(({ target }) => onFocus(id, enumOptionsValueForIndex(target && target.value, enumOptions, emptyValue)), [onFocus, id]);\n    return (_jsx(\"div\", { className: 'field-radio-group', id: id, children: Array.isArray(enumOptions) &&\n            enumOptions.map((option, i) => {\n                const checked = enumOptionsIsSelected(option.value, value);\n                const itemDisabled = Array.isArray(enumDisabled) && enumDisabled.indexOf(option.value) !== -1;\n                const disabledCls = disabled || itemDisabled || readonly ? 'disabled' : '';\n                const handleChange = () => onChange(option.value);\n                const radio = (_jsxs(\"span\", { children: [_jsx(\"input\", { type: 'radio', id: optionId(id, i), checked: checked, name: id, required: required, value: String(i), disabled: disabled || itemDisabled || readonly, autoFocus: autofocus && i === 0, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, \"aria-describedby\": ariaDescribedByIds(id) }), _jsx(\"span\", { children: option.label })] }));\n                return inline ? (_jsx(\"label\", { className: `radio-inline ${disabledCls}`, children: radio }, i)) : (_jsx(\"div\", { className: `radio ${disabledCls}`, children: _jsx(\"label\", { children: radio }) }, i));\n            }) }));\n}\nexport default RadioWidget;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/** The `RangeWidget` component uses the `BaseInputTemplate` changing the type to `range` and wrapping the result\n * in a div, with the value along side it.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function RangeWidget(props) {\n    const { value, registry: { templates: { BaseInputTemplate }, }, } = props;\n    return (_jsxs(\"div\", { className: 'field-range-wrapper', children: [_jsx(BaseInputTemplate, { type: 'range', ...props }), _jsx(\"span\", { className: 'range-view', children: value })] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, enumOptionsIndexForValue, enumOptionsValueForIndex, } from '@rjsf/utils';\nfunction getValue(event, multiple) {\n    if (multiple) {\n        return Array.from(event.target.options)\n            .slice()\n            .filter((o) => o.selected)\n            .map((o) => o.value);\n    }\n    return event.target.value;\n}\n/** The `SelectWidget` is a widget for rendering dropdowns.\n *  It is typically used with string properties constrained with enum options.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction SelectWidget({ schema, id, options, value, required, disabled, readonly, multiple = false, autofocus = false, onChange, onBlur, onFocus, placeholder, }) {\n    const { enumOptions, enumDisabled, emptyValue: optEmptyVal } = options;\n    const emptyValue = multiple ? [] : '';\n    const handleFocus = useCallback((event) => {\n        const newValue = getValue(event, multiple);\n        return onFocus(id, enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));\n    }, [onFocus, id, schema, multiple, enumOptions, optEmptyVal]);\n    const handleBlur = useCallback((event) => {\n        const newValue = getValue(event, multiple);\n        return onBlur(id, enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));\n    }, [onBlur, id, schema, multiple, enumOptions, optEmptyVal]);\n    const handleChange = useCallback((event) => {\n        const newValue = getValue(event, multiple);\n        return onChange(enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));\n    }, [onChange, schema, multiple, enumOptions, optEmptyVal]);\n    const selectedIndexes = enumOptionsIndexForValue(value, enumOptions, multiple);\n    const showPlaceholderOption = !multiple && schema.default === undefined;\n    return (_jsxs(\"select\", { id: id, name: id, multiple: multiple, className: 'form-control', value: typeof selectedIndexes === 'undefined' ? emptyValue : selectedIndexes, required: required, disabled: disabled || readonly, autoFocus: autofocus, onBlur: handleBlur, onFocus: handleFocus, onChange: handleChange, \"aria-describedby\": ariaDescribedByIds(id), children: [showPlaceholderOption && _jsx(\"option\", { value: '', children: placeholder }), Array.isArray(enumOptions) &&\n                enumOptions.map(({ value, label }, i) => {\n                    const disabled = enumDisabled && enumDisabled.indexOf(value) !== -1;\n                    return (_jsx(\"option\", { value: String(i), disabled: disabled, children: label }, i));\n                })] }));\n}\nexport default SelectWidget;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds } from '@rjsf/utils';\n/** The `TextareaWidget` is a widget for rendering input fields as textarea.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction TextareaWidget({ id, options = {}, placeholder, value, required, disabled, readonly, autofocus = false, onChange, onBlur, onFocus, }) {\n    const handleChange = useCallback(({ target: { value } }) => onChange(value === '' ? options.emptyValue : value), [onChange, options.emptyValue]);\n    const handleBlur = useCallback(({ target }) => onBlur(id, target && target.value), [onBlur, id]);\n    const handleFocus = useCallback(({ target }) => onFocus(id, target && target.value), [id, onFocus]);\n    return (_jsx(\"textarea\", { id: id, name: id, className: 'form-control', value: value ? value : '', placeholder: placeholder, required: required, disabled: disabled, readOnly: readonly, autoFocus: autofocus, rows: options.rows, onBlur: handleBlur, onFocus: handleFocus, onChange: handleChange, \"aria-describedby\": ariaDescribedByIds(id) }));\n}\nTextareaWidget.defaultProps = {\n    autofocus: false,\n    options: {},\n};\nexport default TextareaWidget;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `TextWidget` component uses the `BaseInputTemplate`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function TextWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { ...props });\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { getTemplate } from '@rjsf/utils';\n/** The `TimeWidget` component uses the `BaseInputTemplate` changing the type to `time` and transforms\n * the value to undefined when it is falsy during the `onChange` handling.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function TimeWidget(props) {\n    const { onChange, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    const handleChange = useCallback((value) => onChange(value ? `${value}:00` : undefined), [onChange]);\n    return _jsx(BaseInputTemplate, { type: 'time', ...props, onChange: handleChange });\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `URLWidget` component uses the `BaseInputTemplate` changing the type to `url`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function URLWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'url', ...props });\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `UpDownWidget` component uses the `BaseInputTemplate` changing the type to `number`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function UpDownWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'number', ...props });\n}\n", "import AltDateWidget from './AltDateWidget.js';\nimport AltDateTimeWidget from './AltDateTimeWidget.js';\nimport CheckboxWidget from './CheckboxWidget.js';\nimport CheckboxesWidget from './CheckboxesWidget.js';\nimport ColorWidget from './ColorWidget.js';\nimport DateWidget from './DateWidget.js';\nimport DateTimeWidget from './DateTimeWidget.js';\nimport EmailWidget from './EmailWidget.js';\nimport FileWidget from './FileWidget.js';\nimport HiddenWidget from './HiddenWidget.js';\nimport PasswordWidget from './PasswordWidget.js';\nimport RadioWidget from './RadioWidget.js';\nimport RangeWidget from './RangeWidget.js';\nimport SelectWidget from './SelectWidget.js';\nimport TextareaWidget from './TextareaWidget.js';\nimport TextWidget from './TextWidget.js';\nimport TimeWidget from './TimeWidget.js';\nimport URLWidget from './URLWidget.js';\nimport UpDownWidget from './UpDownWidget.js';\nfunction widgets() {\n    return {\n        AltDateWidget,\n        AltDateTimeWidget,\n        CheckboxWidget,\n        CheckboxesWidget,\n        ColorWidget,\n        DateWidget,\n        DateTimeWidget,\n        EmailWidget,\n        FileWidget,\n        HiddenWidget,\n        PasswordWidget,\n        RadioWidget,\n        RangeWidget,\n        SelectWidget,\n        TextWidget,\n        TextareaWidget,\n        TimeWidget,\n        UpDownWidget,\n        URLWidget,\n    };\n}\nexport default widgets;\n", "import { englishStringTranslator } from '@rjsf/utils';\nimport fields from './components/fields/index.js';\nimport templates from './components/templates/index.js';\nimport widgets from './components/widgets/index.js';\n/** The default registry consists of all the fields, templates and widgets provided in the core implementation,\n * plus an empty `rootSchema` and `formContext. We omit schemaUtils here because it cannot be defaulted without a\n * rootSchema and validator. It will be added into the computed registry later in the Form.\n */\nexport default function getDefaultRegistry() {\n    return {\n        fields: fields(),\n        templates: templates(),\n        widgets: widgets(),\n        rootSchema: {},\n        formContext: {},\n        translateString: englishStringTranslator,\n    };\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { forwardRef } from 'react';\nimport Form from './components/Form.js';\n/** A Higher-Order component that creates a wrapper around a `Form` with the overrides from the `WithThemeProps` */\nexport default function withTheme(themeProps) {\n    return forwardRef(({ fields, widgets, templates, ...directProps }, ref) => {\n        fields = { ...themeProps?.fields, ...fields };\n        widgets = { ...themeProps?.widgets, ...widgets };\n        templates = {\n            ...themeProps?.templates,\n            ...templates,\n            ButtonTemplates: {\n                ...themeProps?.templates?.ButtonTemplates,\n                ...templates?.ButtonTemplates,\n            },\n        };\n        return (_jsx(Form, { ...themeProps, ...directProps, fields: fields, widgets: widgets, templates: templates, ref: ref }));\n    });\n}\n", "import Form from './components/Form.js';\nimport withTheme from './withTheme.js';\nimport getDefaultRegistry from './getDefaultRegistry.js';\nexport { withTheme, getDefaultRegistry };\nexport default Form;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,uBAA2C;AAC3C,IAAAC,iBAAqC;;;ACWrC,SAAS,SAAS,QAAQ,OAAO;AAC/B,SAAO,mBAAW,QAAQ,OAAO,SAAS,OAAO,MAAM;AACrD,WAAO,cAAM,QAAQ,IAAI;AAAA,EAC3B,CAAC;AACH;AAEA,IAAO,mBAAQ;;;ACEf,IAAI,OAAO,iBAAS,SAAS,QAAQ,OAAO;AAC1C,SAAO,UAAU,OAAO,CAAC,IAAI,iBAAS,QAAQ,KAAK;AACrD,CAAC;AAED,IAAO,eAAQ;;;ACxBf,yBAA4B;AAC5B,mBAA0B;;;ACkB1B,IAAI,SAAS,CAAC,OAAO,OACnB,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,SAAS;AAChE,UAAQ;AACR,MAAI,OAAO,IAAI;AACb,UAAM,KAAK,SAAS,EAAE;AAAA,EACxB,WAAW,OAAO,IAAI;AACpB,WAAO,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY;AAAA,EAC7C,WAAW,OAAO,IAAI;AACpB,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AACA,SAAO;AACT,GAAG,EAAE;;;ADvBP,SAAS,gBAAgB;AACrB,SAAO,OAAO;AAClB;AAMA,SAAS,sBAAsB,UAAU;AACrC,SAAO,CAAC,MAAM,QAAQ,QAAQ,IACxB,CAAC,IACD,SAAS,IAAI,CAAC,SAAS;AACrB,WAAO;AAAA,MACH,KAAK,cAAc;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ,CAAC;AACT;AAMA,SAAS,qBAAqB,eAAe;AACzC,MAAI,MAAM,QAAQ,aAAa,GAAG;AAC9B,WAAO,cAAc,IAAI,CAAC,cAAc,UAAU,IAAI;AAAA,EAC1D;AACA,SAAO,CAAC;AACZ;AAIA,IAAM,aAAN,cAAyB,uBAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,YAAY,OAAO;AACf,UAAM,KAAK;AAmFf;AAAA;AAAA;AAAA,8CAAqB,MAAM;AACvB,YAAM,EAAE,QAAQ,SAAS,IAAI,KAAK;AAClC,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,aAAa,OAAO;AACxB,UAAI,aAAa,MAAM,KAAK,qBAAqB,MAAM,GAAG;AACtD,qBAAa,OAAO;AAAA,MACxB;AAEA,aAAO,YAAY,oBAAoB,UAAU;AAAA,IACrD;AAkDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCAAa,CAAC,UAAU;AACpB,WAAK,gBAAgB,KAAK;AAAA,IAC9B;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAAkB,CAAC,UAAU;AACzB,aAAO,CAAC,UAAU;AACd,aAAK,gBAAgB,OAAO,KAAK;AAAA,MACrC;AAAA,IACJ;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4CAAmB,CAAC,UAAU;AAC1B,aAAO,CAAC,UAAU;AACd,YAAI,OAAO;AACP,gBAAM,eAAe;AAAA,QACzB;AACA,cAAM,EAAE,UAAU,YAAY,IAAI,KAAK;AACvC,cAAM,EAAE,cAAc,IAAI,KAAK;AAE/B,YAAI;AACJ,YAAI,aAAa;AACb,2BAAiB,CAAC;AAClB,qBAAW,OAAO,aAAa;AAC3B,kBAAMC,KAAI,SAAS,GAAG;AACtB,gBAAIA,MAAK,OAAO;AACZ,0BAAI,gBAAgB,CAACA,EAAC,GAAG,YAAY,GAAG,CAAC;AAAA,YAC7C,WACSA,KAAI,OAAO;AAChB,0BAAI,gBAAgB,CAACA,KAAI,CAAC,GAAG,YAAY,GAAG,CAAC;AAAA,YACjD;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,sBAAsB;AAAA,UACxB,KAAK,cAAc;AAAA,UACnB,MAAM,kBAAU,cAAc,KAAK,EAAE,IAAI;AAAA,QAC7C;AACA,cAAM,mBAAmB,CAAC,GAAG,aAAa;AAC1C,YAAI,UAAU,QAAW;AACrB,2BAAiB,OAAO,QAAQ,GAAG,GAAG,mBAAmB;AAAA,QAC7D,OACK;AACD,2BAAiB,KAAK,mBAAmB;AAAA,QAC7C;AACA,aAAK,SAAS;AAAA,UACV,eAAe;AAAA,UACf,sBAAsB;AAAA,QAC1B,GAAG,MAAM,SAAS,qBAAqB,gBAAgB,GAAG,cAAc,CAAC;AAAA,MAC7E;AAAA,IACJ;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4CAAmB,CAAC,UAAU;AAC1B,aAAO,CAAC,UAAU;AACd,YAAI,OAAO;AACP,gBAAM,eAAe;AAAA,QACzB;AACA,cAAM,EAAE,UAAU,YAAY,IAAI,KAAK;AACvC,cAAM,EAAE,cAAc,IAAI,KAAK;AAE/B,YAAI;AACJ,YAAI,aAAa;AACb,2BAAiB,CAAC;AAClB,qBAAW,OAAO,aAAa;AAC3B,kBAAMA,KAAI,SAAS,GAAG;AACtB,gBAAIA,KAAI,OAAO;AACX,0BAAI,gBAAgB,CAACA,EAAC,GAAG,YAAY,GAAG,CAAC;AAAA,YAC7C,WACSA,KAAI,OAAO;AAChB,0BAAI,gBAAgB,CAACA,KAAI,CAAC,GAAG,YAAY,GAAG,CAAC;AAAA,YACjD;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,mBAAmB,cAAc,OAAO,CAACC,IAAGD,OAAMA,OAAM,KAAK;AACnE,aAAK,SAAS;AAAA,UACV,eAAe;AAAA,UACf,sBAAsB;AAAA,QAC1B,GAAG,MAAM,SAAS,qBAAqB,gBAAgB,GAAG,cAAc,CAAC;AAAA,MAC7E;AAAA,IACJ;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,OAAO,aAAa;AAClC,aAAO,CAAC,UAAU;AACd,YAAI,OAAO;AACP,gBAAM,eAAe;AACrB,gBAAM,cAAc,KAAK;AAAA,QAC7B;AACA,cAAM,EAAE,UAAU,YAAY,IAAI,KAAK;AACvC,YAAI;AACJ,YAAI,aAAa;AACb,2BAAiB,CAAC;AAClB,qBAAW,OAAO,aAAa;AAC3B,kBAAMA,KAAI,SAAS,GAAG;AACtB,gBAAIA,MAAK,OAAO;AACZ,0BAAI,gBAAgB,CAAC,QAAQ,GAAG,YAAY,KAAK,CAAC;AAAA,YACtD,WACSA,MAAK,UAAU;AACpB,0BAAI,gBAAgB,CAAC,KAAK,GAAG,YAAY,QAAQ,CAAC;AAAA,YACtD,OACK;AACD,0BAAI,gBAAgB,CAAC,GAAG,GAAG,YAAYA,EAAC,CAAC;AAAA,YAC7C;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,iBAAS,eAAe;AAEpB,gBAAM,oBAAoB,cAAc,MAAM;AAE9C,4BAAkB,OAAO,OAAO,CAAC;AACjC,4BAAkB,OAAO,UAAU,GAAG,cAAc,KAAK,CAAC;AAC1D,iBAAO;AAAA,QACX;AACA,cAAM,mBAAmB,aAAa;AACtC,aAAK,SAAS;AAAA,UACV,eAAe;AAAA,QACnB,GAAG,MAAM,SAAS,qBAAqB,gBAAgB,GAAG,cAAc,CAAC;AAAA,MAC7E;AAAA,IACJ;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA,4CAAmB,CAAC,UAAU;AAC1B,aAAO,CAAC,OAAO,gBAAgB,OAAO;AAClC,cAAM,EAAE,UAAU,UAAU,YAAY,IAAI,KAAK;AACjD,cAAM,YAAY,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC;AACxD,cAAM,cAAc,UAAU,IAAI,CAAC,MAAMA,OAAM;AAG3C,gBAAM,YAAY,OAAO,UAAU,cAAc,OAAO;AACxD,iBAAO,UAAUA,KAAI,YAAY;AAAA,QACrC,CAAC;AACD,iBAAS,aAAa,eAClB,eAAe;AAAA,UACf,GAAG;AAAA,UACH,CAAC,KAAK,GAAG;AAAA,QACb,GAAG,EAAE;AAAA,MACT;AAAA,IACJ;AAEA;AAAA,0CAAiB,CAAC,UAAU;AACxB,YAAM,EAAE,UAAU,SAAS,IAAI,KAAK;AACpC,eAAS,OAAO,QAAW,YAAY,SAAS,GAAG;AAAA,IACvD;AAhTI,UAAM,EAAE,WAAW,CAAC,EAAE,IAAI;AAC1B,UAAM,gBAAgB,sBAAsB,QAAQ;AACpD,SAAK,QAAQ;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,IAC1B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,yBAAyB,WAAW,WAAW;AAElD,QAAI,UAAU,sBAAsB;AAChC,aAAO;AAAA,QACH,sBAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,UAAM,eAAe,MAAM,QAAQ,UAAU,QAAQ,IAAI,UAAU,WAAW,CAAC;AAC/E,UAAM,wBAAwB,UAAU,iBAAiB,CAAC;AAC1D,UAAM,mBAAmB,aAAa,WAAW,sBAAsB,SACjE,sBAAsB,IAAI,CAAC,wBAAwB,UAAU;AAC3D,aAAO;AAAA,QACH,KAAK,uBAAuB;AAAA,QAC5B,MAAM,aAAa,KAAK;AAAA,MAC5B;AAAA,IACJ,CAAC,IACC,sBAAsB,YAAY;AACxC,WAAO;AAAA,MACH,eAAe;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACZ,UAAM,EAAE,QAAQ,SAAS,IAAI,KAAK;AAClC,UAAM,EAAE,gBAAgB,IAAI;AAC5B,WAAO,YAAI,QAAQ,CAAC,WAAW,OAAO,GAAG,YAAI,QAAQ,CAAC,WAAW,aAAa,GAAG,gBAAgB,mBAAmB,cAAc,CAAC,CAAC;AAAA,EACxI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,YAAY;AACvB,QAAI,MAAM,QAAQ,WAAW,IAAI,GAAG;AAGhC,aAAO,CAAC,WAAW,KAAK,SAAS,MAAM;AAAA,IAC3C;AAEA,WAAO,WAAW,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,WAAW;AAClB,UAAM,EAAE,QAAQ,UAAU,SAAS,IAAI,KAAK;AAC5C,QAAI,EAAE,QAAQ,IAAI,aAAa,UAAU,SAAS,eAAe;AACjE,QAAI,YAAY,OAAO;AAGnB,UAAI,OAAO,aAAa,QAAW;AAC/B,kBAAU,UAAU,SAAS,OAAO;AAAA,MACxC,OACK;AACD,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,gBAAgB,OAAO,OAAO;AAC1B,QAAI,OAAO;AACP,YAAM,eAAe;AAAA,IACzB;AACA,UAAM,EAAE,UAAU,YAAY,IAAI,KAAK;AACvC,UAAM,EAAE,cAAc,IAAI,KAAK;AAE/B,QAAI;AACJ,QAAI,aAAa;AACb,uBAAiB,CAAC;AAClB,iBAAW,OAAO,aAAa;AAC3B,cAAMA,KAAI,SAAS,GAAG;AACtB,YAAI,UAAU,UAAaA,KAAI,OAAO;AAClC,sBAAI,gBAAgB,CAACA,EAAC,GAAG,YAAY,GAAG,CAAC;AAAA,QAC7C,WACSA,MAAK,OAAO;AACjB,sBAAI,gBAAgB,CAACA,KAAI,CAAC,GAAG,YAAY,GAAG,CAAC;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,sBAAsB;AAAA,MACxB,KAAK,cAAc;AAAA,MACnB,MAAM,KAAK,mBAAmB;AAAA,IAClC;AACA,UAAM,mBAAmB,CAAC,GAAG,aAAa;AAC1C,QAAI,UAAU,QAAW;AACrB,uBAAiB,OAAO,OAAO,GAAG,mBAAmB;AAAA,IACzD,OACK;AACD,uBAAiB,KAAK,mBAAmB;AAAA,IAC7C;AACA,SAAK,SAAS;AAAA,MACV,eAAe;AAAA,MACf,sBAAsB;AAAA,IAC1B,GAAG,MAAM,SAAS,qBAAqB,gBAAgB,GAAG,cAAc,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA,EA6KA,SAAS;AACL,UAAM,EAAE,QAAQ,UAAU,UAAU,SAAS,IAAI,KAAK;AACtD,UAAM,EAAE,aAAa,gBAAgB,IAAI;AACzC,QAAI,EAAE,aAAa,SAAS;AACxB,YAAM,YAAY,aAAa,QAAQ;AACvC,YAAM,2BAA2B,YAAY,4BAA4B,UAAU,SAAS;AAC5F,iBAAQ,mBAAAE,KAAK,0BAA0B,EAAE,QAAgB,UAAoB,QAAQ,gBAAgB,mBAAmB,YAAY,GAAG,SAAmB,CAAC;AAAA,IAC/J;AACA,QAAI,YAAY,cAAc,MAAM,GAAG;AAEnC,aAAO,KAAK,kBAAkB;AAAA,IAClC;AACA,QAAI,eAAe,QAAQ,GAAG;AAC1B,aAAO,KAAK,mBAAmB;AAAA,IACnC;AACA,QAAI,aAAa,MAAM,GAAG;AACtB,aAAO,KAAK,iBAAiB;AAAA,IACjC;AACA,QAAI,YAAY,aAAa,QAAQ,QAAQ,GAAG;AAC5C,aAAO,KAAK,YAAY;AAAA,IAC5B;AACA,WAAO,KAAK,kBAAkB;AAAA,EAClC;AAAA;AAAA;AAAA,EAGA,oBAAoB;AAChB,UAAM,EAAE,QAAQ,WAAW,CAAC,GAAG,aAAa,UAAU,MAAM,OAAO,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,OAAO,UAAU,QAAQ,SAAS,UAAU,cAAc,KAAK,UAAW,IAAI,KAAK;AACxN,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAM,aAAa,OAAO,SAAS,SAAS;AAC5C,UAAM,EAAE,aAAa,YAAY,IAAI;AACrC,UAAM,YAAY,aAAa,QAAQ;AACvC,UAAM,eAAe,iBAAS,OAAO,KAAK,IAAI,OAAO,QAAQ,CAAC;AAC9D,UAAM,cAAc,YAAY,eAAe,YAAY;AAC3D,UAAM,WAAW,qBAAqB,KAAK,MAAM,aAAa;AAC9D,UAAM,SAAS,KAAK,WAAW,QAAQ;AACvC,UAAM,aAAa;AAAA,MACf;AAAA,MACA,OAAO,cAAc,IAAI,CAAC,WAAW,UAAU;AAC3C,cAAM,EAAE,KAAK,KAAK,IAAI;AAEtB,cAAM,WAAW;AACjB,cAAM,aAAa,YAAY,eAAe,cAAc,QAAQ;AACpE,cAAM,kBAAkB,cAAc,YAAY,KAAK,IAAI;AAC3D,cAAM,eAAe,SAAS,MAAM,cAAc;AAClD,cAAM,eAAe,YAAY,WAAW,YAAY,cAAc,UAAU,UAAU,WAAW;AACrG,eAAO,KAAK,qBAAqB;AAAA,UAC7B;AAAA,UACA;AAAA,UACA,MAAM,QAAQ,GAAG,IAAI,IAAI,KAAK;AAAA,UAC9B,OAAO,aAAa,GAAG,UAAU,IAAI,QAAQ,CAAC,KAAK;AAAA,UACnD;AAAA,UACA,WAAW,QAAQ;AAAA,UACnB,aAAa,QAAQ,SAAS,SAAS;AAAA,UACvC;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV,cAAc,SAAS;AAAA,UACvB,WAAW,aAAa,UAAU;AAAA,UAClC;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,cAAc;AAAA,QAC9B,CAAC;AAAA,MACL,CAAC;AAAA,MACD,WAAW,oCAAoC,YAAY,IAAI;AAAA,MAC/D;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,WAAW,YAAY,sBAAsB,UAAU,SAAS;AACtE,eAAO,mBAAAA,KAAK,UAAU,EAAE,GAAG,WAAW,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA,EAGA,qBAAqB;AACjB,UAAM,EAAE,QAAQ,UAAU,UAAU,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,OAAO,WAAW,aAAa,QAAQ,SAAS,UAAU,QAAQ,CAAC,GAAG,UAAU,WAAW,KAAM,IAAI,KAAK;AAChN,UAAM,EAAE,SAAAC,UAAS,aAAa,iBAAiB,YAAY,IAAI;AAC/D,UAAM,EAAE,QAAQ,OAAO,SAAS,GAAG,QAAQ,IAAI,aAAa,UAAU,eAAe;AACrF,UAAM,SAAS,UAAU,QAAQ,QAAQA,QAAO;AAChD,UAAM,QAAQ,WAAW,OAAO,SAAS;AACzC,UAAM,eAAe,YAAY,gBAAgB,QAAQ,UAAU,eAAe;AAClF,eAAQ,mBAAAD,KAAK,QAAQ,EAAE,IAAI,SAAS,KAAK,MAAY,UAAU,MAAM,UAAU,KAAK,gBAAgB,QAAgB,SAAkB,SAAkB,QAAgB,UAAoB,UAAoB,OAAO,OAAO,UAAoB,UAAoB,WAAsB,UAAoB,OAAc,WAAW,CAAC,cAAc,aAA0B,aAA0B,WAAsB,UAAqB,CAAC;AAAA,EAC5b;AAAA;AAAA;AAAA,EAGA,oBAAoB;AAChB,UAAM,EAAE,QAAQ,UAAU,UAAU,UAAU,QAAQ,CAAC,GAAG,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,OAAO,aAAa,QAAQ,SAAS,UAAU,WAAW,KAAM,IAAI,KAAK;AACrM,UAAM,EAAE,SAAAC,UAAS,aAAa,aAAa,gBAAgB,IAAI;AAC/D,UAAM,cAAc,YAAY,eAAe,OAAO,OAAO,KAAK;AAClE,UAAM,cAAc,YAAY,aAAa,QAAQ;AACrD,UAAM,EAAE,SAAS,UAAU,OAAO,SAAS,GAAG,QAAQ,IAAI,aAAa,UAAU,eAAe;AAChG,UAAM,SAAS,UAAU,QAAQ,QAAQA,QAAO;AAChD,UAAM,QAAQ,WAAW,OAAO,SAAS;AACzC,UAAM,eAAe,YAAY,gBAAgB,QAAQ,UAAU,eAAe;AAClF,eAAQ,mBAAAD,KAAK,QAAQ,EAAE,IAAI,SAAS,KAAK,MAAY,UAAU,MAAM,UAAU,KAAK,gBAAgB,QAAgB,SAAkB,SAAS,EAAE,GAAG,SAAS,YAAY,GAAG,QAAgB,UAAoB,UAAoB,OAAO,OAAO,UAAoB,UAAoB,UAAoB,OAAc,WAAW,CAAC,cAAc,aAA0B,aAA0B,WAAsB,UAAqB,CAAC;AAAA,EAC1b;AAAA;AAAA;AAAA,EAGA,cAAc;AACV,UAAM,EAAE,QAAQ,UAAU,UAAU,MAAM,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,OAAO,QAAQ,SAAS,UAAU,UAAU,QAAQ,CAAC,GAAG,UAAW,IAAI,KAAK;AACxL,UAAM,EAAE,SAAAC,UAAS,aAAa,iBAAiB,YAAY,IAAI;AAC/D,UAAM,EAAE,SAAS,SAAS,OAAO,SAAS,GAAG,QAAQ,IAAI,aAAa,UAAU,eAAe;AAC/F,UAAM,SAAS,UAAU,QAAQ,QAAQA,QAAO;AAChD,UAAM,QAAQ,WAAW,OAAO,SAAS;AACzC,UAAM,eAAe,YAAY,gBAAgB,QAAQ,UAAU,eAAe;AAClF,eAAQ,mBAAAD,KAAK,QAAQ,EAAE,SAAkB,IAAI,SAAS,KAAK,MAAY,UAAU,MAAM,UAAU,KAAK,gBAAgB,QAAgB,SAAkB,QAAgB,UAAoB,OAAO,OAAO,UAAoB,UAAoB,UAAoB,UAAoB,aAA0B,WAAsB,WAAsB,OAAc,WAAW,CAAC,aAAa,CAAC;AAAA,EAC5Y;AAAA;AAAA;AAAA,EAGA,mBAAmB;AACf,UAAM,EAAE,QAAQ,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,UAAU,cAAc,KAAK,UAAU,MAAM,OAAO,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,OAAO,UAAU,QAAQ,SAAS,UAAW,IAAI,KAAK;AACvO,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,QAAI,EAAE,UAAU,QAAQ,CAAC,EAAE,IAAI,KAAK;AACpC,UAAM,aAAa,OAAO,SAAS,SAAS;AAC5C,UAAM,YAAY,aAAa,QAAQ;AACvC,UAAM,EAAE,aAAa,YAAY,IAAI;AACrC,UAAM,eAAe,iBAAS,OAAO,KAAK,IAAI,OAAO,QAAQ,CAAC;AAC9D,UAAM,cAAc,aAAa,IAAI,CAAC,MAAM,UAAU,YAAY,eAAe,MAAM,SAAS,KAAK,CAAC,CAAC;AACvG,UAAM,mBAAmB,iBAAS,OAAO,eAAe,IAClD,YAAY,eAAe,OAAO,iBAAiB,QAAQ,IAC3D;AACN,QAAI,CAAC,SAAS,MAAM,SAAS,YAAY,QAAQ;AAE7C,cAAQ,SAAS,CAAC;AAClB,cAAQ,MAAM,OAAO,IAAI,MAAM,YAAY,SAAS,MAAM,MAAM,CAAC;AAAA,IACrE;AAEA,UAAM,SAAS,KAAK,WAAW,KAAK,KAAK,CAAC,CAAC;AAC3C,UAAM,aAAa;AAAA,MACf;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,cAAc,IAAI,CAAC,WAAW,UAAU;AAC3C,cAAM,EAAE,KAAK,KAAK,IAAI;AAEtB,cAAM,WAAW;AACjB,cAAM,aAAa,SAAS,YAAY;AACxC,cAAM,cAAc,cAAc,iBAAS,OAAO,eAAe,IAC3D,YAAY,eAAe,OAAO,iBAAiB,QAAQ,IAC3D,YAAY,KAAK,MAAM,CAAC;AAC9B,cAAM,eAAe,SAAS,MAAM,cAAc;AAClD,cAAM,eAAe,YAAY,WAAW,YAAY,cAAc,UAAU,UAAU,WAAW;AACrG,cAAM,eAAe,aACf,SAAS,mBAAmB,CAAC,IAC7B,MAAM,QAAQ,SAAS,KAAK,IACxB,SAAS,MAAM,KAAK,IACpB,SAAS,SAAS,CAAC;AAC7B,cAAM,kBAAkB,cAAc,YAAY,KAAK,IAAI;AAC3D,eAAO,KAAK,qBAAqB;AAAA,UAC7B;AAAA,UACA;AAAA,UACA,MAAM,QAAQ,GAAG,IAAI,IAAI,KAAK;AAAA,UAC9B,OAAO,aAAa,GAAG,UAAU,IAAI,QAAQ,CAAC,KAAK;AAAA,UACnD;AAAA,UACA,WAAW;AAAA,UACX,WAAW,SAAS,YAAY,SAAS;AAAA,UACzC,aAAa,cAAc,QAAQ,MAAM,SAAS;AAAA,UAClD;AAAA,UACA,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA,WAAW,aAAa,UAAU;AAAA,UAClC;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,cAAc;AAAA,QAC9B,CAAC;AAAA,MACL,CAAC;AAAA,MACD,YAAY,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,WAAW,YAAY,sBAAsB,UAAU,SAAS;AACtE,eAAO,mBAAAA,KAAK,UAAU,EAAE,GAAG,WAAW,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AACxB,UAAM,EAAE,KAAK,OAAO,MAAM,QAAQ,YAAY,MAAM,WAAW,aAAa,YAAY,UAAU,cAAc,cAAc,iBAAiB,WAAW,QAAQ,SAAS,WAAW,YAAY,MAAO,IAAI;AAC7M,UAAM,EAAE,UAAU,WAAW,UAAU,aAAa,UAAU,UAAU,UAAU,YAAY,IAAI,KAAK;AACvG,UAAM,EAAE,QAAQ,EAAE,kBAAkB,aAAAE,aAAY,GAAG,gBAAiB,IAAI;AACxE,UAAM,kBAAkB,oBAAoBA;AAC5C,UAAM,EAAE,YAAY,MAAM,YAAY,MAAM,WAAW,MAAM,IAAI,aAAa,UAAU,eAAe;AACvG,UAAM,MAAM;AAAA,MACR,QAAQ,aAAa;AAAA,MACrB,UAAU,aAAa;AAAA,MACvB,MAAM,YAAY;AAAA,MAClB,QAAQ,aAAa;AAAA,MACrB,SAAS;AAAA,IACb;AACA,QAAI,UAAU,OAAO,KAAK,GAAG,EAAE,KAAK,CAACC,SAAQ,IAAIA,IAAG,CAAC;AACrD,WAAO;AAAA,MACH,cAAW,mBAAAH,KAAK,iBAAiB,EAAE,MAAY,OAAc,OAAc,QAAQ,YAAY,UAAU,cAAc,UAAU,UAAU,aAA0B,aAAa,iBAAiB,UAAoB,aAA0B,UAAU,cAAc,UAAU,KAAK,eAAe,UAAU,GAAG,UAAU,KAAK,iBAAiB,KAAK,GAAG,QAAgB,SAAkB,UAAoB,UAAoB,UAAoB,WAAsB,WAAsB,UAAqB,CAAC;AAAA,MAC5f,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,SAAS,IAAI;AAAA,MACb,YAAY,IAAI;AAAA,MAChB,WAAW,IAAI;AAAA,MACf,aAAa,IAAI;AAAA,MACjB,WAAW,IAAI;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,MACtB,kBAAkB,KAAK;AAAA,MACvB,kBAAkB,KAAK;AAAA,MACvB,gBAAgB,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,UAAU;AAAA,IACd;AAAA,EACJ;AACJ;AAIA,IAAO,qBAAQ;;;AEplBf,IAAAI,sBAA4B;AAQ5B,SAAS,aAAa,OAAO;AACzB,QAAM,EAAE,QAAQ,MAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,WAAW,WAAW,OAAO,UAAU,SAAS,QAAQ,UAAW,IAAI;AACnK,QAAM,EAAE,OAAO,YAAY,IAAI;AAC/B,QAAM,EAAE,SAAAC,UAAS,aAAa,iBAAiB,gBAAgB,IAAI;AACnE,QAAM;AAAA,IAAE,SAAS;AAAA,IAAY,OAAO;AAAA;AAAA,IAEpC,OAAO,eAAe;AAAA,IAAM,GAAG;AAAA,EAAQ,IAAI,aAAa,UAAU,eAAe;AACjF,QAAM,SAAS,UAAU,QAAQ,QAAQA,QAAO;AAChD,QAAM,MAAM,gBAAgB,mBAAmB,QAAQ;AACvD,QAAM,KAAK,gBAAgB,mBAAmB,OAAO;AACrD,MAAI;AACJ,QAAM,QAAQ,WAAW,eAAe,SAAS;AACjD,MAAI,MAAM,QAAQ,OAAO,KAAK,GAAG;AAC7B,kBAAc,YAAY;AAAA,MACtB,OAAO,OAAO,MACT,IAAI,CAAC,WAAW;AACjB,YAAI,iBAAS,MAAM,GAAG;AAClB,iBAAO;AAAA,YACH,GAAG;AAAA,YACH,OAAO,OAAO,UAAU,OAAO,UAAU,OAAO,MAAM;AAAA,UAC1D;AAAA,QACJ;AACA,eAAO;AAAA,MACX,CAAC,EACI,OAAO,CAACC,OAAMA,EAAC;AAAA;AAAA,IACxB,GAAG,QAAQ;AAAA,EACf,OACK;AAED,UAAM,sBAAsB;AAC5B,UAAM,QAAQ,OAAO,QAAQ,CAAC,MAAM,KAAK;AACzC,QAAI,CAAC,oBAAoB,aAAa,MAAM,WAAW,KAAK,MAAM,MAAM,CAACC,OAAM,OAAOA,OAAM,SAAS,GAAG;AACpG,oBAAc;AAAA,QACV;AAAA,UACI,OAAO,MAAM,CAAC;AAAA,UACd,OAAO,MAAM,CAAC,IAAI,MAAM;AAAA,QAC5B;AAAA,QACA;AAAA,UACI,OAAO,MAAM,CAAC;AAAA,UACd,OAAO,MAAM,CAAC,IAAI,MAAM;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ,OACK;AACD,oBAAc,YAAY;AAAA,QACtB,MAAM;AAAA;AAAA,QAEN,WAAW,oBAAoB;AAAA,MACnC,GAAG,QAAQ;AAAA,IACf;AAAA,EACJ;AACA,aAAQ,oBAAAC,KAAK,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,YAAY,GAAG,QAAgB,UAAoB,IAAI,SAAS,KAAK,MAAY,UAAoB,SAAkB,QAAgB,OAAc,WAAW,CAAC,cAAc,OAAO,UAAU,UAAoB,UAAoB,UAAoB,WAAsB,UAAoB,aAA0B,WAAsB,UAAqB,CAAC;AAC9Z;AACA,IAAO,uBAAQ;;;AC7Df,IAAAC,sBAA2C;AAC3C,IAAAC,gBAA0B;AAU1B,IAAM,aAAN,cAAyB,wBAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,YAAY,OAAO;AACf,UAAM,KAAK;AAsDf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,WAAW;AACzB,YAAM,EAAE,gBAAgB,iBAAiB,IAAI,KAAK;AAClD,YAAM,EAAE,UAAU,UAAU,SAAS,IAAI,KAAK;AAC9C,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,WAAW,SAAY,SAAS,QAAQ,EAAE,IAAI;AAChE,UAAI,cAAc,gBAAgB;AAC9B;AAAA,MACJ;AACA,YAAM,YAAY,aAAa,IAAI,iBAAiB,SAAS,IAAI;AACjE,YAAM,YAAY,kBAAkB,IAAI,iBAAiB,cAAc,IAAI;AAC3E,UAAI,cAAc,YAAY,yBAAyB,WAAW,WAAW,QAAQ;AACrF,UAAI,WAAW;AAGX,sBAAc,YAAY,oBAAoB,WAAW,aAAa,uBAAuB;AAAA,MACjG;AACA,WAAK,SAAS,EAAE,gBAAgB,UAAU,GAAG,MAAM;AAC/C,iBAAS,aAAa,QAAW,KAAK,WAAW,CAAC;AAAA,MACtD,CAAC;AAAA,IACL;AAxEI,UAAM,EAAE,UAAU,SAAS,UAAU,EAAE,YAAY,EAAG,IAAI,KAAK;AAE/D,UAAM,mBAAmB,QAAQ,IAAI,CAAC,QAAQ,YAAY,eAAe,KAAK,QAAQ,CAAC;AACvF,SAAK,QAAQ;AAAA,MACT;AAAA,MACA,gBAAgB,KAAK,kBAAkB,GAAG,UAAU,gBAAgB;AAAA,IACxE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,WAAW,WAAW;AACrC,UAAM,EAAE,UAAU,SAAS,SAAS,IAAI,KAAK;AAC7C,UAAM,EAAE,eAAe,IAAI,KAAK;AAChC,QAAI,WAAW,KAAK;AACpB,QAAI,CAAC,WAAW,UAAU,SAAS,OAAO,GAAG;AACzC,YAAM,EAAE,UAAU,EAAE,YAAY,EAAG,IAAI,KAAK;AAE5C,YAAM,mBAAmB,QAAQ,IAAI,CAAC,QAAQ,YAAY,eAAe,KAAK,QAAQ,CAAC;AACvF,iBAAW,EAAE,gBAAgB,iBAAiB;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,UAAU,UAAU,QAAQ,KAAK,SAAS,QAAQ,UAAU,SAAS,KAAK;AACtF,YAAM,EAAE,iBAAiB,IAAI;AAC7B,YAAM,iBAAiB,KAAK,kBAAkB,gBAAgB,UAAU,gBAAgB;AACxF,UAAI,aAAa,mBAAmB,gBAAgB;AAChD,mBAAW,EAAE,gBAAgB,gBAAgB,iBAAiB;AAAA,MAClE;AAAA,IACJ;AACA,QAAI,aAAa,KAAK,OAAO;AACzB,WAAK,SAAS,QAAQ;AAAA,IAC1B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,gBAAgB,UAAU,SAAS;AACjD,UAAM,EAAE,QAAQ,UAAU,EAAE,YAAY,EAAG,IAAI,KAAK;AACpD,UAAM,gBAAgB,gCAAgC,MAAM;AAC5D,UAAM,SAAS,YAAY,yBAAyB,UAAU,SAAS,gBAAgB,aAAa;AACpG,WAAO;AAAA,EACX;AAAA,EA2BA,aAAa;AACT,UAAM,EAAE,UAAU,OAAO,IAAI,KAAK;AAClC,WAAO,GAAG,SAAS,GAAG,GAAG,OAAO,QAAQ,mBAAmB,gBAAgB;AAAA,EAC/E;AAAA;AAAA;AAAA,EAGA,SAAS;AACL,UAAM,EAAE,MAAM,WAAW,OAAO,cAAc,CAAC,GAAG,aAAa,QAAQ,SAAS,UAAU,UAAU,QAAQ,SAAU,IAAI,KAAK;AAC/H,UAAM,EAAE,SAAAC,UAAS,QAAAC,SAAQ,iBAAiB,iBAAiB,YAAY,IAAI;AAC3E,UAAM,EAAE,aAAa,aAAa,IAAIA;AACtC,UAAM,EAAE,gBAAgB,iBAAiB,IAAI,KAAK;AAClD,UAAM,EAAE,SAAS,UAAU,aAAa,WAAW,cAAc,QAAQ,OAAO,OAAO,GAAG,UAAU,IAAI,aAAa,UAAU,eAAe;AAC9I,UAAM,SAAS,UAAU,EAAE,MAAM,SAAS,GAAG,QAAQD,QAAO;AAC5D,UAAM,YAAY,YAAI,aAAa,YAAY,CAAC,CAAC;AACjD,UAAM,mBAAmB,aAAK,aAAa,CAAC,UAAU,CAAC;AACvD,UAAM,eAAe,YAAY,gBAAgB,QAAQ,UAAU,eAAe;AAClF,UAAM,SAAS,kBAAkB,IAAI,iBAAiB,cAAc,KAAK,OAAO;AAChF,QAAI;AACJ,QAAI,QAAQ;AAER,YAAM,EAAE,SAAS,IAAI;AAErB,qBAAe,WAAW,aAAa,EAAE,SAAS,GAAG,MAAM,IAAI;AAAA,IACnE;AAEA,QAAI,kBAAkB,CAAC;AACvB,QAAI,cAAc,UAAU,YAAY,cAAc,UAAU;AAC5D,UAAI,MAAM,QAAQ,SAAS,UAAU,CAAC,GAAG;AACrC,0BAAkB,SAAS,UAAU;AAAA,MACzC,OACK;AACD,gBAAQ,KAAK,uCAAuC,SAAS,IAAI,GAAG;AAAA,MACxE;AAAA,IACJ,WACS,cAAc,UAAU,YAAY,cAAc,UAAU;AACjE,UAAI,MAAM,QAAQ,SAAS,UAAU,CAAC,GAAG;AACrC,0BAAkB,SAAS,UAAU;AAAA,MACzC,OACK;AACD,gBAAQ,KAAK,uCAAuC,SAAS,IAAI,GAAG;AAAA,MACxE;AAAA,IACJ;AAEA,QAAI,iBAAiB;AACrB,QAAI,kBAAkB,KAAK,gBAAgB,SAAS,gBAAgB;AAChE,uBAAiB,gBAAgB,cAAc;AAAA,IACnD;AACA,UAAM,gBAAgB,QAChB,mBAAmB,oBACnB,mBAAmB;AACzB,UAAM,kBAAkB,QAAQ,CAAC,KAAK,IAAI,CAAC;AAC3C,UAAM,cAAc,iBAAiB,IAAI,CAAC,KAAK,UAAU;AAErD,YAAM,EAAE,OAAO,UAAU,IAAI,MAAM,IAAI,aAAa,gBAAgB,KAAK,CAAC;AAC1E,aAAO;AAAA,QACH,OAAO,WAAW,gBAAgB,eAAe,gBAAgB,OAAO,OAAO,QAAQ,CAAC,CAAC,CAAC;AAAA,QAC1F,OAAO;AAAA,MACX;AAAA,IACJ,CAAC;AACD,eAAQ,oBAAAE,MAAM,OAAO,EAAE,WAAW,kCAAkC,UAAU,KAAC,oBAAAC,KAAK,OAAO,EAAE,WAAW,cAAc,cAAU,oBAAAA,KAAK,QAAQ,EAAE,IAAI,KAAK,WAAW,GAAG,MAAM,GAAG,IAAI,GAAG,OAAO,QAAQ,mBAAmB,gBAAgB,IAAI,QAAQ,EAAE,MAAM,UAAU,SAAS,EAAE,GAAG,UAAU,KAAK,gBAAgB,QAAgB,SAAkB,UAAU,YAAY,gBAAQ,WAAW,GAAG,UAAU,OAAO,WAAsB,aAAa,kBAAkB,OAAO,kBAAkB,IAAI,iBAAiB,QAAW,SAAS,EAAE,aAAa,GAAG,UAAU,GAAG,UAAoB,aAA0B,aAA0B,cAA4B,WAAsB,OAAO,SAAS,MAAM,WAAW,CAAC,cAAc,SAAmB,CAAC,EAAE,CAAC,GAAG,oBAAgB,oBAAAA,KAAK,cAAc,EAAE,GAAG,KAAK,OAAO,QAAQ,cAAc,UAAU,eAAe,CAAC,CAAC,EAAE,CAAC;AAAA,EACx1B;AACJ;AACA,IAAO,2BAAQ;;;ACzJf,IAAAC,sBAA4B;AAC5B,IAAAC,gBAAsC;AAMtC,IAAM,gCAAgC;AAKtC,IAAM,sBAAsB;AAkB5B,SAAS,YAAY,OAAO;AACxB,QAAM,EAAE,UAAU,UAAU,UAAU,OAAO,aAAa,IAAI;AAC9D,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,YAAY;AACvD,QAAM,EAAE,aAAAC,aAAY,IAAI,SAAS;AACjC,MAAI,QAAQ;AAKZ,QAAM,mBAAe,2BAAY,CAACC,QAAO,aAAa,OAAO;AAEzD,iBAAaA,MAAK;AAGlB,QAAI,GAAGA,MAAK,GAAG,OAAO,CAAC,MAAM,KAAK;AAC9B,MAAAA,SAAQ,IAAIA,MAAK;AAAA,IACrB;AAIA,UAAM,YAAY,OAAOA,WAAU,YAAYA,OAAM,MAAM,6BAA6B,IAClF,SAASA,OAAM,QAAQ,qBAAqB,EAAE,CAAC,IAC/C,SAASA,MAAK;AACpB,aAAS,WAAW,aAAa,EAAE;AAAA,EACvC,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,OAAO,cAAc,YAAY,OAAO,UAAU,UAAU;AAI5D,UAAMC,MAAK,IAAI,OAAO,KAAK,OAAO,KAAK,EAAE,QAAQ,KAAK,KAAK,CAAC,WAAW;AAGvE,QAAI,UAAU,MAAMA,GAAE,GAAG;AACrB,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,aAAO,oBAAAC,KAAKH,cAAa,EAAE,GAAG,OAAO,UAAU,OAAO,UAAU,aAAa,CAAC;AAClF;AACA,IAAO,sBAAQ;;;ACpEf,IAAAI,sBAA2C;AAC3C,IAAAC,gBAA0B;;;;;;;;;;;;;;ICabC,IAAW,EACtBC,YAAY,KACZC,WAAW,KACXC,eAAe,KACfC,WAAW,KACXC,YAAY,KACZC,YAAY,KACZC,UAAU,KACVC,mBAAmB,KACnBC,SAAS,KACTC,SAAS,KACTC,eAAe,MAEfC,WAAW,MACXC,aAAa,MAEbC,iBAAiB,MACjBC,OAAO,MACPC,MAAM,MAENC,6BAA6B,MAE7BC,qBAAqB,MAErBC,oBAAoB,MACpBC,kBAAkB,MAClBC,aAAa,MACbC,WAAW,MACXC,KAAK,MACLC,UAAU,MACVC,SAAS,MACTC,OAAO,MACPC,gBAAgB,MAChBC,MAAM,MACNC,YAAY,MACZC,gBAAgB,MAChBC,aAAa,MACbC,YAAY,MACZC,qBAAqB,MACrBC,eAAe,KAAA;AASjB,IAAWC;AAAAA,CAAX,SAAWA,IAAAA;AAITA,EAAAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA,OAIAA,GAAAA,GAAAA,OAAAA,CAAAA,IAAAA,QAIAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA,OAIAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA,OAIAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA;AACD,EArBUA,MAAAA,IAAAA,CAAAA,EAAAA;AAwBX,IAAMC,IAA4B,CAChC,mBACA,qBACA,gBACA,aACA,YACA,eACA,eACA,WACA,WACA,WACA,mBACA,eACA,eACA,WACA,cACA,eACA,cACA,kBACA,cACA,eACA,YACA,aACA,aACA,WACA,gBACA,eACA,aACA,cACA,aACA,cACA,cACA,YACA,WACA,cACA,UACA,WACA,UACA,YACA,QAAA,EACAC,OACA,CAACC,IAAKC,QACJD,GAAIC,GAAEC,YAAAA,CAAAA,IAAiBD,IAChBD,KAET,EAAEG,OAAO,aAAaC,KAAK,UAAA,CAAA;AA7C7B,IAgDMC,IAAsB,EAC1BC,KAAK,KACLC,MAAM,KACNC,IAAI,KACJC,IAAI,KACJC,MAAM,KACNC,MAAM,IAAA;AAtDR,IAyDMC,IAA+B,CAAC,SAAS,QAAA;AAzD/C,IA2FMC,IACJ;AA5FF,IAgGMC,IAA0B;AAhGhC,IAiGMC,IAAc;AAjGpB,IAkGMC,IAAe;AAlGrB,IAmGMC,IAAmC;AAnGzC,IAoGMC,IAAqB;AApG3B,IAqGMC,IAAe;AArGrB,IAsGMC,IAAmB;AAtGzB,IAuGMC,IACJ;AAxGF,IAyGMC,IAAe;AAzGrB,IA0GMC,IAAgB;AA1GtB,IA2GMC,IAAwB;AA3G9B,IA4GMC,IAAe;AA5GrB,IA8IMC,IAAa;AA9InB,IAgJMC,IAAuB;AAhJ7B,IAiJMC,IAAa;AAjJnB,IAkJMC,IAAiB;AAlJvB,IAmJMC,IAAa;AAnJnB,IAoJMC,IAAY;AApJlB,IAqJMC,IACJ;AAtJF,IAuJMC,IAAmB;AAvJzB,IA+KMC,IACJ;AAhLF,IAkLMC,IAAmB;AAlLzB,IAoLMC,IAAiB;AApLvB,IAyLMC,IAAqB;AAzL3B,IA2LMC,IACJ;AA5LF,IA6LMC,IAAkB;AA7LxB,IA8LMC,IAA2B;AA9LjC,IA+LMC,IAAyB;AA/L/B,IAgMMC,IAAkB;AAhMxB,IAiMMC,IAA8B;AAjMpC,IAkMMC,IAAa;AAlMnB,IAoMMC,IAA0B;AApMhC,IAqMMC,IAAoB;AArM1B,IAsMMC,IAAmB;AAtMzB,IAuMMC,IAA2B;AAvMjC,IAwMMC,IAAQ;AAxMd,IAyMMC,IAAmB;AAzMzB,IA0MMC,IAAqB;AA1M3B,IA2MMC,IAAmB;AA3MzB,IA4MMC,IAAoB;AA5M1B,IAmNMC,IACJ;AApNF,IA0NMC,IAAc,IAAIC,OAAAA,aAAoBF,CAAAA,eAAAA;AA1N5C,IA+NMG,IAAoB,IAAID,OAAAA,UAAiBF,CAAAA,YAAAA;AA/N/C,IAoOMI,IAAgB,IAAIF,OAAAA,QAAeF,CAAAA,KAAAA;AApOzC,IAyOMK,IAAyB,IAAIH,OAAAA,QAAeF,CAAAA,KAAAA;AAzOlD,IA2OMM,KAAiB;AA3OvB,IA4OMC,KAAkB;AA5OxB,IAkPMC,KAAe;AAlPrB,IAoPMC,KAAyB;AApP/B,IAsPMC,KAA0B;AAtPhC,IAwPMC,KAAiB;AAxPvB,IA+PMC,KAAoB;AA/P1B,IAkQMC,KAAsB;AAlQ5B,IAmQMC,KAAwB;AAE9B,SAASC,GAAuBC,IAAAA;AAC9B,SACE,WAZuB,MAatBA,KAAmBH,KAAsBC,MAC1C;AAEJ;AAIA,IAAMG,KAA2BF,GApBN,CAAA;AAoB3B,IACMG,KAA6BH,GApBN,CAAA;AAsB7B,SAASI,GAA4BH,IAAAA;AACnC,SAAA,IAAWd,OACT,OAzBuB,MA0BpBc,KAAmBC,KAA2BC,GAAAA;AAErD;AAEA,IAAME,KAA6BD,GA9BR,CAAA;AA8B3B,IACME,KAA+BF,GA9BR,CAAA;AAgC7B,SAASG,GAAsBN,IAAAA;AAQ7B,SAAA,IAAWd,OACT,OA1CuB,MA2CpBc,KACGC,KACAC,MAHN,yBA1CuB,MAgDpBF,KAAmBH,KAAsBC,MAC1C,sBACF,IAAA;AAEJ;AAEA,IAAMS,KAAsBD,GAtDD,CAAA;AAsD3B,IACME,KAAwBF,GAtDD,CAAA;AA0D7B,SAASG,GAAkBT,IAAAA;AACzB,QAAMU,KA5DmB,MA4DVV,KAAmBH,KAAsBC;AAExD,SAAA,IAAWZ,OACT,WACEwB,KADF,sCAKEA,KACA,SACAA,KAPF,oBAAA;AAaJ;AAEA,IAAMC,KAAiBF,GA9EI,CAAA;AA8E3B,IACMG,KAAmBH,GA9EI,CAAA;AAgF7B,SAASI,GACPC,IACAd,IAAAA;AAIA,QAAMe,KAvFmB,MAuFTf,IACVgB,KAASD,KAAUJ,KAAiBC,IACpCK,KAAcF,KAAUR,KAAsBC,IAC9CU,KAAqBH,KACvBX,KACAC;AAEJ,SAAO,EACLc,OAAOC,GAAY,SAAUC,IAAQC,IAAAA;AASnC,UAAMC,KAAgB3B,GAAkB4B,KAAKF,GAAMG,WAAAA;AAGnD,WAAIF,OAFkBD,GAAMI,QAAAA,CAAUJ,GAAMK,UAAAA,CAAWL,GAAMM,UAKpDZ,GAAOQ,KAFdH,KAASE,GAAc,CAAA,IAAKF,EAAAA,IAAAA;EAMhC,CAAA,GACAQ,OAAAA,GACAC,MAAMC,IAASD,IAAOR,IAAAA;AACpB,UACMU,KAAQjB,KAAAA,CADCgB,GAAQ,CAAA,IAAA,QAEjBE,KAAQF,GAAQ,CAAA,EAGnBG,QAAQzF,GAAa,IAAA,EACrB0E,MAAMF,EAAAA;AAET,QAAIkB,KAAAA;AAgEJ,WAAO,EACLF,OA/DkBA,GAAMG,IAAI,SAAUC,IAAMC,IAAAA;AAE5C,YAAMC,KAAQrB,GAAmBM,KAAKa,EAAAA,EAAM,CAAA,EAAGG,QAIzCC,KAAa,IAAIvD,OAAO,UAAUqD,KAAQ,KAAK,IAAA,GAG/CG,KAAUL,GAEbH,QAAQO,IAAY,EAAA,EAEpBP,QAAQhB,IAAoB,EAAA,GASzByB,KAAaL,OAAML,GAAMO,SAAS,GASlCI,KAAAA,OARiBF,GAAQG,QAAQ,MAAA,KASlBF,MAAcR;AACnCA,MAAAA,KAAwBS;AAKxB,YAAME,KAAiBxB,GAAMK,QACvBoB,KAAezB,GAAMI;AAK3B,UAAIsB;AAJJ1B,MAAAA,GAAMI,OAAAA,MAKFkB,MACFtB,GAAMK,SAAAA,OACNqB,KAAkBC,GAAQP,EAAAA,IAAW,WAErCpB,GAAMK,SAAAA,MACNqB,KAAkBC,GAAQP,EAAAA;AAG5B,YAAMQ,KAASpB,GAAMkB,IAAiB1B,EAAAA;AAMtC,aAHAA,GAAMK,SAASmB,IACfxB,GAAMI,OAAOqB,IAENG;IACT,CAAA,GAIEnC,SAASA,IACTiB,OAAOA,GAAAA;EAEX,GACAmB,QAAMA,CAACC,IAAMC,IAAQ/B,OAIjBR,GAHUsC,GAAKrC,UAAU,OAAO,MAAA,EAI9BuC,KAAKhC,GAAMgC,KACXtB,OAAOoB,GAAKpD,SAAS5G,EAASqB,cAAc2I,GAAKpB,QAAAA,OAAQuB,GAExDH,GAAKnB,MAAMG,IAAI,SAA0BC,IAAMC,IAAAA;AAC9C,WAAOxB,GAAAA,MAAAA,EAAIwC,KAAKhB,GAAAA,GAAIe,GAAOhB,IAAMf,EAAAA,CAAAA;EACnC,CAAA,CAAA,EAAA;AAKV;AAEA,IAGMkC,KAAS,IAAItE,OACjB,4IAAA;AAJF,IAMMuE,KAAU;AANhB,IAQMC,KAA+B,CACnChH,GACAK,GACAC,GACAS,GACAE,GACAD,GACAY,GACAqC,IACAC,EAAAA;AAjBF,IAoBM+C,KAAiB,CAAA,GAClBD,IAjSe,0BAmSlB9F,GACAE,GACAE,CAAAA;AAGF,SAASiF,GAAQW,IAAAA;AACf,MAAIC,KAAMD,GAAIpB;AACd,SAAOqB,KAAM,KAAKD,GAAIC,KAAM,CAAA,KAAM,MAAKA,CAAAA;AACvC,SAAOD,GAAIE,MAAM,GAAGD,EAAAA;AACtB;AAAA,SAqBgBE,GAAQH,IAAAA;AACtB,SAAOA,GACJ1B,QAAQ,qBAAqB,GAAA,EAC7BA,QAAQ,SAAS,GAAA,EACjBA,QAAQ,SAAS,GAAA,EACjBA,QAAQ,eAAe,GAAA,EACvBA,QAAQ,eAAe,GAAA,EACvBA,QAAQ,SAAS,GAAA,EACjBA,QAAQ,mBAAmB,GAAA,EAC3BA,QAAQ,eAAe,GAAA,EACvBA,QAAQ,WAAW,GAAA,EACnBA,QAAQ,iBAAiB,EAAA,EACzBA,QAAQ,OAAO,GAAA,EACftG,YAAAA;AACL;AAEA,SAASoI,GAAuBC,IAAAA;AAC9B,SAAIlF,EAAkBmF,KAAKD,EAAAA,IAClB,UACEpF,EAAmBqF,KAAKD,EAAAA,IAC1B,WACEnF,EAAiBoF,KAAKD,EAAAA,IACxB,SAAA;AAIX;AAEA,SAASE,GACP9C,IACAS,IACAR,IACA8C,IAAAA;AAEA,QAAMC,KAAc/C,GAAMgD;AAE1BhD,EAAAA,GAAMgD,UAAAA;AAEN,MAAIC,KAAwC,CAAC,CAAA,CAAA,GACzCC,KAAM;AAEV,WAASC,KAAAA;AACP,QAAA,CAAKD,GAAK;AAEV,UAAME,KAAOH,GAAMA,GAAM/B,SAAS,CAAA;AAClCkC,IAAAA,GAAKC,KAAKC,MAAMF,IAAM5C,GAAM0C,IAAKlD,EAAAA,CAAAA,GACjCkD,KAAM;EACR;AA4BA,SA1BAnD,GACGwD,KAAAA,EAEAC,MAAM,mBAAA,EACNC,OAAOC,OAAAA,EACPC,QAAQ,CAACC,IAAU5C,IAAG6C,OAAAA;AACG,YAApBD,GAASL,KAAAA,MACXJ,GAAAA,GAEIL,MACQ,MAAN9B,MAAWA,OAAM6C,GAAI3C,SAAS,KAEhC+B,GAAMI,KAAK,CAAA,CAAA,IAOjBH,MAAOU;EAAAA,CAAAA,GAGXT,GAAAA,GAEAnD,GAAMgD,UAAUD,IAETE;AACT;AAoBA,SAASa,GACPrD,IACAD,IACAR,IAAAA;AAMAA,EAAAA,GAAMK,SAAAA;AACN,QAAM0D,KAAQtD,GAAQ,CAAA,IAAqBA,GAAQ,CAAA,EA3B1BG,QAAQtD,GAAkB,EAAA,EAAIkG,MAAM,GAAA,EAE5C1C,IAAI4B,EAAAA,IAyBoC,CAAA,GACnDO,KAAQxC,GAAQ,CAAA,IAvBxB,SACEV,IACAS,IACAR,IAAAA;AAIA,WAFiBD,GAAOwD,KAAAA,EAAOC,MAAM,IAAA,EAErB1C,IAAI,SAAUkD,IAAAA;AAC5B,aAAOnB,GAAcmB,IAASxD,IAAOR,IAAAA,IAAO;IAC9C,CAAA;EACF,EAa6CS,GAAQ,CAAA,GAAID,IAAOR,EAAAA,IAAS,CAAA,GACjEiE,KAASpB,GAAcpC,GAAQ,CAAA,GAAID,IAAOR,IAAAA,CAAAA,CAASiD,GAAM/B,MAAAA;AAG/D,SAFAlB,GAAMK,SAAAA,OAEC4C,GAAM/B,SACT,EACE6C,OAAOA,IACPd,OAAOA,IACPgB,QAAQA,IACRvF,MAAM5G,EAAS0B,MAAAA,IAEjB,EACE0K,UAAUD,IACVvF,MAAM5G,EAASsB,UAAAA;AAEvB;AAEA,SAAS+K,GAAcrC,IAAMsC,IAAAA;AAC3B,SAA+B,QAAxBtC,GAAKiC,MAAMK,EAAAA,IACd,CAAA,IACA,EACEC,WAAWvC,GAAKiC,MAAMK,EAAAA,EAAAA;AAE9B;AA0LA,SAAStE,GAAqDwE,IAAAA;AAG5D,SAFAA,GAAGjE,SAAS,GAELiE;AACT;AAGA,SAASC,GAAYC,IAAAA;AACnB,SAAO1E,GAAY,SAAeC,IAAQC,IAAAA;AACxC,WAAIA,GAAMK,SACDmE,GAAMtE,KAAKH,EAAAA,IAAAA;EAItB,CAAA;AACF;AAGA,SAAS0E,GAAkBD,IAAAA;AACzB,SAAO1E,GAAY,SACjBC,IACAC,IAAAA;AAEA,WAAIA,GAAMK,UAAUL,GAAMM,SACjBkE,GAAMtE,KAAKH,EAAAA,IAAAA;EAItB,CAAA;AACF;AAGA,SAAS2E,GAAWF,IAAAA;AAClB,SAAA,SAAsBzE,IAAgBC,IAAAA;AACpC,WAAIA,GAAMK,UAAUL,GAAMM,SAAAA,OAGjBkE,GAAMtE,KAAKH,EAAAA;EAEtB;AACF;AAGA,SAAS4E,GAAcH,IAAAA;AACrB,SAAO1E,GAAY,SAAeC,IAAAA;AAChC,WAAOyE,GAAMtE,KAAKH,EAAAA;EACpB,CAAA;AACF;AAEA,SAAS6E,GAAe7E,IAAgBC,IAAAA;AACtC,MAAIA,GAAMK,UAAUL,GAAMM,OACxB,QAAA;AAGF,MAAIT,KAAQ;AAEZE,EAAAA,GAAOyD,MAAM,IAAA,EAAMqB,MAAMC,CAAAA,QACvBA,MAAQ,MAAA,CAGJ1C,GAA6B2C,KAAKP,CAAAA,OAASA,GAAM5B,KAAKkC,EAAAA,CAAAA,MAI1DjF,MAASiF,IAAAA,CAAAA,CAEAA,GAAKvB,KAAAA,GAAAA;AAGhB,QAAMyB,KAAWrD,GAAQ9B,EAAAA;AACzB,SAAgB,MAAZmF,KAAAA,OAMG,CAACnF,IAAAA,EAASmF,EAAAA;AACnB;AAAA,SAEgBC,GAAUC,IAAAA;AACxB,MAAA;AAGE,QAFgBC,mBAAmBD,EAAAA,EAAKtE,QAAQ,mBAAmB,EAAA,EAEvDf,MAAM,4CAAA,EAQhB,QAAA;EAcJ,SAZSuF,IAAAA;AAWP,WAAA;EACF;AAEA,SAAOF;AACT;AAEA,SAASG,GAAYC,IAAAA;AACnB,SAAOA,GAAa1E,QAAQvC,IAAgB,IAAA;AAC9C;AAKA,SAASkH,GACP/E,IACA0D,IACAlE,IAAAA;AAEA,QAAMwF,KAAoBxF,GAAMK,UAAAA,OAC1BoF,KAAoBzF,GAAMM,UAAAA;AAChCN,EAAAA,GAAMK,SAAAA,MACNL,GAAMM,SAAAA;AACN,QAAMsB,KAASpB,GAAM0D,IAAUlE,EAAAA;AAG/B,SAFAA,GAAMK,SAASmF,IACfxF,GAAMM,SAASmF,IACR7D;AACT;AAKA,SAAS8D,GACPlF,IACA0D,IACAlE,IAAAA;AAEA,QAAMwF,KAAoBxF,GAAMK,UAAAA,OAC1BoF,KAAoBzF,GAAMM,UAAAA;AAChCN,EAAAA,GAAMK,SAAAA,OACNL,GAAMM,SAAAA;AACN,QAAMsB,KAASpB,GAAM0D,IAAUlE,EAAAA;AAG/B,SAFAA,GAAMK,SAASmF,IACfxF,GAAMM,SAASmF,IACR7D;AACT;AAEA,SAAS+D,GACPnF,IACA0D,IACAlE,IAAAA;AAEA,QAAMwF,KAAoBxF,GAAMK,UAAAA;AAChCL,EAAAA,GAAMK,SAAAA;AACN,QAAMuB,KAASpB,GAAM0D,IAAUlE,EAAAA;AAE/B,SADAA,GAAMK,SAASmF,IACR5D;AACT;AAEA,IAAMgE,KAEDA,CAACnF,IAASD,IAAOR,QACb,EACLkE,UAAUqB,GAAY/E,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,EAAAA;AAI7C,SAAS6F,KAAAA;AACP,SAAO,CAAA;AACT;AAEA,SAASC,KAAAA;AACP,SAAA;AACF;AAwDA,SAASC,MAAMC,IAAAA;AACb,SAAOA,GAAKvC,OAAOC,OAAAA,EAASuC,KAAK,GAAA;AACnC;AAEA,SAASC,GAAIC,IAAaC,IAAcC,IAAAA;AACtC,MAAIC,KAAMH;AACV,QAAMI,KAAQH,GAAK5C,MAAM,GAAA;AAEzB,SAAO+C,GAAMrF,WACXoF,KAAMA,GAAIC,GAAM,CAAA,CAAA,GAAA,WAEZD,MACCC,CAAAA,GAAMC,MAAAA;AAGb,SAAOF,MAAOD;AAChB;AAAA,SAagBI,GACdC,KAAmB,IACnBC,KAAiC,CAAA,GAAA;AAajC,WAASnH,GAEPoH,IACAC,OAIG3C,IAAAA;AAEH,UAAM4C,KAAgBZ,GAAIS,GAAQI,WAAAA,GAAcH,EAAAA,UAAa,CAAA,CAAA;AAE7D,WAAOD,GAAQK,cArCnB,SAAgBJ,IAAaG,IAAAA;AAC3B,YAAME,KAAWf,GAAIa,IAAWH,EAAAA;AAEhC,aAAKK,KAEsB,cAAA,OAAbA,MACS,YAAA,OAAbA,MAAyB,YAAYA,KAC3CA,KACAf,GAAIa,IAAAA,GAAcH,EAAAA,cAAiBA,EAAAA,IALjBA;IAMxB,EA6BaA,IAAKD,GAAQI,SAAAA,GAAUG,EAAAA,CAAAA,GAEzBL,IACAC,IAAAA,EACHK,WAAWpB,GAAAA,QAAGc,KAAAA,SAAAA,GAAOM,WAAWL,GAAcK,SAAAA,KAAAA,OAAclF,CAAAA,GAAAA,GAE3DiC,EAAAA;EAEP;AAEA,WAASkD,GAAQC,IAAAA;AACfA,IAAAA,KAAQA,GAAMzG,QAAQ3E,GAAgB,EAAA;AAEtC,QAAIoE,KAAAA;AAEAsG,IAAAA,GAAQW,cACVjH,KAAAA,OACUsG,GAAQY,eAKlBlH,KAAAA,UAASjD,EAAyBwF,KAAKyE,EAAAA;AAGzC,UAAMxD,KAAM2D,IACVC,IACEpH,KACIgH,KAAAA,GACG1F,GAAQ0F,EAAAA,EAAOzG,QAAQzC,IAAwB,EAAA,CAAA;;GACtD,EACEkC,QAAAA,GAAAA,CAAAA,CAAAA;AAKN,WACiC,YAAA,OAAxBwD,GAAIA,GAAI3C,SAAS,CAAA,KAAA,CACvB2C,GAAIA,GAAI3C,SAAS,CAAA,EAAGqC,KAAAA,IAErBM,CAAAA,GAAI6D,IAAAA;AAGN,QAAwB,SAApBf,GAAQgB,QACV,QAAO9D;AAGT,UAAM8D,KAAUhB,GAAQgB,YAAYtH,KAAS,SAAS;AACtD,QAAIuH;AAEJ,QAAI/D,GAAI3C,SAAS,KAAKyF,GAAQkB,aAC5BD,CAAAA,KAAM/D;SAAAA;AAAAA,UACkB,MAAfA,GAAI3C,OAIb,QAHA0G,KAAM/D,GAAI,CAAA,GAGS,YAAA,OAAR+D,KACFpI,GAAAA,QAAAA,EAAMwC,KAAI,QAAA,GAAS4F,EAAAA,IAEnBA;AAITA,MAAAA,KAAM;IACR;AAEA,WAAOjB,GAAQK,cACbW,IACA,EAAE3F,KAAK,QAAA,GACP4F,EAAAA;EAEJ;AAEA,WAASE,GACPlB,IACAtE,IAAAA;AAEA,UAAMyF,KAAazF,GAAIzC,MAAM5E,CAAAA;AAC7B,WAAK8M,KAIEA,GAAW5N,OAAO,SAAU2G,IAAKkH,IAAAA;AACtC,YAAMC,KAAeD,GAAIzG,QAAQ,GAAA;AAEjC,UAAA,OAAI0G,IAAqB;AACvB,cAAMjG,KA7iBd,SAA+BA,IAAAA;AAS7B,iBAAA,OARoBA,GAAIT,QAAQ,GAAA,KAE4B,SAAlCS,GAAInC,MAAMpD,CAAAA,MAClCuF,KAAMA,GAAIpB,QAAQ7D,GAA6B,SAAUmL,IAAGC,IAAAA;AAC1D,mBAAOA,GAAOC,YAAAA;UAChB,CAAA,IAGKpG;QACT,EAmiB0CgG,GAAIxF,MAAM,GAAGyF,EAAAA,CAAAA,EAAe1E,KAAAA,GACxD8E,KAjsBd,SAAiB/F,IAAAA;AACf,gBAAMgG,KAAQhG,GAAI,CAAA;AAClB,kBACa,QAAVgG,MAA2B,QAAVA,OAClBhG,GAAIpB,UAAU,KACdoB,GAAIA,GAAIpB,SAAS,CAAA,MAAOoH,KAEjBhG,GAAIE,MAAM,GAAA,EAAI,IAEhBF;QACT,EAurB8B0F,GAAIxF,MAAMyF,KAAe,CAAA,EAAG1E,KAAAA,CAAAA,GAE5CgF,KAAYrO,EAA0B8H,EAAAA,KAAQA;AAGpD,YAAkB,UAAduG,GAAqB,QAAOzH;AAEhC,cAAM0H,KAAmB1H,GAAIyH,EAAAA,IAziBrC,SACE3B,IACA5E,IACAqG,IACAI,IAAAA;AAEA,iBAAY,YAARzG,KACKqG,GAAM7E,MAAM,MAAA,EAAQrJ,OAAO,SAAUuO,IAAQC,IAAAA;AAClD,kBAAM3G,KAAM2G,GAAOnG,MAAM,GAAGmG,GAAOpH,QAAQ,GAAA,CAAA;AAW3C,mBAFAmH,GALsB1G,GACnBuB,KAAAA,EACA3C,QAAQ,aAAagI,CAAAA,OAAUA,GAAO,CAAA,EAAGR,YAAAA,CAAAA,CAAAA,IAGpBO,GAAOnG,MAAMR,GAAId,SAAS,CAAA,EAAGqC,KAAAA,GAE9CmF;UACT,GAAG,CAAA,CAAA,IACc,WAAR1G,MAA0B,UAARA,KACpByG,GAAcJ,IAAOzB,IAAK5E,EAAAA,KACxBqG,GAAMxI,MAAMlD,CAAAA,MAErB0L,KAAQA,GAAM7F,MAAM,GAAG6F,GAAMnH,SAAS,CAAA,IAG1B,WAAVmH,MAEiB,YAAVA,MAIJA;QACT,EAugBUzB,IACA5E,IACAqG,IACA1B,GAAQ1B,SAAAA;AAImB,oBAAA,OAApBuD,OACNlM,EAAqBsG,KAAK4F,EAAAA,KACzB9L,EAA4BkG,KAAK4F,EAAAA,OAEnC1H,GAAIyH,EAAAA,IAAanB,GAAQoB,GAAgBjF,KAAAA,CAAAA;MAE7C,MAAmB,aAARyE,OACTlH,GAAI5G,EAA0B8N,EAAAA,KAAQA,EAAAA,IAAAA;AAGxC,aAAOlH;IACT,GAAG,CAAA,CAAA,IAAA;EACL;AAzIA6F,EAAAA,GAAQI,YAAYJ,GAAQI,aAAa,CAAA,GACzCJ,GAAQ1B,YAAY0B,GAAQ1B,aAAaA,IACzC0B,GAAQlE,UAAUkE,GAAQlE,WAAWA,IACrCkE,GAAQlM,sBAAsBkM,GAAQlM,sBAAmByM,EAAAA,CAAAA,GAChDzM,GAAwBkM,GAAQlM,mBAAAA,IACrCA,GAEJkM,GAAQK,gBAAgBL,GAAQK,iBAAuBA;AAwJvD,QAAM6B,KAAwD,CAAA,GACxDC,KAA6D,CAAA,GAQ7DC,KAA6B,EACjC,CAACjR,EAASC,UAAAA,GAAa,EACrB8H,OAAO6E,GAAWtJ,CAAAA,GAClBmF,OAAAA,GACAC,MAAMC,IAASD,IAAOR,IAAAA;AACpB,UAAA,CAAA,EAASgJ,IAAO5H,EAAAA,IAAWX,GAAQ,CAAA,EAChCG,QAAQvF,GAAkC,EAAA,EAC1CwE,MAAMvE,CAAAA;AAET,WAAO,EACL0N,OAAAA,IACA9E,UAAU1D,GAAMY,IAASpB,EAAAA,EAAAA;EAE7B,GACA6B,OAAOC,IAAMC,IAAQ/B,IAAAA;AACnB,UAAM6G,KAAQ,EACZ7E,KAAKhC,GAAMgC,IAAAA;AAiBb,WAdIF,GAAKkH,UACPnC,GAAMM,YACJ,oBACAR,GAAQlE,QAAQX,GAAKkH,MAAM1O,YAAAA,GAAemI,EAAAA,GAE5CX,GAAKoC,SAAS+E,QAAQ,EACpBC,OAAO,CAAA,GACPhF,UAAU,CAAC,EAAExF,MAAM5G,EAAS4B,MAAMA,MAAMoI,GAAKkH,MAAAA,CAAAA,GAC7CG,cAAAA,MACAzK,MAAM5G,EAASY,WACfkO,KAAK,SAAA,CAAA,IAIFpH,GAAE,cAAcqH,IAAO9E,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA;EACtD,EAAA,GAGF,CAAClI,EAASE,SAAAA,GAAY,EACpB6H,OAAO8E,GAAcpJ,CAAAA,GACrBgF,OAAAA,GACAC,OAAOqF,IACPhE,QAAMA,CAACqG,IAAGkB,IAAIpJ,OACLR,GAAAA,MAAAA,EAAIwC,KAAKhC,GAAMgC,IAAAA,CAAAA,EAAAA,GAI1B,CAAClK,EAASG,aAAAA,GAAgB,EACxB4H,OAAO6E,GAAWlJ,CAAAA,GAClB+E,OAAAA,GACAC,OAAOqF,IACPhE,QAAMA,CAACqG,IAAGkB,IAAIpJ,OACLR,GAAAA,MAAAA,EAAIwC,KAAKhC,GAAMgC,IAAAA,CAAAA,EAAAA,GAI1B,CAAClK,EAASI,SAAAA,GAAY,EACpB2H,OAAO6E,GAAWhJ,CAAAA,GAClB6E,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL4I,MAAAA,QACA3P,MAAMiI,GAAQlB,GAAQ,CAAA,EAAGG,QAAQ,WAAW,EAAA,CAAA,EAAKA,QAC/C3C,IACA,IAAA,EAAA,IAKN4D,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,OAAAA,EAAKwC,KAAKhC,GAAMgC,IAAAA,GACdxC,GAAAA,QAAAA,EAAAA,CAAAA,GACMsC,GAAKoH,OAAAA,EACT/B,WAAWrF,GAAKuH,OAAAA,QAAevH,GAAKuH,IAAAA,KAAS,GAAA,CAAA,GAE5CvH,GAAKpI,IAAAA,CAAAA,EAAAA,GAWhB,CAAC5B,EAASK,UAAAA,GAAa,EACrB0H,OAAO6E,GAAWjJ,CAAAA,GAClB8E,OAAAA,GACAC,OAAMC,CAAAA,QACG,EAELyI,OAAOpB,GAAgB,QAAQrH,GAAQ,CAAA,KAAM,EAAA,GAC7C4I,MAAM5I,GAAQ,CAAA,KAAA,QACd/G,MAAM+G,GAAQ,CAAA,EAAGG,QAAQ3C,IAAiB,IAAA,GAC1CS,MAAM5G,EAASI,UAAAA,GAAAA,GAKrB,CAACJ,EAASM,UAAAA,GAAa,EACrByH,OAAO4E,GAAkB9I,CAAAA,GACzB4E,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL/G,MAAM+G,GAAQ,CAAA,EAAGG,QAAQ3C,IAAiB,IAAA,EAAA,IAG9C4D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,QAAAA,EAAMwC,KAAKhC,GAAMgC,IAAAA,GAAMF,GAAKpI,IAAAA,EAAAA,GAOvC,CAAC5B,EAASO,QAAAA,GAAW,EACnBwH,OAAO6E,GAAW5I,CAAAA,GAClByE,OAAAA,GACAC,OAAMC,CAAAA,QACJoI,GAAUxF,KAAK,EACbhL,UAAUoI,GAAQ,CAAA,GAClB6I,YAAY7I,GAAQ,CAAA,EAAA,CAAA,GAGf,CAAA,IAEToB,QAAQiE,GAAAA,GAGV,CAAChO,EAASQ,iBAAAA,GAAoB,EAC5BuH,OAAO0E,GAAYxI,CAAAA,GACnBwE,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL8I,QAAAA,IAAY5C,GAAQlE,QAAQhC,GAAQ,CAAA,GAAIgC,EAAAA,CAAAA,IACxC/I,MAAM+G,GAAQ,CAAA,EAAA,IAGlBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,KAAAA,EAAGwC,KAAKhC,GAAMgC,KAAKwH,MAAM7C,GAAQ1B,UAAUnD,GAAKyH,QAAQ,KAAK,MAAA,EAAA,GAC3D/J,GAAAA,OAAAA,EAAKwC,KAAKhC,GAAMgC,IAAAA,GAAMF,GAAKpI,IAAAA,CAAAA,EAAAA,GAMnC,CAAC5B,EAASS,OAAAA,GAAU,EAClBsH,OAAO0E,GAAYrI,CAAAA,GACnBqE,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACLgJ,WAAwC,QAA7BhJ,GAAQ,CAAA,EAAGnG,YAAAA,EAAAA,IAG1BuH,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,SAAAA,EACEkK,SAAS5H,GAAK2H,WACdzH,KAAKhC,GAAMgC,KACX2H,UAAAA,MACAjL,MAAK,WAAA,CAAA,EAAA,GAMb,CAAC5G,EAASU,OAAAA,GAAU,EAClBqH,OAAO6E,GACLiC,GAAQiD,qBAAqBxN,IAA0BD,CAAAA,GAEzDoE,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAUqB,GAAY/E,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,GACzC6J,IAAIlD,GAAQlE,QAAQhC,GAAQ,CAAA,GAAIgC,EAAAA,GAChCqH,OAAOrJ,GAAQ,CAAA,EAAGS,OAAAA,IAGtBW,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,IACDsC,GAAKgI,KAAAA,IACT,EAAED,IAAI/H,GAAK+H,IAAI7H,KAAKhC,GAAMgC,IAAAA,GAC1BD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAK5B,CAAClI,EAASW,aAAAA,GAAgB,EACxBoH,OAAO6E,GAAWrI,CAAAA,GAClBkE,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAUqB,GAAY/E,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,GACzC8J,OAAsB,QAAfrJ,GAAQ,CAAA,IAAa,IAAI,GAChC/B,MAAM5G,EAASU,QAAAA,GAAAA,GAKrB,CAACV,EAASY,SAAAA,GAAY,EAIpBmH,OAAO8E,GAAcrI,CAAAA,GACrBiE,OAAAA,GACAC,MAAMC,IAASD,IAAOR,IAAAA;AACpB,UAAA,CAAA,EAAS+J,EAAAA,IAActJ,GAAQ,CAAA,EAAGZ,MAAMzB,EAAAA,GAElC4L,KAAU,IAAIpM,OAAAA,IAAWmM,EAAAA,IAAc,IAAA,GACvCE,KAAUxJ,GAAQ,CAAA,EAAGG,QAAQoJ,IAAS,EAAA,GAEtCE,MAr9Be7C,KAq9BiB4C,IAp9BrC5H,GAAe0C,KAAKoF,CAAAA,OAAKA,GAAEvH,KAAKyE,EAAAA,CAAAA,IAq9B7B1B,KACAJ;AAv9BZ,QAA6B8B;AAy9BrB,UAAM+C,KAAU3J,GAAQ,CAAA,EAAGnG,YAAAA,GACrB6O,KAAAA,OACJnO,EAA6BuG,QAAQ6I,EAAAA,GAEjCxD,MACJuC,KAAeiB,KAAU3J,GAAQ,CAAA,GACjC8C,KAAAA,GAEI8G,KAAM,EACVnB,OAAOpB,GAAgBlB,IAAKnG,GAAQ,CAAA,CAAA,GACpC0I,cAAcA,IACdvC,KAAAA,GAAAA;AAuBF,WAdA5G,GAAMsK,WAAWtK,GAAMsK,YAAwB,QAAZF,IAE/BjB,KACFkB,GAAI3Q,OAAO+G,GAAQ,CAAA,IAEnB4J,GAAInG,WAAWgG,GAAU1J,IAAOyJ,IAASjK,EAAAA,GAO3CA,GAAMsK,WAAAA,OAECD;EACT,GACAxI,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAACsC,GAAK8E,KAAGM,EAAAA,EAAClF,KAAKhC,GAAMgC,IAAAA,GAASF,GAAKoH,KAAAA,GAChCpH,GAAKpI,SAASoI,GAAKoC,WAAWnC,GAAOD,GAAKoC,UAAUlE,EAAAA,IAAS,GAAA,EAAA,GAMtE,CAAClI,EAASc,eAAAA,GAAkB,EAI1BiH,OAAO8E,GAAcjI,CAAAA,GACrB6D,OAAAA,GACAC,MAAMC,IAAAA;AACJ,UAAMmG,KAAMnG,GAAQ,CAAA,EAAG8C,KAAAA;AAEvB,WAAO,EACL2F,OAAOpB,GAAgBlB,IAAKnG,GAAQ,CAAA,KAAM,EAAA,GAC1CmG,KAAAA,GAAAA;EAEJ,GACA/E,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAACsC,GAAK8E,KAAGM,EAAAA,CAAAA,GAAKpF,GAAKoH,OAAAA,EAAOlH,KAAKhC,GAAMgC,IAAAA,CAAAA,CAAAA,EAAAA,GAIhD,CAAClK,EAASa,WAAAA,GAAc,EACtBkH,OAAO8E,GAAcnI,CAAAA,GACrB+D,OAAAA,GACAC,OAAKA,OACI,CAAA,IAETqB,QAAQiE,GAAAA,GAGV,CAAChO,EAASe,KAAAA,GAAQ,EAChBgH,OAAO4E,GAAkBtC,EAAAA,GACzB5B,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL8J,KAAK9J,GAAQ,CAAA,GACb8I,QAAQlE,GAAY5E,GAAQ,CAAA,CAAA,GAC5B+J,OAAO/J,GAAQ,CAAA,EAAA,IAGnBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,OAAAA,EACEwC,KAAKhC,GAAMgC,KACXuI,KAAKzI,GAAKyI,OAAAA,QACVC,OAAO1I,GAAK0I,SAAAA,QACZrE,KAAKQ,GAAQ1B,UAAUnD,GAAKyH,QAAQ,OAAO,KAAA,EAAA,CAAA,EAAA,GAUnD,CAACzR,EAASgB,IAAAA,GAAO,EACf+G,OAAO0E,GAAYrC,EAAAA,GACnB3B,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAUwB,GAAkBlF,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,GAC/CuJ,QAAQlE,GAAY5E,GAAQ,CAAA,CAAA,GAC5B+J,OAAO/J,GAAQ,CAAA,EAAA,IAGnBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,KAAAA,EACEwC,KAAKhC,GAAMgC,KACXwH,MAAM7C,GAAQ1B,UAAUnD,GAAKyH,QAAQ,KAAK,MAAA,GAC1CiB,OAAO1I,GAAK0I,MAAAA,GAEXzI,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAO/B,CAAClI,EAASiB,2BAAAA,GAA8B,EACtC8G,OAAO0E,GAAYzH,CAAAA,GACnByD,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACLyD,UAAU,CACR,EACExK,MAAM+G,GAAQ,CAAA,GACd/B,MAAM5G,EAAS4B,KAAAA,CAAAA,GAGnB6P,QAAQ9I,GAAQ,CAAA,GAChB/B,MAAM5G,EAASgB,KAAAA,GAAAA,GAKrB,CAAChB,EAASkB,mBAAAA,GAAsB,EAC9B6G,OAAOC,GAAY,CAACC,IAAQC,OACtBA,GAAMsK,YAAY3D,GAAQ8D,kBAAAA,OAIvBlG,GAAY3H,CAAAA,EAA0BmD,IAAQC,EAAAA,CAAAA,GAEvDO,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACLyD,UAAU,CACR,EACExK,MAAM+G,GAAQ,CAAA,GACd/B,MAAM5G,EAAS4B,KAAAA,CAAAA,GAGnB6P,QAAQ9I,GAAQ,CAAA,GAChB+J,OAAAA,QACA9L,MAAM5G,EAASgB,KAAAA,GAAAA,GAKrB,CAAChB,EAASmB,kBAAAA,GAAqB,EAC7B4G,OAAO0E,GAAY1H,CAAAA,GACnB0D,OAAAA,GACAC,MAAMC,IAAAA;AACJ,QAAIiK,KAAUjK,GAAQ,CAAA,GAClB8I,KAAS9I,GAAQ,CAAA;AAOrB,WAJKvF,EAAwB0H,KAAK2G,EAAAA,MAChCA,KAAS,YAAYA,KAGhB,EACLrF,UAAU,CACR,EACExK,MAAMgR,GAAQ9J,QAAQ,WAAW,EAAA,GACjClC,MAAM5G,EAAS4B,KAAAA,CAAAA,GAGnB6P,QAAQA,IACR7K,MAAM5G,EAASgB,KAAAA;EAEnB,EAAA,GAGF,CAAChB,EAASqB,WAAAA,GAAcoG,GACtBC,IA74CqB,CAAA,GAi5CvB,CAAC1H,EAASkC,aAAAA,GAAgBuF,GACxBC,IAj5CuB,CAAA,GAq5CzB,CAAC1H,EAASoB,gBAAAA,GAAmB,EAC3B2G,OAAO6E,GAAW9I,CAAAA,GAClB2E,OAAAA,GACAC,OAAOqF,IACPhE,QAAMA,MACG,KAAA,GAIX,CAAC/J,EAASsB,SAAAA,GAAY,EACpByG,OAAOC,GAAY8E,EAAAA,GACnBrE,OAAAA,GACAC,OAAOoF,IACP/D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,KAAAA,EAAGwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAIrD,CAAClI,EAASuB,GAAAA,GAAM,EACdwG,OAAO0E,GAAYtH,CAAAA,GACnBsD,OAAAA,GACAC,OAAMC,CAAAA,QACJqI,GAAKrI,GAAQ,CAAA,CAAA,IAAM,EACjB8I,QAAQ9I,GAAQ,CAAA,GAChB+J,OAAO/J,GAAQ,CAAA,EAAA,GAGV,CAAA,IAEToB,QAAQiE,GAAAA,GAGV,CAAChO,EAASwB,QAAAA,GAAW,EACnBuG,OAAO4E,GAAkBvH,CAAAA,GACzBqD,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL8J,KAAK9J,GAAQ,CAAA,KAAA,QACbpH,KAAKoH,GAAQ,CAAA,EAAA,IAGjBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OACZ8I,GAAKhH,GAAKzI,GAAAA,IACfmG,GAAAA,OAAAA,EACEwC,KAAKhC,GAAMgC,KACXuI,KAAKzI,GAAKyI,KACVpE,KAAKQ,GAAQ1B,UAAU6D,GAAKhH,GAAKzI,GAAAA,EAAKkQ,QAAQ,OAAO,KAAA,GACrDiB,OAAO1B,GAAKhH,GAAKzI,GAAAA,EAAKmR,MAAAA,CAAAA,IAEtB,KAAA,GAIR,CAAC1S,EAASyB,OAAAA,GAAU,EAClBsG,OAAO0E,GAAYpH,CAAAA,GACnBoD,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAU1D,GAAMC,GAAQ,CAAA,GAAIT,EAAAA,GAC5B2K,kBAAkBlK,GAAQ,CAAA,GAC1BpH,KAAKoH,GAAQ,CAAA,EAAA,IAGjBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OACZ8I,GAAKhH,GAAKzI,GAAAA,IACfmG,GAAAA,KAAAA,EACEwC,KAAKhC,GAAMgC,KACXwH,MAAM7C,GAAQ1B,UAAU6D,GAAKhH,GAAKzI,GAAAA,EAAKkQ,QAAQ,KAAK,MAAA,GACpDiB,OAAO1B,GAAKhH,GAAKzI,GAAAA,EAAKmR,MAAAA,GAErBzI,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,IAGzBR,GAAAA,QAAAA,EAAMwC,KAAKhC,GAAMgC,IAAAA,GAAMF,GAAK6I,gBAAAA,EAAAA,GAKlC,CAAC7S,EAAS0B,KAAAA,GAAQ,EAChBqG,OAAO6E,GAAW1H,CAAAA,GAClBuD,OAAAA,GACAC,OAAOsD,IACPjC,OAAOC,IAAMC,IAAQ/B,IAAAA;AACnB,UAAMxG,KAAQsI;AACd,WACEtC,GAAAA,SAAAA,EAAOwC,KAAKhC,GAAMgC,IAAAA,GAChBxC,GAAAA,SAAAA,MACEA,GAAAA,MAAAA,MACGhG,GAAMyK,OAAOnD,IAAI,SAA4BM,IAASJ,IAAAA;AACrD,aACExB,GAAAA,MAAAA,EAAIwC,KAAKhB,IAAG4J,OAAOzG,GAAc3K,IAAOwH,EAAAA,EAAAA,GACrCe,GAAOX,IAASpB,EAAAA,CAAAA;IAGvB,CAAA,CAAA,CAAA,GAIJR,GAAAA,SAAAA,MACGhG,GAAMyJ,MAAMnC,IAAI,SAA0B+J,IAAK7J,IAAAA;AAC9C,aACExB,GAAAA,MAAAA,EAAIwC,KAAKhB,GAAAA,GACN6J,GAAI/J,IAAI,SAA2BM,IAAS0J,IAAAA;AAC3C,eACEtL,GAAAA,MAAAA,EAAIwC,KAAK8I,IAAGF,OAAOzG,GAAc3K,IAAOsR,EAAAA,EAAAA,GACrC/I,GAAOX,IAASpB,EAAAA,CAAAA;MAGvB,CAAA,CAAA;IAGN,CAAA,CAAA,CAAA;EAIR,EAAA,GAGF,CAAClI,EAAS4B,IAAAA,GAAO,EAKfmG,OAAO8E,GAAczG,EAAAA,GACrBqC,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL/G,MAAM+G,GAAQ,CAAA,EAEXG,QAAQrE,GAAkB,CAACwO,IAAMC,OACzBrE,GAAQlM,oBAAoBuQ,EAAAA,IAC/BrE,GAAQlM,oBAAoBuQ,EAAAA,IAC5BD,EAAAA,EAAAA,IAIZlJ,QAAOC,CAAAA,OACEA,GAAKpI,KAAAA,GAIhB,CAAC5B,EAAS6B,UAAAA,GAAa,EACrBkG,OAAO4E,GAAkB9G,CAAAA,GACzB4C,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EAGLkE,UAAU1D,GAAMC,GAAQ,CAAA,GAAIT,EAAAA,EAAAA,IAGhC6B,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,UAAAA,EAAQwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAI1D,CAAClI,EAAS8B,cAAAA,GAAiB,EACzBiG,OAAO4E,GAAkB5G,CAAAA,GACzB0C,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EAGLkE,UAAU1D,GAAMC,GAAQ,CAAA,GAAIT,EAAAA,EAAAA,IAGhC6B,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,MAAAA,EAAIwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAItD,CAAClI,EAAS+B,WAAAA,GAAc,EAKtBgG,OAAO4E,GAAkBzG,EAAAA,GACzBuC,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL/G,MAAM+G,GAAQ,CAAA,GACd/B,MAAM5G,EAAS4B,KAAAA,GAAAA,GAKrB,CAAC5B,EAASgC,UAAAA,GAAa,EACrB+F,OAAO4E,GAAkB3G,CAAAA,GACzByC,OAAAA,GACAC,OAAOoF,IACP/D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,QAAAA,EAAMwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAIxD,CAAClI,EAASiC,mBAAAA,GAAsB,EAC9B8F,OAAO4E,GAAkB1G,CAAAA,GACzBwC,OAAAA,GACAC,OAAOoF,IACP/D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,OAAAA,EAAKwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,EAAAA;AAAAA,WAoCrD2G,GAAQsE,0BAAAA,OACHlC,GAAMjR,EAASY,SAAAA,GAAAA,OACfqQ,GAAMjR,EAASc,eAAAA;AAGxB,QAAM6O,MA9qCR,SACEsB,IAAAA;AAOA,QAAImC,KAAWC,OAAOC,KAAKrC,EAAAA;AA8B3B,aAASsC,GACPtL,IACAC,IAAAA;AAEA,UACIsL,IAEAC,IAHA3J,KAAS,CAAA,GAET4J,KAAW,IAEXC,KAAoB;AAQxB,WANAzL,GAAMG,cAAcH,GAAMG,eAAe,IAMlCJ,MAAQ;AACb,YAAIiB,KAAI;AACR,eAAOA,KAAIkK,GAAShK,UAAQ;AAI1B,cAHAsK,KAAWN,GAASlK,EAAAA,GACpBsK,KAAOvC,GAAMyC,EAAAA,GAETxL,GAAMK,UAAAA,CAAWiL,GAAKzL,MAAMQ,QAAQ;AACtCW,YAAAA;AACA;UACF;AAEA,gBAAMP,KAAU6K,GAAKzL,MAAME,IAAQC,EAAAA;AAEnC,cAAIS,IAAS;AACXgL,YAAAA,KAAoBhL,GAAQ,CAAA,GAG5BT,GAAMG,eAAesL,IAErB1L,KAASA,GAAO2L,UAAUD,GAAkBvK,MAAAA,GAE5CqK,KAASD,GAAK9K,MAAMC,IAAS4K,IAAarL,EAAAA,GAMvB,QAAfuL,GAAO7M,SACT6M,GAAO7M,OAAO8M,KAGhB5J,GAAOyB,KAAKkI,EAAAA;AACZ;UACF;AAEAvK,UAAAA;QACF;MACF;AAKA,aAFAhB,GAAMG,cAAc,IAEbyB;IACT;AAEA,WA3EAsJ,GAASS,KAAK,SAAUC,IAAOC,IAAAA;AAC7B,UAAIC,KAAS/C,GAAM6C,EAAAA,EAAOrL,OACtBwL,KAAShD,GAAM8C,EAAAA,EAAOtL;AAG1B,aAAIuL,OAAWC,KACND,KAASC,KACPH,KAAQC,KAAAA,KACT;IAIZ,CAAA,GAAA,SA+D2B9L,IAAQC,IAAAA;AACjC,aAAOqL,GA9HX,SAA6BtL,IAAAA;AAC3B,eAAOA,GACJa,QAAQ/E,GAAc,IAAA,EACtB+E,QAAQ5E,GAAY,EAAA,EACpB4E,QAAQvD,GAAO,MAAA;MACpB,EAyH2C0C,EAAAA,GAASC,EAAAA;IAClD;EACF,EAwkC2B+I,EAAAA,GACnBvB,OAj5BU3F,MAqClB,yBACEkH,IACAiD,IAAAA;AAEA,WAAA,SACE3B,IACAxI,IACA7B,IAAAA;AAEA,YAAMiM,KAAWlD,GAAMsB,GAAI3L,IAAAA,EAAMmD;AAEjC,aAAOmK,KACHA,GAAW,MAAMC,GAAS5B,IAAKxI,IAAQ7B,EAAAA,GAAQqK,IAAKxI,IAAQ7B,EAAAA,IAC5DiM,GAAS5B,IAAKxI,IAAQ7B,EAAAA;IAC5B;EACF,EA61BoD+I,IAAOpC,GAAQuF,UAAAA,GAAAA,SAh5BjDC,GACd9B,IACArK,KAA6B,CAAA,GAAA;AAE7B,QAAIoM,MAAMC,QAAQhC,EAAAA,GAAM;AACtB,YAAMiC,KAAStM,GAAMgC,KACfJ,KAAS,CAAA;AAIf,UAAI2K,KAAAA;AAEJ,eAASvL,KAAI,GAAGA,KAAIqJ,GAAInJ,QAAQF,MAAK;AACnChB,QAAAA,GAAMgC,MAAMhB;AAEZ,cAAMwL,KAAUL,GAAc9B,GAAIrJ,EAAAA,GAAIhB,EAAAA,GAChCyM,KAA8B,YAAA,OAAZD;AAEpBC,QAAAA,MAAYF,KACd3K,GAAOA,GAAOV,SAAS,CAAA,KAAMsL,KACR,SAAZA,MACT5K,GAAOyB,KAAKmJ,EAAAA,GAGdD,KAAgBE;MAClB;AAIA,aAFAzM,GAAMgC,MAAMsK,IAEL1K;IACT;AAEA,WAAOC,IAAOwI,IAAK8B,IAAenM,EAAAA;EACpC;AAlCF,MAAkB6B;AAm5BhB,QAAM+F,MAAMR,GAAQV,EAAAA;AAEpB,SAAImC,GAAU3H,SAEV1B,GAAAA,OAAAA,MACGoI,KACDpI,GAAAA,UAAAA,EAAQwC,KAAI,SAAA,GACT6G,GAAU/H,IAAI,SAAwB4L,IAAAA;AACrC,WACElN,GAAAA,OAAAA,EACEqK,IAAIlD,GAAQlE,QAAQiK,GAAIpD,YAAY7G,EAAAA,GACpCT,KAAK0K,GAAIpD,WAAAA,GAERoD,GAAIpD,YACJ9B,IAAQC,IAAOiF,GAAIrU,UAAU,EAAEgI,QAAAA,KAAQ,CAAA,CAAA,CAAA;EAG9C,CAAA,CAAA,CAAA,IAMDuH;AACT;AAAA,IAAA,uBAWI+E,CAAAA,OAAAA;AAAAA,MAAAA,EAACzI,UAAEA,KAAW,IAAEyC,SAAEA,GAAAA,IAAmBgG,IAAP9F,KAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,QAAAA,GAAAA,QAAAA,CAAAA;AAAAA,QAAAA,IAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,OAAAA,KAAAA,EAAAA;AAAAA,SAAAA,KAAAA,GAAAA,KAAAA,GAAAA,QAAAA,KAAAA,CAAAA,GAAAA,QAAAA,KAAAA,GAAAA,EAAAA,CAAAA,KAAAA,MAAAA,GAAAA,EAAAA,IAAAA,GAAAA,EAAAA;AAAAA,WAAAA;EAAAA,EAAK8F,IAAAC,CAAAA;AAQrC,SAAaC,eACXpG,GAASvC,IAAUyC,EAAAA,GACnBE,EAAAA;AAAsC;;;AC3+D1C,SAAS,MAAM,QAAQ,MAAM;AAC3B,SAAO,UAAU,OAAO,OAAO,kBAAU,QAAQ,IAAI;AACvD;AAEA,IAAO,gBAAQ;;;AFnBf,IAAM,cAAN,cAA0B,wBAAU;AAAA,EAApC;AAAA;AAEI;AAAA,iCAAQ;AAAA,MACJ,wBAAwB;AAAA,MACxB,sBAAsB,CAAC;AAAA,IAC3B;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4CAAmB,CAAC,MAAM,8BAA8B,UAAU;AAC9D,aAAO,CAAC,OAAO,gBAAgB,OAAO;AAClC,cAAM,EAAE,UAAU,UAAU,YAAY,IAAI,KAAK;AACjD,YAAI,UAAU,UAAa,6BAA6B;AAQpD,kBAAQ;AAAA,QACZ;AACA,cAAM,cAAc,EAAE,GAAG,UAAU,CAAC,IAAI,GAAG,MAAM;AACjD,iBAAS,aAAa,eAClB,eAAe;AAAA,UACf,GAAG;AAAA,UACH,CAAC,IAAI,GAAG;AAAA,QACZ,GAAG,EAAE;AAAA,MACT;AAAA,IACJ;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+CAAsB,CAAC,QAAQ;AAC3B,aAAO,CAAC,UAAU;AACd,cAAM,eAAe;AACrB,cAAM,EAAE,UAAU,SAAS,IAAI,KAAK;AACpC,cAAM,iBAAiB,EAAE,GAAG,SAAS;AACrC,sBAAM,gBAAgB,GAAG;AACzB,iBAAS,cAAc;AAAA,MAC3B;AAAA,IACJ;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAAkB,CAAC,cAAc,aAAa;AAC1C,YAAM,EAAE,UAAU,SAAS,IAAI,KAAK;AACpC,YAAM,EAAE,8BAA8B,IAAI,IAAI,aAAa,UAAU,SAAS,eAAe;AAC7F,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,aAAO,YAAI,UAAU,MAAM,GAAG;AAC1B,iBAAS,GAAG,YAAY,GAAG,2BAA2B,GAAG,EAAE,KAAK;AAAA,MACpE;AACA,aAAO;AAAA,IACX;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAAc,CAAC,aAAa;AACxB,aAAO,CAAC,OAAO,mBAAmB;AAC9B,YAAI,aAAa,OAAO;AACpB;AAAA,QACJ;AACA,cAAM,EAAE,UAAU,UAAU,YAAY,IAAI,KAAK;AACjD,gBAAQ,KAAK,gBAAgB,OAAO,QAAQ;AAC5C,cAAM,cAAc;AAAA,UAChB,GAAG;AAAA,QACP;AACA,cAAM,UAAU,EAAE,CAAC,QAAQ,GAAG,MAAM;AACpC,cAAM,YAAY,OAAO,KAAK,WAAW,EAAE,IAAI,CAAC,QAAQ;AACpD,gBAAM,SAAS,QAAQ,GAAG,KAAK;AAC/B,iBAAO,EAAE,CAAC,MAAM,GAAG,YAAY,GAAG,EAAE;AAAA,QACxC,CAAC;AACD,cAAM,aAAa,OAAO,OAAO,CAAC,GAAG,GAAG,SAAS;AACjD,aAAK,SAAS,EAAE,wBAAwB,KAAK,CAAC;AAC9C,iBAAS,YAAY,eACjB,eAAe;AAAA,UACf,GAAG;AAAA,UACH,CAAC,KAAK,GAAG;AAAA,QACb,CAAC;AAAA,MACL;AAAA,IACJ;AA6BA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,WAAW,MAAM;AAC/B,UAAI,CAAC,OAAO,sBAAsB;AAC9B;AAAA,MACJ;AACA,YAAM,EAAE,UAAU,UAAU,SAAS,IAAI,KAAK;AAC9C,YAAM,cAAc,EAAE,GAAG,SAAS;AAClC,UAAI,OAAO;AACX,UAAI,aAAa;AACjB,UAAI,eAAe;AACnB,UAAI,iBAAS,OAAO,oBAAoB,GAAG;AACvC,eAAO,OAAO,qBAAqB;AACnC,qBAAa,OAAO,qBAAqB;AACzC,uBAAe,OAAO,qBAAqB;AAC3C,YAAI,WAAW,OAAO;AACtB,YAAI,WAAW,UAAU;AACrB,gBAAM,EAAE,YAAY,IAAI;AACxB,qBAAW,YAAY,eAAe,EAAE,MAAM,SAAS,OAAO,EAAE,GAAG,QAAQ;AAC3E,iBAAO,SAAS;AAChB,uBAAa,SAAS;AACtB,yBAAe,SAAS;AAAA,QAC5B;AACA,YAAI,CAAC,SAAS,cAAc,YAAY,cAAc,WAAW;AAC7D,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,YAAM,SAAS,KAAK,gBAAgB,UAAU,WAAW;AACzD,YAAM,WAAW,cAAc,gBAAgB,KAAK,gBAAgB,IAAI;AAExE,kBAAI,aAAa,QAAQ,QAAQ;AACjC,eAAS,WAAW;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAzJA,WAAW,MAAM;AACb,UAAM,EAAE,OAAO,IAAI,KAAK;AACxB,WAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,OAAO,SAAS,QAAQ,IAAI,MAAM;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA,EAgGA,gBAAgB,MAAM;AAClB,UAAM,EAAE,UAAU,EAAE,gBAAgB,EAAG,IAAI,KAAK;AAChD,YAAQ,MAAM;AAAA,MACV,KAAK;AACD,eAAO,CAAC;AAAA,MACZ,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO,CAAC;AAAA,MACZ,KAAK;AAAA,MACL;AAEI,eAAO,gBAAgB,mBAAmB,gBAAgB;AAAA,IAClE;AAAA,EACJ;AAAA;AAAA;AAAA,EAuCA,SAAS;AACL,UAAM,EAAE,QAAQ,WAAW,WAAW,CAAC,GAAG,UAAU,aAAa,UAAU,MAAM,WAAW,OAAO,UAAU,UAAU,WAAW,UAAU,aAAa,QAAQ,SAAS,UAAU,MAAO,IAAI,KAAK;AACpM,UAAM,EAAE,QAAAiG,SAAQ,aAAa,aAAa,iBAAiB,gBAAgB,IAAI;AAC/E,UAAM,EAAE,aAAAC,aAAY,IAAID;AACxB,UAAM,SAAS,YAAY,eAAe,WAAW,QAAQ;AAC7D,UAAM,YAAY,aAAa,UAAU,eAAe;AACxD,UAAM,EAAE,YAAY,mBAAmB,CAAC,EAAE,IAAI;AAC9C,UAAM,gBAAgB,UAAU,SAAS,OAAO,SAAS,SAAS;AAClE,UAAM,cAAc,UAAU,eAAe,OAAO;AACpD,QAAI;AACJ,QAAI;AACA,YAAM,aAAa,OAAO,KAAK,gBAAgB;AAC/C,0BAAoB,gBAAgB,YAAY,UAAU,KAAK;AAAA,IACnE,SACO,KAAK;AACR,iBAAQ,oBAAAE,MAAM,OAAO,EAAE,UAAU,KAAC,oBAAAC,KAAK,KAAK,EAAE,WAAW,gBAAgB,OAAO,EAAE,OAAO,MAAM,GAAG,cAAU,oBAAAA,KAAK,sBAAU,EAAE,SAAS,EAAE,uBAAuB,KAAK,GAAG,UAAU,gBAAgB,mBAAmB,oBAAoB,CAAC,QAAQ,QAAQ,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,OAAG,oBAAAA,KAAK,OAAO,EAAE,UAAU,KAAK,UAAU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;AAAA,IACtU;AACA,UAAM,WAAW,YAAY,uBAAuB,UAAU,SAAS;AACvE,UAAM,gBAAgB;AAAA;AAAA,MAElB,OAAO,UAAU,UAAU,QAAQ,KAAK;AAAA,MACxC,aAAa,UAAU,UAAU,QAAQ,SAAY;AAAA,MACrD,YAAY,kBAAkB,IAAI,CAACC,UAAS;AACxC,cAAM,8BAA8B,YAAI,QAAQ,CAAC,gBAAgBA,OAAM,wBAAwB,CAAC;AAChG,cAAM,gBAAgB,8BAA8B,SAAS,uBAAuB,SAASA,KAAI;AACjG,cAAM,SAAS,aAAa,aAAa,EAAE,WAAW;AACtD,cAAM,gBAAgB,YAAI,UAAU,CAACA,KAAI,GAAG,CAAC,CAAC;AAC9C,eAAO;AAAA,UACH,aAAU,oBAAAD,KAAKF,cAAa,EAAE,MAAMG,OAAM,UAAU,KAAK,WAAWA,KAAI,GAAG,QAAQ,YAAI,QAAQ,CAAC,gBAAgBA,KAAI,GAAG,CAAC,CAAC,GAAG,UAAU,eAAe,aAAa,YAAI,aAAaA,KAAI,GAAG,UAAU,eAAe,UAAoB,aAA0B,UAAU,YAAI,UAAUA,KAAI,GAAG,aAA0B,wBAAwB,KAAK,MAAM,wBAAwB,aAAa,KAAK,YAAYA,KAAI,GAAG,UAAU,KAAK,iBAAiBA,OAAM,2BAA2B,GAAG,QAAgB,SAAkB,UAAoB,UAAoB,UAAoB,WAAsB,qBAAqB,KAAK,oBAAoB,GAAGA,KAAI;AAAA,UACvoB,MAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,eAAO,oBAAAD,KAAK,UAAU,EAAE,GAAG,eAAe,YAAY,KAAK,eAAe,CAAC;AAAA,EAC/E;AACJ;AACA,IAAO,sBAAQ;;;AGvOf,IAAAE,sBAAkE;AAClE,IAAAC,gBAAuC;AAMvC,IAAM,kBAAkB;AAAA,EACpB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AACV;AAWA,SAAS,kBAAkB,QAAQ,WAAW,UAAU,UAAU;AAC9D,QAAM,QAAQ,UAAU;AACxB,QAAM,EAAE,QAAAC,SAAQ,gBAAgB,IAAI;AACpC,MAAI,OAAO,UAAU,YAAY;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,YAAY,SAASA,SAAQ;AAC9C,WAAOA,QAAO,KAAK;AAAA,EACvB;AACA,QAAM,aAAa,cAAc,MAAM;AACvC,QAAM,OAAO,MAAM,QAAQ,UAAU,IAAI,WAAW,CAAC,IAAI,cAAc;AACvE,QAAM,WAAW,OAAO;AACxB,MAAI,gBAAgB,gBAAgB,IAAI;AACxC,MAAI,YAAY,YAAYA,SAAQ;AAChC,oBAAgB;AAAA,EACpB;AAGA,MAAI,CAAC,kBAAkB,OAAO,SAAS,OAAO,QAAQ;AAClD,WAAO,MAAM;AAAA,EACjB;AACA,SAAO,iBAAiBA,UAClBA,QAAO,aAAa,IACpB,MAAM;AACJ,UAAM,2BAA2B,YAAY,4BAA4B,UAAU,SAAS;AAC5F,eAAQ,oBAAAC,KAAK,0BAA0B,EAAE,QAAgB,UAAoB,QAAQ,gBAAgB,mBAAmB,kBAAkB,CAAC,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,SAAmB,CAAC;AAAA,EAC1L;AACR;AAOA,SAAS,kBAAkB,OAAO;AAC9B,QAAM,EAAE,QAAQ,SAAS,UAAU,WAAW,UAAU,UAAU,aAAa,UAAU,aAAa,MAAM,UAAU,aAAa,qBAAqB,UAAU,UAAU,yBAAyB,MAAO,IAAI;AAChN,QAAM,EAAE,aAAa,aAAa,gBAAgB,IAAI;AACtD,QAAM,YAAY,aAAa,UAAU,eAAe;AACxD,QAAMC,iBAAgB,YAAY,iBAAiB,UAAU,SAAS;AACtE,QAAM,2BAA2B,YAAY,4BAA4B,UAAU,SAAS;AAC5F,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,SAAS;AAC9E,QAAMC,sBAAqB,YAAY,sBAAsB,UAAU,SAAS;AAChF,QAAM,SAAS,YAAY,eAAe,SAAS,QAAQ;AAC3D,QAAM,UAAU,UAAU,MAAM;AAChC,QAAM,WAAW,aAAa,YAAY,WAAW,QAAQ,SAAS,UAAU,UAAU,WAAW,GAAG,SAAS;AAIjH,QAAM,iCAA6B,2BAAY,CAACC,WAAU,gBAAgBC,QAAO;AAC7E,UAAM,QAAQA,OAAM;AACpB,WAAO,SAASD,WAAU,gBAAgB,KAAK;AAAA,EACnD,GAAG,CAAC,SAAS,QAAQ,CAAC;AACtB,QAAM,iBAAiB,kBAAkB,QAAQ,WAAW,UAAU,QAAQ;AAC9E,QAAM,WAAW,QAAQ,UAAU,YAAY,MAAM,QAAQ;AAC7D,QAAM,WAAW,QAAQ,UAAU,aAAa,MAAM,YAAY,MAAM,OAAO,YAAY,OAAO,SAAS;AAC3G,QAAM,oBAAoB,UAAU;AAEpC,QAAM,YAAY,sBAAsB,SAAY,MAAM,YAAY,QAAQ,iBAAiB;AAC/F,QAAM,YAAY,QAAQ,UAAU,aAAa,MAAM,SAAS;AAChE,MAAI,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AAClC,WAAO;AAAA,EACX;AACA,QAAM,eAAe,YAAY,gBAAgB,QAAQ,UAAU,eAAe;AAClF,QAAM,EAAE,UAAU,GAAG,iBAAiB,IAAI,eAAe,CAAC;AAE1D,QAAM,gBAAgB,aAAK,UAAU,CAAC,iBAAiB,cAAc,UAAU,CAAC;AAChF,MAAI,kBAAkB,eAAe;AACjC,kBAAc,cAAc,IAAI,aAAK,cAAc,cAAc,GAAG,CAAC,cAAc,OAAO,CAAC;AAAA,EAC/F;AACA,QAAM,YAAS,oBAAAJ,KAAK,gBAAgB,EAAE,GAAG,OAAO,UAAU,4BAA4B,UAAoB,QAAgB,UAAU,eAAe,UAAoB,UAAoB,WAAsB,WAAsB,aAAa,kBAAkB,aAA0B,WAAW,SAAS,CAAC;AACrT,QAAM,KAAK,SAAS,MAAM;AAE1B,MAAI;AACJ,MAAI,wBAAwB;AACxB,YAAQ;AAAA,EACZ,OACK;AACD,YACI,4BAA4B,SACtB,OACA,UAAU,SAAS,MAAM,OAAO,SAAS,OAAO,SAAS,MAAM,SAAS;AAAA,EACtF;AACA,QAAM,cAAc,UAAU,eAAe,MAAM,OAAO,eAAe,OAAO,eAAe;AAC/F,QAAM,kBAAkB,UAAU,kCAA+B,oBAAAA,KAAK,sBAAU,EAAE,SAAS,EAAE,uBAAuB,KAAK,GAAG,UAAU,YAAY,CAAC,IAAM;AACzJ,QAAM,OAAO,UAAU;AACvB,QAAM,SAAS,UAAU,WAAW;AACpC,QAAM,aAAa,CAAC,cAAc,SAAS,SAAS,cAAc,MAAM,CAAC,EAAE;AAC3E,MAAI,CAAC,aAAa,YAAY,SAAS,SAAS,GAAG;AAC/C,eAAW,KAAK,kCAAkC;AAAA,EACtD;AACA,MAAI,qCAAU,YAAY;AACtB,QAAI,MAAuC;AACvC,cAAQ,KAAK,yGAAyG;AAAA,IAC1H;AACA,eAAW,KAAK,SAAS,UAAU;AAAA,EACvC;AACA,MAAI,UAAU,YAAY;AACtB,eAAW,KAAK,UAAU,UAAU;AAAA,EACxC;AACA,QAAM,oBAAiB,oBAAAA,KAAKE,oBAAmB,EAAE,MAAY,UAAoB,QAAgB,UAAoB,WAAW,CAAC,aAAa,YAAY,SAAS,SAAS,GAAG,SAAmB,CAAC;AAKnM,QAAM,kBAAkB,cAAe,OAAO,SAAS,OAAO,UAAU,CAAC,YAAY,SAAS,MAAM,IAAK,aAAa,oBAAAF,KAAKG,qBAAoB,EAAE,QAAQ,UAAU,aAA0B,UAAoB,QAAgB,UAAoB,SAAmB,CAAC;AACzQ,QAAM,aAAa;AAAA,IACf,iBAAc,oBAAAH,KAAK,0BAA0B,EAAE,IAAI,cAAc,EAAE,GAAG,aAAa,iBAAiB,QAAgB,UAAoB,SAAmB,CAAC;AAAA,IAC5J,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,SAAS,OAAO,SAAS,WAAW,OAAO;AAAA,IAC3C,QAAQ;AAAA,IACR,WAAW,YAAY,SAAY;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,WAAW,KAAK,GAAG,EAAE,KAAK;AAAA,IACtC,OAAO,UAAU;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,QAAM,cAAc,SAAS,OAAO;AACpC,QAAM,cAAc,SAAS,OAAO;AACpC,QAAM,yBAAwB,qCAAW,iBAAe,qCAAW,mCAAkC;AACrG,aAAQ,oBAAAA,KAAKC,gBAAe,EAAE,GAAG,YAAY,cAAU,oBAAAK,MAAM,oBAAAC,UAAW,EAAE,UAAU,CAAC,OAAO,OAAO,SAAS,CAAC,yBAAyB,CAAC,YAAY,SAAS,MAAM,SAAM,oBAAAP,KAAK,aAAa,EAAE,MAAY,UAAoB,UAAoB,WAAsB,aAA0B,UAAoB,aAA0B,UAAoB,UAAoB,aAA0B,QAAQ,MAAM,QAAQ,UAAU,MAAM,UAAU,SAAS,MAAM,SAAS,SAAS,OAAO,MAAM,IAAI,CAACQ,aAAY,YAAY,eAAe,iBAASA,QAAO,IAAIA,WAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,UAAoB,UAAoB,QAAgB,SAAmB,CAAC,GAAI,OAAO,SAAS,CAAC,yBAAyB,CAAC,YAAY,SAAS,MAAM,SAAM,oBAAAR,KAAK,aAAa,EAAE,MAAY,UAAoB,UAAoB,WAAsB,aAA0B,UAAoB,aAA0B,UAAoB,UAAoB,aAA0B,QAAQ,MAAM,QAAQ,UAAU,MAAM,UAAU,SAAS,MAAM,SAAS,SAAS,OAAO,MAAM,IAAI,CAACQ,aAAY,YAAY,eAAe,iBAASA,QAAO,IAAIA,WAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,UAAoB,UAAoB,QAAgB,SAAmB,CAAC,CAAE,EAAE,CAAC,EAAE,CAAC;AAC5tC;AAIA,IAAM,cAAN,cAA0B,wBAAU;AAAA,EAChC,sBAAsB,WAAW;AAC7B,WAAO,CAAC,WAAW,KAAK,OAAO,SAAS;AAAA,EAC5C;AAAA,EACA,SAAS;AACL,eAAO,oBAAAR,KAAK,mBAAmB,EAAE,GAAG,KAAK,MAAM,CAAC;AAAA,EACpD;AACJ;AACA,IAAO,sBAAQ;;;AC7Kf,IAAAS,sBAA4B;AAM5B,SAAS,YAAY,OAAO;AACxB,QAAM,EAAE,QAAQ,MAAM,UAAU,UAAU,UAAU,UAAU,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,UAAU,QAAQ,SAAS,UAAU,WAAW,UAAW,IAAI;AACpL,QAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,QAAM,EAAE,SAAAC,UAAS,aAAa,aAAa,gBAAgB,IAAI;AAC/D,QAAM,cAAc,YAAY,SAAS,MAAM,IAAI,YAAY,QAAQ,QAAQ,IAAI;AACnF,MAAI,gBAAgB,cAAc,WAAW;AAC7C,MAAI,UAAU,UAAU,QAAQ,QAAQA,QAAO,GAAG;AAC9C,oBAAgB;AAAA,EACpB;AACA,QAAM,EAAE,SAAS,eAAe,cAAc,IAAI,OAAO,SAAS,GAAG,QAAQ,IAAI,aAAa,QAAQ;AACtG,QAAM,eAAe,YAAY,gBAAgB,QAAQ,UAAU,eAAe;AAClF,QAAM,QAAQ,WAAW,SAAS;AAClC,QAAM,SAAS,UAAU,QAAQ,QAAQA,QAAO;AAChD,aAAQ,oBAAAC,KAAK,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,YAAY,GAAG,QAAgB,UAAoB,IAAI,SAAS,KAAK,MAAY,OAAc,WAAW,CAAC,cAAc,WAAsB,OAAO,UAAU,UAAoB,QAAgB,SAAkB,UAAoB,UAAoB,UAAoB,aAA0B,WAAsB,UAAoB,aAA0B,UAAqB,CAAC;AACxb;AACA,IAAO,sBAAQ;;;ACrBf,IAAAC,gBAA0B;AAM1B,SAAS,UAAU,OAAO;AACtB,QAAM,EAAE,UAAU,SAAS,IAAI;AAC/B,+BAAU,MAAM;AACZ,QAAI,aAAa,QAAW;AACxB,eAAS,IAAI;AAAA,IACjB;AAAA,EACJ,GAAG,CAAC,UAAU,QAAQ,CAAC;AACvB,SAAO;AACX;AACA,IAAO,oBAAQ;;;ACPf,SAAS,SAAS;AACd,SAAO;AAAA,IACH,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,IAEZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAO,iBAAQ;;;ACtBf,IAAAC,sBAA4B;AAOb,SAAR,8BAA+C,OAAO;AACzD,QAAM,EAAE,UAAU,aAAa,UAAU,QAAQ,SAAS,IAAI;AAC9D,QAAM,UAAU,aAAa,UAAU,SAAS,eAAe;AAC/D,QAAM,EAAE,OAAO,eAAe,KAAK,IAAI;AACvC,MAAI,CAAC,eAAe,CAAC,cAAc;AAC/B,WAAO;AAAA,EACX;AACA,QAAM,2BAA2B,YAAY,4BAA4B,UAAU,OAAO;AAC1F,aAAQ,oBAAAC,KAAK,0BAA0B,EAAE,IAAI,cAAc,QAAQ,GAAG,aAA0B,QAAgB,UAAoB,SAAmB,CAAC;AAC5J;;;AChBA,IAAAC,sBAA2C;AAK5B,SAAR,uBAAwC,OAAO;AAClD,QAAM,EAAE,UAAU,WAAW,UAAU,YAAY,aAAa,WAAW,WAAW,SAAS,OAAO,kBAAkB,kBAAkB,gBAAgB,UAAU,UAAU,SAAU,IAAI;AAC5L,QAAM,EAAE,YAAAC,aAAY,gBAAAC,iBAAgB,cAAAC,eAAc,cAAAC,cAAa,IAAI,SAAS,UAAU;AACtF,QAAM,WAAW;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,EAChB;AACA,aAAQ,oBAAAC,MAAM,OAAO,EAAE,WAAsB,UAAU,KAAC,oBAAAC,KAAK,OAAO,EAAE,WAAW,aAAa,aAAa,aAAa,SAAmB,CAAC,GAAG,kBAAe,oBAAAA,KAAK,OAAO,EAAE,WAAW,+BAA+B,cAAU,oBAAAD,MAAM,OAAO,EAAE,WAAW,aAAa,OAAO;AAAA,IAC1P,SAAS;AAAA,IACT,gBAAgB;AAAA,EACpB,GAAG,UAAU,EAAE,aAAa,oBAAiB,oBAAAC,KAAKH,eAAc,EAAE,OAAO,UAAU,UAAU,YAAY,YAAY,CAAC,WAAW,SAAS,eAAe,OAAO,QAAQ,CAAC,GAAG,UAAoB,SAAmB,CAAC,IAAK,aAAa,oBAAiB,oBAAAG,KAAKJ,iBAAgB,EAAE,OAAO,UAAU,UAAU,YAAY,YAAY,CAAC,aAAa,SAAS,eAAe,OAAO,QAAQ,CAAC,GAAG,UAAoB,SAAmB,CAAC,GAAI,eAAY,oBAAAI,KAAKL,aAAY,EAAE,OAAO,UAAU,UAAU,YAAY,UAAU,SAAS,iBAAiB,KAAK,GAAG,UAAoB,SAAmB,CAAC,GAAI,iBAAc,oBAAAK,KAAKF,eAAc,EAAE,OAAO,UAAU,UAAU,YAAY,UAAU,SAAS,iBAAiB,KAAK,GAAG,UAAoB,SAAmB,CAAC,CAAE,EAAE,CAAC,EAAE,CAAC,CAAE,EAAE,CAAC;AACnwB;;;AClBA,IAAAG,uBAA2C;AAM5B,SAAR,mBAAoC,OAAO;AAC9C,QAAM,EAAE,QAAQ,WAAW,UAAU,UAAU,UAAU,OAAO,YAAY,UAAU,UAAU,UAAU,QAAQ,MAAO,IAAI;AAC7H,QAAM,YAAY,aAAa,QAAQ;AACvC,QAAMC,iCAAgC,YAAY,iCAAiC,UAAU,SAAS;AACtG,QAAMC,0BAAyB,YAAY,0BAA0B,UAAU,SAAS;AACxF,QAAMC,2BAA0B,YAAY,2BAA2B,UAAU,SAAS;AAE1F,QAAM,EAAE,iBAAiB,EAAE,WAAAC,WAAU,EAAG,IAAI,SAAS;AACrD,aAAQ,qBAAAC,MAAM,YAAY,EAAE,WAAsB,IAAI,SAAS,KAAK,UAAU,KAAC,qBAAAC,KAAKH,0BAAyB,EAAE,UAAoB,OAAO,UAAU,SAAS,OAAO,UAAoB,QAAgB,UAAoB,SAAmB,CAAC,OAAG,qBAAAG,KAAKL,gCAA+B,EAAE,UAAoB,aAAa,UAAU,eAAe,OAAO,aAAa,QAAgB,UAAoB,SAAmB,CAAC,OAAG,qBAAAK,KAAK,OAAO,EAAE,WAAW,uBAAuB,UAAU,SAC5c,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,UAAU,UAAO,qBAAAA,KAAKJ,yBAAwB,EAAE,GAAG,UAAU,GAAG,GAAG,CAAE,EAAE,CAAC,GAAG,cAAW,qBAAAI,KAAKF,YAAW,EAAE,WAAW,kBAAkB,SAAS,YAAY,UAAU,YAAY,UAAU,UAAoB,SAAmB,CAAC,CAAE,EAAE,CAAC;AAChR;;;AChBA,IAAAG,uBAA4B;AAOb,SAAR,wBAAyC,OAAO;AACnD,QAAM,EAAE,UAAU,OAAO,QAAQ,UAAU,UAAU,SAAS,IAAI;AAClE,QAAM,UAAU,aAAa,UAAU,SAAS,eAAe;AAC/D,QAAM,EAAE,OAAO,eAAe,KAAK,IAAI;AACvC,MAAI,CAAC,SAAS,CAAC,cAAc;AACzB,WAAO;AAAA,EACX;AACA,QAAM,qBAAqB,YAAY,sBAAsB,UAAU,OAAO;AAC9E,aAAQ,qBAAAC,KAAK,oBAAoB,EAAE,IAAI,QAAQ,QAAQ,GAAG,OAAc,UAAoB,QAAgB,UAAoB,SAAmB,CAAC;AACxJ;;;AChBA,IAAAC,uBAAkE;AAClE,IAAAC,gBAA4B;AAQb,SAAR,kBAAmC,OAAO;AAC7C,QAAM;AAAA,IAAE;AAAA,IAAI;AAAA;AAAA,IACZ;AAAA,IAAO;AAAA,IAAU;AAAA,IAAU;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAU;AAAA,IAAkB;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAa;AAAA,IAAU;AAAA,IAAW;AAAA,IAAM;AAAA;AAAA,IACtJ;AAAA;AAAA,IACA,GAAG;AAAA,EAAK,IAAI;AAGZ,MAAI,CAAC,IAAI;AACL,YAAQ,IAAI,aAAa,KAAK;AAC9B,UAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,KAAK,CAAC,EAAE;AAAA,EAC9D;AACA,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,cAAc,QAAQ,MAAM,OAAO;AAAA,EAC1C;AACA,MAAI;AACJ,MAAI,WAAW,SAAS,YAAY,WAAW,SAAS,WAAW;AAC/D,iBAAa,SAAS,UAAU,IAAI,QAAQ;AAAA,EAChD,OACK;AACD,iBAAa,SAAS,OAAO,KAAK;AAAA,EACtC;AACA,QAAM,gBAAY,2BAAY,CAAC,EAAE,QAAQ,EAAE,OAAAC,OAAM,EAAE,MAAM,SAASA,WAAU,KAAK,QAAQ,aAAaA,MAAK,GAAG,CAAC,UAAU,OAAO,CAAC;AACjI,QAAM,cAAU,2BAAY,CAAC,EAAE,OAAO,MAAM,OAAO,IAAI,UAAU,OAAO,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC;AAC5F,QAAM,eAAW,2BAAY,CAAC,EAAE,OAAO,MAAM,QAAQ,IAAI,UAAU,OAAO,KAAK,GAAG,CAAC,SAAS,EAAE,CAAC;AAC/F,aAAQ,qBAAAC,MAAM,qBAAAC,UAAW,EAAE,UAAU,KAAC,qBAAAC,KAAK,SAAS,EAAE,IAAQ,MAAM,IAAI,WAAW,gBAAgB,UAAU,UAAU,UAAoB,WAAW,WAAW,OAAO,YAAY,GAAG,YAAY,MAAM,OAAO,WAAW,WAAW,EAAE,IAAI,QAAW,UAAU,oBAAoB,WAAW,QAAQ,SAAS,SAAS,UAAU,oBAAoB,mBAAmB,IAAI,CAAC,CAAC,OAAO,QAAQ,EAAE,CAAC,GAAG,MAAM,QAAQ,OAAO,QAAQ,SAAM,qBAAAA,KAAK,YAAY,EAAE,IAAI,WAAW,EAAE,GAAG,UAAU,OAAO,SAChd,OAAO,OAAO,WAAW,CAAC,OAAO,SAAS,SAAS,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,CAAC,EAC1F,IAAI,CAAC,YAAY;AAClB,eAAO,qBAAAA,KAAK,UAAU,EAAE,OAAO,QAAQ,GAAG,OAAO;AAAA,EACrD,CAAC,EAAE,GAAG,YAAY,EAAE,EAAE,CAAE,EAAE,CAAC;AAC3C;;;ACvCA,IAAAC,uBAA4B;AAIb,SAAR,aAA8B,EAAE,SAAS,GAAG;AAC/C,QAAM,EAAE,YAAY,UAAU,OAAO,oBAAoB,CAAC,EAAE,IAAI,uBAAuB,QAAQ;AAC/F,MAAI,UAAU;AACV,WAAO;AAAA,EACX;AACA,aAAQ,qBAAAC,KAAK,OAAO,EAAE,cAAU,qBAAAA,KAAK,UAAU,EAAE,MAAM,UAAU,GAAG,mBAAmB,WAAW,gBAAgB,kBAAkB,aAAa,EAAE,IAAI,UAAU,WAAW,CAAC,EAAE,CAAC;AACpL;;;ACVA,IAAAC,uBAA4B;;;ACA5B,IAAAC,uBAA4B;AAEb,SAAR,WAA4B,OAAO;AACtC,QAAM,EAAE,WAAW,WAAW,MAAM,WAAW,UAAU,UAAU,GAAG,WAAW,IAAI;AACrF,aAAQ,qBAAAC,KAAK,UAAU,EAAE,MAAM,UAAU,WAAW,WAAW,QAAQ,IAAI,SAAS,IAAI,GAAG,YAAY,cAAU,qBAAAA,KAAK,KAAK,EAAE,WAAW,uBAAuB,IAAI,GAAG,CAAC,EAAE,CAAC;AAC9K;AACO,SAAS,WAAW,OAAO;AAC9B,QAAM,EAAE,UAAU,EAAE,gBAAgB,EAAG,IAAI;AAC3C,aAAQ,qBAAAA,KAAK,YAAY,EAAE,OAAO,gBAAgB,mBAAmB,UAAU,GAAG,WAAW,mBAAmB,GAAG,OAAO,MAAM,OAAO,CAAC;AAC5I;AACO,SAAS,eAAe,OAAO;AAClC,QAAM,EAAE,UAAU,EAAE,gBAAgB,EAAG,IAAI;AAC3C,aAAQ,qBAAAA,KAAK,YAAY,EAAE,OAAO,gBAAgB,mBAAmB,cAAc,GAAG,WAAW,wBAAwB,GAAG,OAAO,MAAM,aAAa,CAAC;AAC3J;AACO,SAAS,aAAa,OAAO;AAChC,QAAM,EAAE,UAAU,EAAE,gBAAgB,EAAG,IAAI;AAC3C,aAAQ,qBAAAA,KAAK,YAAY,EAAE,OAAO,gBAAgB,mBAAmB,YAAY,GAAG,WAAW,sBAAsB,GAAG,OAAO,MAAM,WAAW,CAAC;AACrJ;AACO,SAAS,aAAa,OAAO;AAChC,QAAM,EAAE,UAAU,EAAE,gBAAgB,EAAG,IAAI;AAC3C,aAAQ,qBAAAA,KAAK,YAAY,EAAE,OAAO,gBAAgB,mBAAmB,YAAY,GAAG,WAAW,qBAAqB,GAAG,OAAO,UAAU,UAAU,MAAM,SAAS,CAAC;AACtK;;;ADhBe,SAAR,UAA2B,EAAE,WAAW,SAAS,UAAU,SAAU,GAAG;AAC3E,QAAM,EAAE,gBAAgB,IAAI;AAC5B,aAAQ,qBAAAC,KAAK,OAAO,EAAE,WAAW,OAAO,cAAU,qBAAAA,KAAK,KAAK,EAAE,WAAW,uCAAuC,SAAS,IAAI,cAAU,qBAAAA,KAAK,YAAY,EAAE,UAAU,QAAQ,MAAM,QAAQ,WAAW,qBAAqB,OAAO,gBAAgB,mBAAmB,SAAS,GAAG,SAAkB,UAAoB,SAAmB,CAAC,EAAE,CAAC,EAAE,CAAC;AACpV;;;AELA,SAAS,kBAAkB;AACvB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAO,0BAAQ;;;ACbf,IAAAC,uBAA4B;AAKb,SAAR,iBAAkC,OAAO;AAC5C,QAAM,EAAE,IAAI,YAAY,IAAI;AAC5B,MAAI,CAAC,aAAa;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,gBAAgB,UAAU;AACjC,eAAQ,qBAAAC,KAAK,KAAK,EAAE,IAAQ,WAAW,qBAAqB,UAAU,YAAY,CAAC;AAAA,EACvF,OACK;AACD,eAAQ,qBAAAA,KAAK,OAAO,EAAE,IAAQ,WAAW,qBAAqB,UAAU,YAAY,CAAC;AAAA,EACzF;AACJ;;;AChBA,IAAAC,uBAA2C;AAM5B,SAAR,UAA2B,EAAE,QAAQ,SAAU,GAAG;AACrD,QAAM,EAAE,gBAAgB,IAAI;AAC5B,aAAQ,qBAAAC,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU,KAAC,qBAAAC,KAAK,OAAO,EAAE,WAAW,iBAAiB,cAAU,qBAAAA,KAAK,MAAM,EAAE,WAAW,eAAe,UAAU,gBAAgB,mBAAmB,WAAW,EAAE,CAAC,EAAE,CAAC,OAAG,qBAAAA,KAAK,MAAM,EAAE,WAAW,cAAc,UAAU,OAAO,IAAI,CAAC,OAAOC,OAAM;AAC/R,eAAQ,qBAAAD,KAAK,MAAM,EAAE,WAAW,+BAA+B,UAAU,MAAM,MAAM,GAAGC,EAAC;AAAA,EAC7F,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACxB;;;ACXA,IAAAC,uBAA2C;;;ACA3C,IAAAC,uBAA2C;AAC3C,IAAM,wBAAwB;AAKf,SAAR,MAAuB,OAAO;AACjC,QAAM,EAAE,OAAO,UAAU,GAAG,IAAI;AAChC,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,aAAQ,qBAAAC,MAAM,SAAS,EAAE,WAAW,iBAAiB,SAAS,IAAI,UAAU,CAAC,OAAO,gBAAY,qBAAAC,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAU,sBAAsB,CAAC,CAAC,EAAE,CAAC;AAC/K;;;ADJe,SAAR,cAA+B,OAAO;AACzC,QAAM,EAAE,IAAI,OAAO,UAAU,QAAQ,MAAM,aAAa,QAAQ,UAAU,cAAc,UAAU,SAAS,IAAI;AAC/G,QAAM,YAAY,aAAa,QAAQ;AACvC,QAAMC,4BAA2B,YAAY,4BAA4B,UAAU,SAAS;AAC5F,MAAI,QAAQ;AACR,eAAO,qBAAAC,KAAK,OAAO,EAAE,WAAW,UAAU,SAAmB,CAAC;AAAA,EAClE;AACA,aAAQ,qBAAAC,MAAMF,2BAA0B,EAAE,GAAG,OAAO,UAAU,CAAC,oBAAgB,qBAAAC,KAAK,OAAO,EAAE,OAAc,UAAoB,GAAO,CAAC,GAAG,gBAAgB,cAAc,cAAc,MAAM,UAAU,QAAQ,IAAI,EAAE,CAAC;AACzN;;;AEfA,IAAO,wBAAQ;;;ACDf,IAAAE,uBAA4B;AAMb,SAAR,mBAAoC,OAAO;AAC9C,QAAM,EAAE,SAAS,CAAC,GAAG,SAAS,IAAI;AAClC,MAAI,OAAO,WAAW,GAAG;AACrB,WAAO;AAAA,EACX;AACA,QAAM,KAAK,QAAQ,QAAQ;AAC3B,aAAQ,qBAAAC,KAAK,OAAO,EAAE,cAAU,qBAAAA,KAAK,MAAM,EAAE,IAAQ,WAAW,2CAA2C,UAAU,OACxG,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,EACvB,IAAI,CAAC,OAAO,UAAU;AACvB,eAAQ,qBAAAA,KAAK,MAAM,EAAE,WAAW,eAAe,UAAU,MAAM,GAAG,KAAK;AAAA,EAC3E,CAAC,EAAE,CAAC,EAAE,CAAC;AACnB;;;ACjBA,IAAAC,uBAA4B;AAMb,SAAR,kBAAmC,OAAO;AAC7C,QAAM,EAAE,UAAU,KAAK,IAAI;AAC3B,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,QAAM,KAAK,OAAO,QAAQ;AAC1B,MAAI,OAAO,SAAS,UAAU;AAC1B,eAAQ,qBAAAC,KAAK,KAAK,EAAE,IAAQ,WAAW,cAAc,UAAU,KAAK,CAAC;AAAA,EACzE;AACA,aAAQ,qBAAAA,KAAK,OAAO,EAAE,IAAQ,WAAW,cAAc,UAAU,KAAK,CAAC;AAC3E;;;AChBA,IAAAC,uBAA2C;AAQ5B,SAAR,oBAAqC,OAAO;AAC/C,QAAM,EAAE,aAAa,UAAU,UAAU,UAAU,YAAY,YAAY,UAAU,UAAU,UAAU,QAAQ,OAAO,SAAU,IAAI;AACtI,QAAM,UAAU,aAAa,QAAQ;AACrC,QAAM,qBAAqB,YAAY,sBAAsB,UAAU,OAAO;AAC9E,QAAM,2BAA2B,YAAY,4BAA4B,UAAU,OAAO;AAE1F,QAAM,EAAE,iBAAiB,EAAE,WAAAC,WAAU,EAAG,IAAI,SAAS;AACrD,aAAQ,qBAAAC,MAAM,YAAY,EAAE,IAAI,SAAS,KAAK,UAAU,CAAC,aAAU,qBAAAC,KAAK,oBAAoB,EAAE,IAAI,QAAQ,QAAQ,GAAG,OAAc,UAAoB,QAAgB,UAAoB,SAAmB,CAAC,GAAI,mBAAgB,qBAAAA,KAAK,0BAA0B,EAAE,IAAI,cAAc,QAAQ,GAAG,aAA0B,QAAgB,UAAoB,SAAmB,CAAC,GAAI,WAAW,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,UAAU,QAAQ,UAAU,QAAQ,SAAM,qBAAAA,KAAKF,YAAW,EAAE,WAAW,0BAA0B,SAAS,WAAW,MAAM,GAAG,UAAU,YAAY,UAAU,UAAoB,SAAmB,CAAC,CAAE,EAAE,CAAC;AAC9mB;;;AChBA,IAAAG,uBAA2C;AAC3C,IAAMC,yBAAwB;AAKf,SAAR,WAA4B,OAAO;AACtC,QAAM,EAAE,IAAI,OAAO,SAAS,IAAI;AAChC,aAAQ,qBAAAC,MAAM,UAAU,EAAE,IAAQ,UAAU,CAAC,OAAO,gBAAY,qBAAAC,KAAK,QAAQ,EAAE,WAAW,YAAY,UAAUF,uBAAsB,CAAC,CAAC,EAAE,CAAC;AAC/I;;;ACTA,IAAAG,uBAA2C;AAQ3C,SAAS,iBAAiB,OAAO;AAC7B,QAAM,EAAE,QAAQ,UAAU,QAAQ,SAAS,IAAI;AAC/C,QAAM,EAAE,gBAAgB,IAAI;AAC5B,MAAI,gBAAgB,mBAAmB;AACvC,QAAM,kBAAkB,CAAC;AACzB,MAAI,YAAY,SAAS,KAAK;AAC1B,oBAAgB,mBAAmB;AACnC,oBAAgB,KAAK,SAAS,GAAG;AAAA,EACrC;AACA,MAAI,QAAQ;AACR,oBACI,kBAAkB,mBAAmB,mBAC/B,mBAAmB,6BACnB,mBAAmB;AAC7B,oBAAgB,KAAK,MAAM;AAAA,EAC/B;AACA,aAAQ,qBAAAC,MAAM,OAAO,EAAE,WAAW,qBAAqB,UAAU,KAAC,qBAAAC,KAAK,KAAK,EAAE,cAAU,qBAAAA,KAAK,sBAAU,EAAE,SAAS,EAAE,uBAAuB,KAAK,GAAG,UAAU,gBAAgB,eAAe,eAAe,EAAE,CAAC,EAAE,CAAC,GAAG,cAAU,qBAAAA,KAAK,OAAO,EAAE,UAAU,KAAK,UAAU,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAC/R;AACA,IAAO,2BAAQ;;;AC1Bf,IAAAC,uBAA2C;AAQ5B,SAAR,yBAA0C,OAAO;AACpD,QAAM,EAAE,IAAI,YAAY,OAAO,UAAU,OAAO,aAAa,qBAAqB,UAAU,UAAU,QAAQ,UAAU,UAAU,SAAU,IAAI;AAChJ,QAAM,EAAE,WAAAC,YAAW,gBAAgB,IAAI;AAEvC,QAAM,EAAE,cAAAC,cAAa,IAAID,WAAU;AACnC,QAAM,WAAW,gBAAgB,mBAAmB,UAAU,CAAC,KAAK,CAAC;AACrE,QAAM,aAAa,4BAA4B;AAC/C,MAAI,CAAC,YAAY;AACb,eAAQ,qBAAAE,KAAK,OAAO,EAAE,WAAW,YAAY,OAAc,SAAmB,CAAC;AAAA,EACnF;AACA,aAAQ,qBAAAA,KAAK,OAAO,EAAE,WAAW,YAAY,OAAc,cAAU,qBAAAC,MAAM,OAAO,EAAE,WAAW,OAAO,UAAU,KAAC,qBAAAD,KAAK,OAAO,EAAE,WAAW,4BAA4B,cAAU,qBAAAC,MAAM,OAAO,EAAE,WAAW,cAAc,UAAU,KAAC,qBAAAD,KAAK,OAAO,EAAE,OAAO,UAAU,UAAoB,IAAI,GAAG,EAAE,OAAO,CAAC,OAAG,qBAAAA,KAAK,SAAS,EAAE,WAAW,gBAAgB,MAAM,QAAQ,IAAI,GAAG,EAAE,QAAQ,QAAQ,CAAC,EAAE,OAAO,MAAM,YAAY,UAAU,OAAO,KAAK,GAAG,cAAc,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,OAAG,qBAAAA,KAAK,OAAO,EAAE,WAAW,uCAAuC,SAAmB,CAAC,OAAG,qBAAAA,KAAK,OAAO,EAAE,WAAW,YAAY,cAAU,qBAAAA,KAAKD,eAAc,EAAE,WAAW,+BAA+B,OAAO,EAAE,QAAQ,IAAI,GAAG,UAAU,YAAY,UAAU,SAAS,oBAAoB,KAAK,GAAG,UAAoB,SAAmB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AACxyB;;;ACJA,SAAS,YAAY;AACjB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,wBAAgB;AAAA,IACjC;AAAA,IACA,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,IAAO,oBAAQ;;;AClCf,IAAAG,uBAA2C;AAC3C,IAAAC,gBAA6D;AAE7D,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,OAAO,KAAK,EAAE,MAAM,CAAC,UAAU,UAAU,EAAE;AAC7D;AACA,SAAS,YAAY,EAAE,MAAM,OAAO,OAAO,QAAQ,QAAQ,MAAM,UAAU,UAAU,WAAW,UAAU,QAAQ,QAAS,GAAG;AAC1H,QAAM,KAAK,SAAS,MAAM;AAC1B,QAAM,EAAE,cAAAC,cAAa,IAAI,SAAS;AAClC,aAAQ,qBAAAC,KAAKD,eAAc,EAAE,QAAQ,EAAE,MAAM,UAAU,GAAG,IAAQ,MAAY,WAAW,gBAAgB,SAAS,EAAE,aAAa,iBAAiB,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,aAAa,MAAM,OAAc,UAAoB,UAAoB,WAAsB,UAAU,CAACE,WAAU,OAAO,MAAMA,MAAK,GAAG,QAAgB,SAAkB,UAAoB,OAAO,IAAI,oBAAoB,mBAAmB,MAAM,EAAE,CAAC;AACva;AAIA,SAAS,cAAc,EAAE,OAAO,OAAO,WAAW,OAAO,WAAW,OAAO,YAAY,OAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,SAAS,UAAU,MAAO,GAAG;AAC5J,QAAM,EAAE,gBAAgB,IAAI;AAC5B,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,CAAC,OAAO,QAAQ,QAAI,0BAAW,CAACC,QAAO,WAAW;AACpD,WAAO,EAAE,GAAGA,QAAO,GAAG,OAAO;AAAA,EACjC,GAAG,gBAAgB,OAAO,IAAI,CAAC;AAC/B,+BAAU,MAAM;AACZ,UAAM,aAAa,aAAa,OAAO,IAAI;AAC3C,QAAI,eAAe,KAAK,KAAK,eAAe,OAAO;AAE/C,eAAS,UAAU;AAAA,IACvB,WACS,cAAc,OAAO;AAE1B,mBAAa,KAAK;AAClB,eAAS,gBAAgB,OAAO,IAAI,CAAC;AAAA,IACzC;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,UAAU,OAAO,SAAS,CAAC;AAC5C,QAAM,mBAAe,2BAAY,CAAC,UAAUD,WAAU;AAClD,aAAS,EAAE,CAAC,QAAQ,GAAGA,OAAM,CAAC;AAAA,EAClC,GAAG,CAAC,CAAC;AACL,QAAM,mBAAe,2BAAY,CAAC,UAAU;AACxC,UAAM,eAAe;AACrB,QAAI,YAAY,UAAU;AACtB;AAAA,IACJ;AACA,UAAM,YAAY,iBAAgB,oBAAI,KAAK,GAAE,OAAO,GAAG,IAAI;AAC3D,aAAS,aAAa,WAAW,IAAI,CAAC;AAAA,EAC1C,GAAG,CAAC,UAAU,UAAU,IAAI,CAAC;AAC7B,QAAM,kBAAc,2BAAY,CAAC,UAAU;AACvC,UAAM,eAAe;AACrB,QAAI,YAAY,UAAU;AACtB;AAAA,IACJ;AACA,aAAS,MAAS;AAAA,EACtB,GAAG,CAAC,UAAU,UAAU,QAAQ,CAAC;AACjC,aAAQ,qBAAAE,MAAM,MAAM,EAAE,WAAW,eAAe,UAAU,CAAC,oBAAoB,OAAO,MAAM,QAAQ,YAAY,QAAQ,MAAM,EAAE,IAAI,CAAC,WAAWC,WAAO,qBAAAJ,KAAK,MAAM,EAAE,WAAW,oBAAoB,cAAU,qBAAAA,KAAK,aAAa,EAAE,QAAQ,IAAI,MAAY,QAAQ,cAAc,GAAG,WAAW,UAAoB,UAAoB,UAAoB,QAAgB,SAAkB,WAAW,aAAaI,OAAM,EAAE,CAAC,EAAE,GAAGA,EAAC,CAAE,IAAI,QAAQ,kBAAkB,cAAc,CAAC,QAAQ,gBAAgB,aAAU,qBAAAJ,KAAK,MAAM,EAAE,WAAW,oBAAoB,cAAU,qBAAAA,KAAK,KAAK,EAAE,MAAM,KAAK,WAAW,wBAAwB,SAAS,cAAc,UAAU,gBAAgB,mBAAmB,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAK,QAAQ,oBAAoB,cAAc,CAAC,QAAQ,kBAAkB,aAAU,qBAAAA,KAAK,MAAM,EAAE,WAAW,oBAAoB,cAAU,qBAAAA,KAAK,KAAK,EAAE,MAAM,KAAK,WAAW,6BAA6B,SAAS,aAAa,UAAU,gBAAgB,mBAAmB,UAAU,EAAE,CAAC,EAAE,CAAC,CAAE,EAAE,CAAC;AAC19B;AACA,IAAO,wBAAQ;;;ACpDf,IAAAK,uBAA4B;AAM5B,SAAS,kBAAkB,EAAE,OAAO,MAAM,GAAG,MAAM,GAAG;AAClD,QAAM,EAAE,eAAAC,eAAc,IAAI,MAAM,SAAS;AACzC,aAAO,qBAAAC,KAAKD,gBAAe,EAAE,MAAY,GAAG,MAAM,CAAC;AACvD;AACA,IAAO,4BAAQ;;;ACVf,IAAAE,uBAA2C;AAC3C,IAAAC,gBAA4B;AAO5B,SAAS,eAAe,EAAE,QAAQ,UAAU,SAAS,IAAI,OAAO,UAAU,UAAU,OAAO,WAAW,YAAY,OAAO,QAAQ,SAAS,UAAU,SAAU,GAAG;AAC7J,QAAM,2BAA2B,YAAY,4BAA4B,UAAU,OAAO;AAI1F,QAAM,WAAW,wBAAwB,MAAM;AAC/C,QAAM,mBAAe,2BAAY,CAAC,UAAU,SAAS,MAAM,OAAO,OAAO,GAAG,CAAC,QAAQ,CAAC;AACtF,QAAM,iBAAa,2BAAY,CAAC,UAAU,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;AACxF,QAAM,kBAAc,2BAAY,CAAC,UAAU,QAAQ,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,SAAS,EAAE,CAAC;AAC3F,QAAM,cAAc,QAAQ,eAAe,OAAO;AAClD,aAAQ,qBAAAC,MAAM,OAAO,EAAE,WAAW,YAAY,YAAY,WAAW,aAAa,EAAE,IAAI,UAAU,CAAC,CAAC,aAAa,CAAC,CAAC,mBAAgB,qBAAAC,KAAK,0BAA0B,EAAE,IAAI,cAAc,EAAE,GAAG,aAA0B,QAAgB,UAAoB,SAAmB,CAAC,OAAI,qBAAAD,MAAM,SAAS,EAAE,UAAU,KAAC,qBAAAC,KAAK,SAAS,EAAE,MAAM,YAAY,IAAQ,MAAM,IAAI,SAAS,OAAO,UAAU,cAAc,QAAQ,OAAO,UAAoB,UAAU,YAAY,UAAU,WAAW,WAAW,UAAU,cAAc,QAAQ,YAAY,SAAS,aAAa,oBAAoB,mBAAmB,EAAE,EAAE,CAAC,GAAG,eAAW,qBAAAA,KAAK,QAAQ,EAAE,UAAU,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACtpB;AACA,IAAO,yBAAQ;;;ACpBf,IAAAC,uBAA2C;AAC3C,IAAAC,iBAA4B;AAO5B,SAAS,iBAAiB,EAAE,IAAI,UAAU,SAAS,EAAE,SAAS,OAAO,aAAa,cAAc,WAAW,GAAG,OAAO,YAAY,OAAO,UAAU,UAAU,QAAQ,QAAS,GAAG;AAC5K,QAAM,mBAAmB,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9D,QAAM,iBAAa,4BAAY,CAAC,EAAE,OAAO,MAAM,OAAO,IAAI,yBAAyB,UAAU,OAAO,OAAO,aAAa,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;AAClJ,QAAM,kBAAc,4BAAY,CAAC,EAAE,OAAO,MAAM,QAAQ,IAAI,yBAAyB,UAAU,OAAO,OAAO,aAAa,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;AACrJ,aAAQ,qBAAAC,KAAK,OAAO,EAAE,WAAW,cAAc,IAAQ,UAAU,MAAM,QAAQ,WAAW,KAClF,YAAY,IAAI,CAAC,QAAQ,UAAU;AAC/B,UAAM,UAAU,sBAAsB,OAAO,OAAO,gBAAgB;AACpE,UAAM,eAAe,MAAM,QAAQ,YAAY,KAAK,aAAa,QAAQ,OAAO,KAAK,MAAM;AAC3F,UAAM,cAAc,YAAY,gBAAgB,WAAW,aAAa;AACxE,UAAM,eAAe,CAAC,UAAU;AAC5B,UAAI,MAAM,OAAO,SAAS;AACtB,iBAAS,uBAAuB,OAAO,kBAAkB,WAAW,CAAC;AAAA,MACzE,OACK;AACD,iBAAS,yBAAyB,OAAO,kBAAkB,WAAW,CAAC;AAAA,MAC3E;AAAA,IACJ;AACA,UAAM,eAAY,qBAAAC,MAAM,QAAQ,EAAE,UAAU,KAAC,qBAAAD,KAAK,SAAS,EAAE,MAAM,YAAY,IAAI,SAAS,IAAI,KAAK,GAAG,MAAM,IAAI,SAAkB,OAAO,OAAO,KAAK,GAAG,UAAU,YAAY,gBAAgB,UAAU,WAAW,aAAa,UAAU,GAAG,UAAU,cAAc,QAAQ,YAAY,SAAS,aAAa,oBAAoB,mBAAmB,EAAE,EAAE,CAAC,OAAG,qBAAAA,KAAK,QAAQ,EAAE,UAAU,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC;AAC3Y,WAAO,aAAU,qBAAAA,KAAK,SAAS,EAAE,WAAW,mBAAmB,WAAW,IAAI,UAAU,SAAS,GAAG,KAAK,QAAM,qBAAAA,KAAK,OAAO,EAAE,WAAW,YAAY,WAAW,IAAI,cAAU,qBAAAA,KAAK,SAAS,EAAE,UAAU,SAAS,CAAC,EAAE,GAAG,KAAK;AAAA,EAC/N,CAAC,EAAE,CAAC;AAChB;AACA,IAAO,2BAAQ;;;AC7Bf,IAAAE,uBAA4B;AAOb,SAAR,YAA6B,OAAO;AACvC,QAAM,EAAE,UAAU,UAAU,SAAS,SAAS,IAAI;AAClD,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,SAAS,GAAG,OAAO,UAAU,YAAY,SAAS,CAAC;AAC9F;;;ACXA,IAAAE,uBAA4B;AAC5B,IAAAC,iBAA4B;AAOb,SAAR,WAA4B,OAAO;AACtC,QAAM,EAAE,UAAU,SAAS,SAAS,IAAI;AACxC,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,QAAM,mBAAe,4BAAY,CAAC,UAAU,SAAS,SAAS,MAAS,GAAG,CAAC,QAAQ,CAAC;AACpF,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,QAAQ,GAAG,OAAO,UAAU,aAAa,CAAC;AACrF;;;ACbA,IAAAE,uBAA4B;AAOb,SAAR,eAAgC,OAAO;AAC1C,QAAM,EAAE,UAAU,OAAO,SAAS,SAAS,IAAI;AAC/C,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAQ,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,kBAAkB,GAAG,OAAO,OAAO,WAAW,KAAK,GAAG,UAAU,CAACE,WAAU,SAAS,WAAWA,MAAK,CAAC,EAAE,CAAC;AACpJ;;;ACXA,IAAAC,uBAA4B;AAMb,SAAR,YAA6B,OAAO;AACvC,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC;AAC9D;;;ACVA,IAAAE,uBAAkE;AAClE,IAAAC,iBAAqC;AAGrC,SAAS,iBAAiB,SAAS,MAAM;AACrC,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX;AACA,SAAO,QAAQ,QAAQ,WAAW,SAAS,mBAAmB,IAAI,CAAC,SAAS;AAChF;AACA,SAAS,YAAY,MAAM;AACvB,QAAM,EAAE,MAAM,MAAM,KAAK,IAAI;AAC7B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAAS,IAAI,OAAO,WAAW;AACrC,WAAO,UAAU;AACjB,WAAO,SAAS,CAAC,UAAU;AAfnC;AAgBY,UAAI,SAAO,WAAM,WAAN,mBAAc,YAAW,UAAU;AAC1C,gBAAQ;AAAA,UACJ,SAAS,iBAAiB,MAAM,OAAO,QAAQ,IAAI;AAAA,UACnD;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,OACK;AACD,gBAAQ;AAAA,UACJ,SAAS;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,cAAc,IAAI;AAAA,EAC7B,CAAC;AACL;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,QAAQ,IAAI,MAAM,KAAK,KAAK,EAAE,IAAI,WAAW,CAAC;AACzD;AACA,SAAS,gBAAgB,EAAE,UAAU,SAAU,GAAG;AAC9C,QAAM,EAAE,gBAAgB,IAAI;AAC5B,QAAM,EAAE,SAAS,MAAM,KAAK,IAAI;AAChC,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AAIA,MAAI,CAAC,cAAc,WAAW,EAAE,SAAS,IAAI,GAAG;AAC5C,eAAO,qBAAAC,KAAK,OAAO,EAAE,KAAK,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG,WAAW,eAAe,CAAC;AAAA,EAC/F;AAEA,aAAQ,qBAAAC,MAAM,qBAAAC,UAAW,EAAE,UAAU,CAAC,SAAK,qBAAAF,KAAK,KAAK,EAAE,UAAU,WAAW,IAAI,IAAI,MAAM,SAAS,WAAW,iBAAiB,UAAU,gBAAgB,mBAAmB,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC;AACnM;AACA,SAAS,UAAU,EAAE,WAAW,UAAU,SAAS,UAAU,QAAS,GAAG;AACrE,MAAI,UAAU,WAAW,GAAG;AACxB,WAAO;AAAA,EACX;AACA,QAAM,EAAE,gBAAgB,IAAI;AAC5B,QAAM,EAAE,cAAAG,cAAa,IAAI,YAAY,mBAAmB,UAAU,OAAO;AACzE,aAAQ,qBAAAH,KAAK,MAAM,EAAE,WAAW,aAAa,UAAU,UAAU,IAAI,CAAC,UAAU,QAAQ;AAChF,UAAM,EAAE,MAAM,MAAM,KAAK,IAAI;AAC7B,UAAM,eAAe,MAAM,SAAS,GAAG;AACvC,eAAQ,qBAAAC,MAAM,MAAM,EAAE,UAAU,KAAC,qBAAAD,KAAK,sBAAU,EAAE,UAAU,gBAAgB,mBAAmB,WAAW,CAAC,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,eAAW,qBAAAA,KAAK,iBAAiB,EAAE,UAAoB,SAAmB,CAAC,OAAG,qBAAAA,KAAKG,eAAc,EAAE,SAAS,cAAc,SAAmB,CAAC,CAAC,EAAE,GAAG,GAAG;AAAA,EACrS,CAAC,EAAE,CAAC;AACZ;AACA,SAAS,gBAAgB,UAAU;AAC/B,SAAO,SAAS,OAAO,CAAC,KAAK,YAAY;AACrC,QAAI,CAAC,SAAS;AACV,aAAO;AAAA,IACX;AACA,QAAI;AACA,YAAM,EAAE,MAAM,KAAK,IAAI,cAAc,OAAO;AAC5C,aAAO;AAAA,QACH,GAAG;AAAA,QACH;AAAA,UACI;AAAA,UACA;AAAA,UACA,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,QACf;AAAA,MACJ;AAAA,IACJ,SACOC,IAAG;AAEN,aAAO;AAAA,IACX;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AAKA,SAAS,WAAW,OAAO;AACvB,QAAM,EAAE,UAAU,UAAU,UAAU,UAAU,UAAU,OAAO,SAAS,SAAS,IAAI;AACvF,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,QAAM,mBAAe,4BAAY,CAAC,UAAU;AACxC,QAAI,CAAC,MAAM,OAAO,OAAO;AACrB;AAAA,IACJ;AAIA,iBAAa,MAAM,OAAO,KAAK,EAAE,KAAK,CAAC,mBAAmB;AACtD,YAAM,WAAW,eAAe,IAAI,CAAC,aAAa,SAAS,OAAO;AAClE,UAAI,UAAU;AACV,iBAAS,MAAM,OAAO,QAAQ,CAAC;AAAA,MACnC,OACK;AACD,iBAAS,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL,GAAG,CAAC,UAAU,OAAO,QAAQ,CAAC;AAC9B,QAAM,gBAAY,wBAAQ,MAAM,gBAAgB,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAChG,QAAM,aAAS,4BAAY,CAAC,UAAU;AAClC,QAAI,UAAU;AACV,YAAM,WAAW,MAAM,OAAO,CAACC,IAAGC,OAAMA,OAAM,KAAK;AACnD,eAAS,QAAQ;AAAA,IACrB,OACK;AACD,eAAS,MAAS;AAAA,IACtB;AAAA,EACJ,GAAG,CAAC,UAAU,OAAO,QAAQ,CAAC;AAC9B,aAAQ,qBAAAN,MAAM,OAAO,EAAE,UAAU,KAAC,qBAAAD,KAAKK,oBAAmB,EAAE,GAAG,OAAO,UAAU,YAAY,UAAU,MAAM,QAAQ,UAAU,QAAQ,QAAQ,UAAU,kBAAkB,cAAc,OAAO,IAAI,QAAQ,QAAQ,SAAS,OAAO,QAAQ,MAAM,IAAI,OAAU,CAAC,OAAG,qBAAAL,KAAK,WAAW,EAAE,WAAsB,UAAU,QAAQ,UAAoB,SAAS,QAAQ,aAAa,QAAiB,CAAC,CAAC,EAAE,CAAC;AACzY;AACA,IAAO,qBAAQ;;;AC7Hf,IAAAQ,uBAA4B;AAM5B,SAAS,aAAa,EAAE,IAAI,MAAO,GAAG;AAClC,aAAO,qBAAAC,KAAK,SAAS,EAAE,MAAM,UAAU,IAAQ,MAAM,IAAI,OAAO,OAAO,UAAU,cAAc,KAAK,MAAM,CAAC;AAC/G;AACA,IAAO,uBAAQ;;;ACTf,IAAAC,uBAA4B;AAMb,SAAR,eAAgC,OAAO;AAC1C,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC;AACjE;;;ACVA,IAAAE,uBAA2C;AAC3C,IAAAC,iBAA4B;AAO5B,SAAS,YAAY,EAAE,SAAS,OAAO,UAAU,UAAU,UAAU,YAAY,OAAO,QAAQ,SAAS,UAAU,GAAI,GAAG;AACtH,QAAM,EAAE,aAAa,cAAc,QAAQ,WAAW,IAAI;AAC1D,QAAM,iBAAa,4BAAY,CAAC,EAAE,OAAO,MAAM,OAAO,IAAI,yBAAyB,UAAU,OAAO,OAAO,aAAa,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;AAClJ,QAAM,kBAAc,4BAAY,CAAC,EAAE,OAAO,MAAM,QAAQ,IAAI,yBAAyB,UAAU,OAAO,OAAO,aAAa,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;AACrJ,aAAQ,qBAAAC,KAAK,OAAO,EAAE,WAAW,qBAAqB,IAAQ,UAAU,MAAM,QAAQ,WAAW,KACzF,YAAY,IAAI,CAAC,QAAQC,OAAM;AAC3B,UAAM,UAAU,sBAAsB,OAAO,OAAO,KAAK;AACzD,UAAM,eAAe,MAAM,QAAQ,YAAY,KAAK,aAAa,QAAQ,OAAO,KAAK,MAAM;AAC3F,UAAM,cAAc,YAAY,gBAAgB,WAAW,aAAa;AACxE,UAAM,eAAe,MAAM,SAAS,OAAO,KAAK;AAChD,UAAM,YAAS,qBAAAC,MAAM,QAAQ,EAAE,UAAU,KAAC,qBAAAF,KAAK,SAAS,EAAE,MAAM,SAAS,IAAI,SAAS,IAAIC,EAAC,GAAG,SAAkB,MAAM,IAAI,UAAoB,OAAO,OAAOA,EAAC,GAAG,UAAU,YAAY,gBAAgB,UAAU,WAAW,aAAaA,OAAM,GAAG,UAAU,cAAc,QAAQ,YAAY,SAAS,aAAa,oBAAoB,mBAAmB,EAAE,EAAE,CAAC,OAAG,qBAAAD,KAAK,QAAQ,EAAE,UAAU,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC;AAC7Y,WAAO,aAAU,qBAAAA,KAAK,SAAS,EAAE,WAAW,gBAAgB,WAAW,IAAI,UAAU,MAAM,GAAGC,EAAC,QAAM,qBAAAD,KAAK,OAAO,EAAE,WAAW,SAAS,WAAW,IAAI,cAAU,qBAAAA,KAAK,SAAS,EAAE,UAAU,MAAM,CAAC,EAAE,GAAGC,EAAC;AAAA,EAC3M,CAAC,EAAE,CAAC;AAChB;AACA,IAAO,sBAAQ;;;ACtBf,IAAAE,uBAA2C;AAM5B,SAAR,YAA6B,OAAO;AACvC,QAAM,EAAE,OAAO,UAAU,EAAE,WAAW,EAAE,mBAAAC,mBAAkB,EAAG,EAAG,IAAI;AACpE,aAAQ,qBAAAC,MAAM,OAAO,EAAE,WAAW,uBAAuB,UAAU,KAAC,qBAAAC,KAAKF,oBAAmB,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,OAAG,qBAAAE,KAAK,QAAQ,EAAE,WAAW,cAAc,UAAU,MAAM,CAAC,CAAC,EAAE,CAAC;AAC3L;;;ACTA,IAAAC,uBAA2C;AAC3C,IAAAC,iBAA4B;AAE5B,SAAS,SAAS,OAAO,UAAU;AAC/B,MAAI,UAAU;AACV,WAAO,MAAM,KAAK,MAAM,OAAO,OAAO,EACjC,MAAM,EACN,OAAO,CAACC,OAAMA,GAAE,QAAQ,EACxB,IAAI,CAACA,OAAMA,GAAE,KAAK;AAAA,EAC3B;AACA,SAAO,MAAM,OAAO;AACxB;AAMA,SAAS,aAAa,EAAE,QAAQ,IAAI,SAAS,OAAO,UAAU,UAAU,UAAU,WAAW,OAAO,YAAY,OAAO,UAAU,QAAQ,SAAS,YAAa,GAAG;AAC9J,QAAM,EAAE,aAAa,cAAc,YAAY,YAAY,IAAI;AAC/D,QAAM,aAAa,WAAW,CAAC,IAAI;AACnC,QAAM,kBAAc,4BAAY,CAAC,UAAU;AACvC,UAAM,WAAW,SAAS,OAAO,QAAQ;AACzC,WAAO,QAAQ,IAAI,yBAAyB,UAAU,aAAa,WAAW,CAAC;AAAA,EACnF,GAAG,CAAC,SAAS,IAAI,QAAQ,UAAU,aAAa,WAAW,CAAC;AAC5D,QAAM,iBAAa,4BAAY,CAAC,UAAU;AACtC,UAAM,WAAW,SAAS,OAAO,QAAQ;AACzC,WAAO,OAAO,IAAI,yBAAyB,UAAU,aAAa,WAAW,CAAC;AAAA,EAClF,GAAG,CAAC,QAAQ,IAAI,QAAQ,UAAU,aAAa,WAAW,CAAC;AAC3D,QAAM,mBAAe,4BAAY,CAAC,UAAU;AACxC,UAAM,WAAW,SAAS,OAAO,QAAQ;AACzC,WAAO,SAAS,yBAAyB,UAAU,aAAa,WAAW,CAAC;AAAA,EAChF,GAAG,CAAC,UAAU,QAAQ,UAAU,aAAa,WAAW,CAAC;AACzD,QAAM,kBAAkB,yBAAyB,OAAO,aAAa,QAAQ;AAC7E,QAAM,wBAAwB,CAAC,YAAY,OAAO,YAAY;AAC9D,aAAQ,qBAAAC,MAAM,UAAU,EAAE,IAAQ,MAAM,IAAI,UAAoB,WAAW,gBAAgB,OAAO,OAAO,oBAAoB,cAAc,aAAa,iBAAiB,UAAoB,UAAU,YAAY,UAAU,WAAW,WAAW,QAAQ,YAAY,SAAS,aAAa,UAAU,cAAc,oBAAoB,mBAAmB,EAAE,GAAG,UAAU,CAAC,6BAAyB,qBAAAC,KAAK,UAAU,EAAE,OAAO,IAAI,UAAU,YAAY,CAAC,GAAG,MAAM,QAAQ,WAAW,KACxc,YAAY,IAAI,CAAC,EAAE,OAAAC,QAAO,MAAM,GAAGC,OAAM;AACrC,UAAMC,YAAW,gBAAgB,aAAa,QAAQF,MAAK,MAAM;AACjE,eAAQ,qBAAAD,KAAK,UAAU,EAAE,OAAO,OAAOE,EAAC,GAAG,UAAUC,WAAU,UAAU,MAAM,GAAGD,EAAC;AAAA,EACvF,CAAC,CAAC,EAAE,CAAC;AACrB;AACA,IAAO,uBAAQ;;;ACxCf,IAAAE,uBAA4B;AAC5B,IAAAC,iBAA4B;AAM5B,SAAS,eAAe,EAAE,IAAI,UAAU,CAAC,GAAG,aAAa,OAAO,UAAU,UAAU,UAAU,YAAY,OAAO,UAAU,QAAQ,QAAS,GAAG;AAC3I,QAAM,mBAAe,4BAAY,CAAC,EAAE,QAAQ,EAAE,OAAAC,OAAM,EAAE,MAAM,SAASA,WAAU,KAAK,QAAQ,aAAaA,MAAK,GAAG,CAAC,UAAU,QAAQ,UAAU,CAAC;AAC/I,QAAM,iBAAa,4BAAY,CAAC,EAAE,OAAO,MAAM,OAAO,IAAI,UAAU,OAAO,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC;AAC/F,QAAM,kBAAc,4BAAY,CAAC,EAAE,OAAO,MAAM,QAAQ,IAAI,UAAU,OAAO,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC;AAClG,aAAQ,qBAAAC,KAAK,YAAY,EAAE,IAAQ,MAAM,IAAI,WAAW,gBAAgB,OAAO,QAAQ,QAAQ,IAAI,aAA0B,UAAoB,UAAoB,UAAU,UAAU,WAAW,WAAW,MAAM,QAAQ,MAAM,QAAQ,YAAY,SAAS,aAAa,UAAU,cAAc,oBAAoB,mBAAmB,EAAE,EAAE,CAAC;AACrV;AACA,eAAe,eAAe;AAAA,EAC1B,WAAW;AAAA,EACX,SAAS,CAAC;AACd;AACA,IAAO,yBAAQ;;;ACjBf,IAAAC,uBAA4B;AAMb,SAAR,WAA4B,OAAO;AACtC,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,GAAG,MAAM,CAAC;AAC/C;;;ACVA,IAAAE,uBAA4B;AAC5B,IAAAC,iBAA4B;AAOb,SAAR,WAA4B,OAAO;AACtC,QAAM,EAAE,UAAU,SAAS,SAAS,IAAI;AACxC,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,QAAM,mBAAe,4BAAY,CAAC,UAAU,SAAS,QAAQ,GAAG,KAAK,QAAQ,MAAS,GAAG,CAAC,QAAQ,CAAC;AACnG,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,QAAQ,GAAG,OAAO,UAAU,aAAa,CAAC;AACrF;;;ACbA,IAAAE,uBAA4B;AAMb,SAAR,UAA2B,OAAO;AACrC,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC;AAC5D;;;ACVA,IAAAE,uBAA4B;AAMb,SAAR,aAA8B,OAAO;AACxC,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,QAAMC,qBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,aAAO,qBAAAC,KAAKD,oBAAmB,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC;AAC/D;;;ACSA,SAAS,UAAU;AACf,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAO,kBAAQ;;;AClCA,SAAR,qBAAsC;AACzC,SAAO;AAAA,IACH,QAAQ,eAAO;AAAA,IACf,WAAW,kBAAU;AAAA,IACrB,SAAS,gBAAQ;AAAA,IACjB,YAAY,CAAC;AAAA,IACb,aAAa,CAAC;AAAA,IACd,iBAAiB;AAAA,EACrB;AACJ;;;AxDNA,IAAqB,OAArB,cAAkC,yBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWxC,YAAY,OAAO;AACf,UAAM,KAAK;AARf;AAAA;AAAA;AAAA;AAyOA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAAkB,CAAC,UAAUE,YAAW;AAEpC,UAAIA,QAAO,WAAW,KAAK,OAAO,aAAa,UAAU;AACrD,eAAO;AAAA,MACX;AAEA,YAAM,OAAO,aAAM,UAAUA,OAAM;AACnC,UAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,eAAO,OAAO,KAAK,IAAI,EAAE,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;AAAA,MACnD;AACA,aAAO;AAAA,IACX;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAAgB,CAAC,YAAY,aAAa;AACtC,YAAM,cAAc,CAAC,MAAM,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM;AAClD,eAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AAC/B,cAAI,OAAO,KAAK,GAAG,MAAM,UAAU;AAC/B,kBAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,GAAG,CAAC;AAEnD,gBAAI,KAAK,GAAG,EAAE,+BAA+B,KAAK,KAAK,GAAG,EAAE,QAAQ,MAAM,IAAI;AAC1E,kBAAI,KAAK,KAAK,GAAG,EAAE,QAAQ,CAAC;AAAA,YAChC,OACK;AACD,0BAAY,KAAK,GAAG,GAAG,KAAK,QAAQ;AAAA,YACxC;AAAA,UACJ,WACS,QAAQ,YAAY,KAAK,GAAG,MAAM,IAAI;AAC3C,kBAAM,QAAQ,CAAC,SAAS;AACpB,oBAAM,YAAY,YAAK,UAAU,IAAI;AAGrC,kBAAI,OAAO,cAAc,YACrB,gBAAS,SAAS,KACjB,MAAM,QAAQ,SAAS,KAAK,UAAU,MAAM,CAAC,QAAQ,OAAO,QAAQ,QAAQ,GAAI;AACjF,oBAAI,KAAK,IAAI;AAAA,cACjB;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,aAAO,YAAY,UAAU;AAAA,IACjC;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAAgB,CAAC,aAAa;AAC1B,YAAM,EAAE,QAAQ,YAAY,IAAI,KAAK;AACrC,YAAM,kBAAkB,YAAY,eAAe,QAAQ,QAAQ;AACnE,YAAM,aAAa,YAAY,aAAa,iBAAiB,IAAI,QAAQ;AACzE,YAAM,aAAa,KAAK,cAAc,YAAY,QAAQ;AAC1D,YAAM,cAAc,KAAK,gBAAgB,UAAU,UAAU;AAC7D,aAAO;AAAA,IACX;AAsDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAW,CAAC,UAAU,gBAAgB,OAAO;AACzC,YAAM,EAAE,aAAa,eAAe,UAAU,YAAY,cAAc,SAAS,IAAI,KAAK;AAC1F,YAAM,EAAE,aAAa,OAAO,IAAI,KAAK;AACrC,UAAI,kBAAkB,KAAK,MAAM;AACjC,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,QAAQ,GAAG;AAC/C,cAAM,WAAW,KAAK,kBAAkB,KAAK,OAAO,QAAQ;AAC5D,mBAAW,SAAS;AACpB,0BAAkB,SAAS;AAAA,MAC/B;AACA,YAAM,eAAe,CAAC,cAAc;AACpC,UAAI,QAAQ,EAAE,UAAU,OAAO;AAC/B,UAAI,cAAc;AAClB,UAAI,kBAAkB,QAAQ,aAAa,MAAM;AAC7C,sBAAc,KAAK,cAAc,QAAQ;AACzC,gBAAQ;AAAA,UACJ,UAAU;AAAA,QACd;AAAA,MACJ;AACA,UAAI,cAAc;AACd,cAAM,mBAAmB,KAAK,SAAS,aAAa,QAAQ,aAAa,eAAe;AACxF,YAAI,SAAS,iBAAiB;AAC9B,YAAI,cAAc,iBAAiB;AACnC,cAAM,yBAAyB;AAC/B,cAAM,8BAA8B;AACpC,YAAI,aAAa;AACb,gBAAM,SAAS,oBAAoB,kBAAkB,WAAW;AAChE,wBAAc,OAAO;AACrB,mBAAS,OAAO;AAAA,QACpB;AAEA,YAAI,gBAAgB;AAChB,gBAAM,iBAAiB,KAAK,0BAA0B,gBAAgB,iBAAiB,WAAW;AAClG,wBAAc,aAAa,aAAa,gBAAgB,mBAAmB;AAAA,QAC/E;AACA,gBAAQ;AAAA,UACJ,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,WACS,CAAC,cAAc,gBAAgB;AACpC,cAAM,cAAc,cACd,aAAa,gBAAgB,aAAa,mBAAmB,IAC7D;AACN,gBAAQ;AAAA,UACJ,UAAU;AAAA,UACV;AAAA,UACA,QAAQ,YAAY,WAAW;AAAA,QACnC;AAAA,MACJ;AACA,WAAK,SAAS,OAAO,MAAM,YAAY,SAAS,EAAE,GAAG,KAAK,OAAO,GAAG,MAAM,GAAG,EAAE,CAAC;AAAA,IACpF;AAoBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAAQ,MAAM;AACV,YAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,YAAM,WAAW,KAAK,kBAAkB,KAAK,OAAO,MAAS;AAC7D,YAAM,cAAc,SAAS;AAC7B,YAAM,QAAQ;AAAA,QACV,UAAU;AAAA,QACV,aAAa,CAAC;AAAA,QACd,QAAQ,CAAC;AAAA,QACT,wBAAwB,CAAC;AAAA,QACzB,6BAA6B,CAAC;AAAA,MAClC;AACA,WAAK,SAAS,OAAO,MAAM,YAAY,SAAS,EAAE,GAAG,KAAK,OAAO,GAAG,MAAM,CAAC,CAAC;AAAA,IAChF;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAAS,CAAC,IAAI,SAAS;AACnB,YAAM,EAAE,OAAO,IAAI,KAAK;AACxB,UAAI,QAAQ;AACR,eAAO,IAAI,IAAI;AAAA,MACnB;AAAA,IACJ;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCAAU,CAAC,IAAI,SAAS;AACpB,YAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,UAAI,SAAS;AACT,gBAAQ,IAAI,IAAI;AAAA,MACpB;AAAA,IACJ;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAW,CAAC,UAAU;AAClB,YAAM,eAAe;AACrB,UAAI,MAAM,WAAW,MAAM,eAAe;AACtC;AAAA,MACJ;AACA,YAAM,QAAQ;AACd,YAAM,EAAE,eAAe,aAAa,YAAY,SAAS,IAAI,KAAK;AAClE,UAAI,EAAE,UAAU,YAAY,IAAI,KAAK;AACrC,UAAI,kBAAkB,MAAM;AACxB,sBAAc,KAAK,cAAc,WAAW;AAAA,MAChD;AACA,UAAI,cAAc,KAAK,yBAAyB,WAAW,GAAG;AAG1D,cAAM,cAAc,eAAe,CAAC;AACpC,cAAM,SAAS,cAAc,YAAY,WAAW,IAAI,CAAC;AACzD,aAAK,SAAS;AAAA,UACV,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA,wBAAwB,CAAC;AAAA,UACzB,6BAA6B,CAAC;AAAA,QAClC,GAAG,MAAM;AACL,cAAI,UAAU;AACV,qBAAS,EAAE,GAAG,KAAK,OAAO,UAAU,aAAa,QAAQ,YAAY,GAAG,KAAK;AAAA,UACjF;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAyBA;AAAA,kCAAS,MAAM;AACX,UAAI,KAAK,YAAY,SAAS;AAC1B,cAAM,oBAAoB,IAAI,YAAY,UAAU;AAAA,UAChD,YAAY;AAAA,QAChB,CAAC;AACD,0BAAkB,eAAe;AACjC,aAAK,YAAY,QAAQ,cAAc,iBAAiB;AACxD,aAAK,YAAY,QAAQ,cAAc;AAAA,MAC3C;AAAA,IACJ;AAuCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oDAA2B,CAAC,aAAa;AACrC,YAAM,EAAE,aAAa,wBAAwB,mBAAmB,QAAQ,IAAI,KAAK;AACjF,YAAM,EAAE,QAAQ,WAAW,IAAI,KAAK;AACpC,YAAM,mBAAmB,KAAK,SAAS,QAAQ;AAC/C,UAAI,SAAS,iBAAiB;AAC9B,UAAI,cAAc,iBAAiB;AACnC,YAAM,yBAAyB;AAC/B,YAAM,8BAA8B;AACpC,YAAM,WAAW,OAAO,SAAS,KAAM,eAAe;AACtD,UAAI,UAAU;AACV,YAAI,aAAa;AACb,gBAAM,SAAS,oBAAoB,kBAAkB,WAAW;AAChE,wBAAc,OAAO;AACrB,mBAAS,OAAO;AAAA,QACpB;AACA,YAAI,mBAAmB;AACnB,cAAI,OAAO,sBAAsB,YAAY;AACzC,8BAAkB,OAAO,CAAC,CAAC;AAAA,UAC/B,OACK;AACD,iBAAK,aAAa,OAAO,CAAC,CAAC;AAAA,UAC/B;AAAA,QACJ;AACA,aAAK,SAAS;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,GAAG,MAAM;AACL,cAAI,SAAS;AACT,oBAAQ,MAAM;AAAA,UAClB,OACK;AACD,oBAAQ,MAAM,0BAA0B,MAAM;AAAA,UAClD;AAAA,QACJ,CAAC;AAAA,MACL,WACS,WAAW,SAAS,GAAG;AAC5B,aAAK,SAAS;AAAA,UACV,QAAQ,CAAC;AAAA,UACT,aAAa,CAAC;AAAA,UACd,wBAAwB,CAAC;AAAA,UACzB,6BAA6B,CAAC;AAAA,QAClC,CAAC;AAAA,MACL;AACA,aAAO,CAAC;AAAA,IACZ;AA1lBI,QAAI,CAAC,MAAM,WAAW;AAClB,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC5E;AACA,SAAK,QAAQ,KAAK,kBAAkB,OAAO,MAAM,QAAQ;AACzD,QAAI,KAAK,MAAM,YAAY,CAAC,WAAW,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,GAAG;AAC9E,WAAK,MAAM,SAAS,KAAK,KAAK;AAAA,IAClC;AACA,SAAK,kBAAc,0BAAU;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,wBAAwB,WAAW,WAAW;AAC1C,QAAI,CAAC,WAAW,KAAK,OAAO,SAAS,GAAG;AACpC,YAAM,wBAAwB,iBAAiB,KAAK,MAAM,UAAU,UAAU,QAAQ;AACtF,YAAM,kBAAkB,CAAC,WAAW,UAAU,QAAQ,KAAK,MAAM,MAAM;AAGvE,YAAM,oBAAoB,sBAAsB,SAAS,KAAK,CAAC,WAAW,UAAU,UAAU,KAAK,MAAM,QAAQ;AACjH,YAAM,YAAY,KAAK;AAAA,QAAkB,KAAK;AAAA,QAAO,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA,QAIhE,mBAAmB,oBAAoB,SAAY,KAAK,MAAM;AAAA,QAAiB;AAAA,QAAiB;AAAA,MAAqB;AACrH,YAAM,eAAe,CAAC,WAAW,WAAW,SAAS;AACrD,aAAO,EAAE,WAAW,aAAa;AAAA,IACrC;AACA,WAAO,EAAE,cAAc,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,mBAAmBC,IAAG,WAAW,UAAU;AACvC,QAAI,SAAS,cAAc;AACvB,YAAM,EAAE,UAAU,IAAI;AACtB,UAAI,CAAC,WAAW,UAAU,UAAU,KAAK,MAAM,QAAQ,KACnD,CAAC,WAAW,UAAU,UAAU,UAAU,QAAQ,KAClD,KAAK,MAAM,UAAU;AACrB,aAAK,MAAM,SAAS,SAAS;AAAA,MACjC;AACA,WAAK,SAAS,SAAS;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,kBAAkB,OAAO,eAAe,iBAAiB,kBAAkB,OAAO,wBAAwB,CAAC,GAAG;AAzGlH;AA0GQ,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,UAAM,SAAS,YAAY,QAAQ,MAAM,SAAS,KAAK,MAAM;AAC7D,UAAM,YAAY,cAAc,QAAQ,MAAM,WAAW,KAAK,MAAM,aAAa,CAAC;AAClF,UAAM,OAAO,OAAO,kBAAkB;AACtC,UAAM,eAAe,kBAAkB,QAAQ,MAAM,eAAe,KAAK,MAAM;AAC/E,UAAM,eAAe,QAAQ,CAAC,MAAM,cAAc;AAClD,UAAM,aAAa;AACnB,UAAM,wCAAwC,2CAA2C,QACnF,MAAM,wCACN,KAAK,MAAM;AACjB,UAAM,gCAAgC,mCAAmC,QACnE,MAAM,gCACN,KAAK,MAAM;AACjB,QAAI,cAAc,MAAM;AACxB,QAAI,CAAC,eACD,YAAY,sBAAsB,MAAM,WAAW,YAAY,uCAAuC,6BAA6B,GAAG;AACtI,oBAAc,kBAAkB,MAAM,WAAW,YAAY,uCAAuC,6BAA6B;AAAA,IACrI;AACA,UAAM,WAAW,YAAY,oBAAoB,QAAQ,aAAa;AACtE,UAAM,mBAAmB,KAAK,sBAAsB,mBAAmB,YAAY,eAAe,QAAQ,QAAQ,CAAC;AACnH,UAAM,mBAAmB,MAAM;AAE3B,UAAI,MAAM,cAAc,iBAAiB;AACrC,eAAO,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE;AAAA,MACzC,WACS,CAAC,MAAM,cAAc;AAC1B,eAAO;AAAA,UACH,QAAQ,MAAM,0BAA0B,CAAC;AAAA,UACzC,aAAa,MAAM,+BAA+B,CAAC;AAAA,QACvD;AAAA,MACJ;AACA,aAAO;AAAA,QACH,QAAQ,MAAM,UAAU,CAAC;AAAA,QACzB,aAAa,MAAM,eAAe,CAAC;AAAA,MACvC;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI,yBAAyB,MAAM;AACnC,QAAI,8BAA8B,MAAM;AACxC,QAAI,cAAc;AACd,YAAM,mBAAmB,KAAK,SAAS,UAAU,QAAQ,aAAa,gBAAgB;AACtF,eAAS,iBAAiB;AAG1B,UAAI,oBAAoB,QAAW;AAC/B,sBAAc,iBAAiB;AAAA,MACnC,OACK;AACD,sBAAc,cAAa,UAAK,UAAL,mBAAY,aAAa,iBAAiB,aAAa,mBAAmB;AAAA,MACzG;AACA,+BAAyB;AACzB,oCAA8B;AAAA,IAClC,OACK;AACD,YAAM,gBAAgB,iBAAiB;AACvC,eAAS,cAAc;AACvB,oBAAc,cAAc;AAC5B,UAAI,sBAAsB,SAAS,GAAG;AAClC,cAAM,iBAAiB,sBAAsB,OAAO,CAAC,KAAK,QAAQ;AAC9D,cAAI,GAAG,IAAI;AACX,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AACL,sBAAc,8BAA8B,aAAa,cAAc,aAAa,gBAAgB,mBAAmB;AAAA,MAC3H;AAAA,IACJ;AACA,QAAI,MAAM,aAAa;AACnB,YAAM,SAAS,oBAAoB,EAAE,aAAa,OAAO,GAAG,MAAM,WAAW;AAC7E,oBAAc,OAAO;AACrB,eAAS,OAAO;AAAA,IACpB;AACA,UAAM,WAAW,YAAY,WAAW,kBAAkB,SAAS,gBAAgB,GAAG,UAAU,MAAM,UAAU,MAAM,WAAW;AACjI,UAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,WAAW,WAAW;AACxC,WAAO,aAAa,MAAM,WAAW,SAAS;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kCAAkC;AAC9B,UAAM,EAAE,gBAAgB,SAAS,IAAI,KAAK;AAC1C,UAAM,eAAe,KAAK,MAAM;AAChC,QAAI,uBAAuB,CAAC;AAC5B,QAAI,OAAO,mBAAmB,YAAY;AACtC,YAAM,eAAe,eAAe,cAAc,mBAAmB,YAAY,GAAG,QAAQ;AAC5F,YAAM,kBAAkB,mBAAmB,YAAY;AACvD,6BAAuB;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,UAAU,SAAS,KAAK,MAAM,QAAQ,gBAAgB,iBAAiB;AAC5E,UAAM,cAAc,iBAAiB,iBAAiB,KAAK,MAAM;AACjE,UAAM,EAAE,gBAAgB,iBAAiB,SAAS,IAAI,KAAK;AAC3D,UAAM,iBAAiB,mBAAmB,YAAY,eAAe,QAAQ,QAAQ;AACrF,WAAO,YACF,aAAa,EACb,iBAAiB,UAAU,gBAAgB,gBAAgB,iBAAiB,QAAQ;AAAA,EAC7F;AAAA;AAAA,EAEA,aAAa,UAAU;AACnB,UAAM,EAAE,QAAQ,aAAa,QAAQ,SAAS,IAAI,KAAK;AACvD,UAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,UAAM,UAAU,aAAa,QAAQ;AACrC,UAAM,oBAAoB,YAAY,qBAAqB,UAAU,OAAO;AAC5E,QAAI,UAAU,OAAO,QAAQ;AACzB,iBAAQ,qBAAAC,KAAK,mBAAmB,EAAE,QAAgB,aAAa,eAAe,CAAC,GAAG,QAAgB,UAAoB,aAA0B,SAAmB,CAAC;AAAA,IACxK;AACA,WAAO;AAAA,EACX;AAAA;AAAA,EAmEA,0BAA0B,cAAc,gBAAgB,UAAU;AAC9D,UAAM,EAAE,iBAAiB,YAAY,IAAI,KAAK;AAC9C,UAAM,mBAAmB,kBAAkB;AAC3C,UAAM,aAAa,YAAY,aAAa,kBAAkB,IAAI,QAAQ;AAC1E,UAAM,aAAa,KAAK,cAAc,YAAY,QAAQ;AAC1D,UAAM,iBAAiB,aAAM,cAAc,UAAU;AAErD,SAAI,iDAAgB,UAAS,aAAY,iDAAgB,UAAS,SAAS;AACvE,qBAAe,WAAW,aAAa;AAAA,IAC3C;AACA,UAAM,2BAA2B,KAAK,gCAAgC;AAEtE,UAAM,6BAA6B,CAAC,SAAS,CAAC,GAAG,qBAAqB;AAClE,UAAI,OAAO,WAAW,GAAG;AACrB,eAAO;AAAA,MACX;AACA,aAAO,OAAO,OAAO,CAAC,UAAU;AAC5B,eAAO,CAAC,iBAAiB,SAAS,KAAK;AAAA,MAC3C,CAAC;AAAA,IACL;AAEA,UAAM,yBAAyB,CAAC,QAAQ,+BAA+B,CAAC,MAAM;AAC1E,sBAAS,QAAQ,CAAC,YAAY,aAAa;AACvC,cAAM,+BAA+B,6BAA6B,QAAQ;AAC1E,YAAI,cAAO,UAAU,KAAM,MAAM,QAAQ,UAAU,KAAK,WAAW,WAAW,GAAI;AAC9E,iBAAO,OAAO,QAAQ;AAAA,QAC1B,WACS,SAAS,UAAU,KACxB,SAAS,4BAA4B,KACrC,MAAM,QAAQ,6EAA8B,QAAQ,GAAG;AAEvD,iBAAO,QAAQ,IAAI,2BAA2B,WAAW,UAAU,6BAA6B,QAAQ;AAAA,QAC5G,WACS,OAAO,eAAe,YAAY,CAAC,MAAM,QAAQ,WAAW,QAAQ,GAAG;AAC5E,iCAAuB,YAAY,6BAA6B,QAAQ,CAAC;AAAA,QAC7E;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,WAAO,uBAAuB,gBAAgB,wBAAwB;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2EA,sBAAsB,iBAAiB;AAxa3C;AAyaQ,UAAM,YAAY,WAAW,kBAAiB,UAAK,UAAL,mBAAY,eAAe;AACzE,WAAO,YAAY,KAAK,MAAM,kBAAkB;AAAA,EACpD;AAAA;AAAA,EAkFA,cAAc;AA7flB;AA8fQ,UAAM,EAAE,iBAAiB,uBAAuB,WAAW,CAAC,EAAE,IAAI,KAAK;AACvE,UAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,UAAM,EAAE,QAAAF,SAAQ,WAAAG,YAAW,SAAAC,UAAS,aAAa,gBAAgB,IAAI,mBAAmB;AACxF,WAAO;AAAA,MACH,QAAQ,EAAE,GAAGJ,SAAQ,GAAG,KAAK,MAAM,OAAO;AAAA,MAC1C,WAAW;AAAA,QACP,GAAGG;AAAA,QACH,GAAG,KAAK,MAAM;AAAA,QACd,iBAAiB;AAAA,UACb,GAAGA,WAAU;AAAA,UACb,IAAG,UAAK,MAAM,cAAX,mBAAsB;AAAA,QAC7B;AAAA,MACJ;AAAA,MACA,SAAS,EAAE,GAAGC,UAAS,GAAG,KAAK,MAAM,QAAQ;AAAA,MAC7C,YAAY,KAAK,MAAM;AAAA,MACvB,aAAa,KAAK,MAAM,eAAe;AAAA,MACvC;AAAA,MACA,iBAAiB,yBAAyB;AAAA,MAC1C,iBAAiB,SAAS,qBAAqB;AAAA,IACnD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,aAAa,OAAO;AAChB,UAAM,EAAE,WAAW,QAAQ,cAAc,IAAI,IAAI,KAAK;AACtD,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,OAAO,eAAQ,QAAQ;AAC7B,QAAI,KAAK,CAAC,MAAM,IAAI;AAEhB,WAAK,CAAC,IAAI;AAAA,IACd,OACK;AAED,WAAK,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,YAAY,KAAK,KAAK,WAAW;AACvC,QAAI,QAAQ,KAAK,YAAY,QAAQ,SAAS,SAAS;AACvD,QAAI,CAAC,OAAO;AAER,cAAQ,KAAK,YAAY,QAAQ,cAAc,cAAc,SAAS,GAAG;AAAA,IAC7E;AACA,QAAI,SAAS,MAAM,QAAQ;AAEvB,cAAQ,MAAM,CAAC;AAAA,IACnB;AACA,QAAI,OAAO;AACP,YAAM,MAAM;AAAA,IAChB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4DA,eAAe;AACX,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,QAAI,EAAE,UAAU,YAAY,IAAI,KAAK;AACrC,QAAI,kBAAkB,MAAM;AACxB,oBAAc,KAAK,cAAc,WAAW;AAAA,IAChD;AACA,WAAO,KAAK,yBAAyB,WAAW;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,UAAM,EAAE,UAAU,IAAI,UAAU,aAAa,YAAY,IAAI,SAAS,MAAM,QAAQ,QAAQ,QAAQ,cAAc,SAAS,eAAe,eAAe,kBAAkB,OAAO,UAAU,UAAU,aAAa,gBAAgB,OAAO,qBAAsB,IAAI,KAAK;AACzQ,UAAM,EAAE,QAAQ,UAAU,UAAU,aAAa,SAAS,IAAI,KAAK;AACnE,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,EAAE,aAAa,aAAa,IAAI,SAAS;AAC/C,UAAM,EAAE,cAAAC,cAAa,IAAI,SAAS,UAAU;AAI5C,UAAM,KAAK,uBAAuB,UAAU;AAC5C,UAAM,UAAU,wBAAwB,WAAW;AACnD,QAAI,EAAE,CAAC,sBAAsB,GAAG,gBAAgB,CAAC,EAAE,IAAI,aAAa,QAAQ;AAC5E,QAAI,UAAU;AACV,sBAAgB,EAAE,GAAG,eAAe,OAAO,EAAE,GAAG,cAAc,OAAO,UAAU,KAAK,EAAE;AAAA,IAC1F;AACA,UAAM,iBAAiB,EAAE,CAAC,cAAc,GAAG,EAAE,CAAC,sBAAsB,GAAG,cAAc,EAAE;AACvF,eAAQ,qBAAAC,MAAM,SAAS,EAAE,WAAW,YAAY,YAAY,QAAQ,IAAQ,MAAY,QAAgB,QAAgB,QAAgB,cAA4B,SAAS,SAAS,eAAe,iBAAiB,eAAe,YAAY,iBAAiB,UAAU,KAAK,UAAU,IAAQ,KAAK,KAAK,aAAa,UAAU,CAAC,kBAAkB,SAAS,KAAK,aAAa,QAAQ,OAAG,qBAAAJ,KAAK,cAAc,EAAE,MAAM,IAAI,QAAgB,UAAoB,aAA0B,UAAoB,UAAoB,aAA0B,aAA0B,UAAoB,UAAU,KAAK,UAAU,QAAQ,KAAK,QAAQ,SAAS,KAAK,SAAS,UAAoB,UAAoB,SAAmB,CAAC,GAAG,WAAW,eAAW,qBAAAA,KAAKG,eAAc,EAAE,UAAU,gBAAgB,SAAmB,CAAC,GAAG,kBAAkB,YAAY,KAAK,aAAa,QAAQ,CAAC,EAAE,CAAC;AAAA,EAC12B;AACJ;;;AyDtpBA,IAAAE,uBAA4B;AAC5B,IAAAC,iBAA2B;AAGZ,SAAR,UAA2B,YAAY;AAC1C,aAAO,2BAAW,CAAC,EAAE,QAAAC,SAAQ,SAAAC,UAAS,WAAAC,YAAW,GAAG,YAAY,GAAG,QAAQ;AAL/E;AAMQ,IAAAF,UAAS,EAAE,GAAG,yCAAY,QAAQ,GAAGA,QAAO;AAC5C,IAAAC,WAAU,EAAE,GAAG,yCAAY,SAAS,GAAGA,SAAQ;AAC/C,IAAAC,aAAY;AAAA,MACR,GAAG,yCAAY;AAAA,MACf,GAAGA;AAAA,MACH,iBAAiB;AAAA,QACb,IAAG,8CAAY,cAAZ,mBAAuB;AAAA,QAC1B,GAAGA,cAAA,gBAAAA,WAAW;AAAA,MAClB;AAAA,IACJ;AACA,eAAQ,qBAAAC,KAAK,MAAM,EAAE,GAAG,YAAY,GAAG,aAAa,QAAQH,SAAQ,SAASC,UAAS,WAAWC,YAAW,IAAS,CAAC;AAAA,EAC1H,CAAC;AACL;;;ACdA,IAAO,cAAQ;", "names": ["import_jsx_runtime", "import_react", "i", "_", "_jsx", "widgets", "<PERSON><PERSON><PERSON>F<PERSON>", "key", "import_jsx_runtime", "widgets", "o", "v", "_jsx", "import_jsx_runtime", "import_react", "widgets", "fields", "_jsxs", "_jsx", "import_jsx_runtime", "import_react", "StringField", "value", "re", "_jsx", "import_jsx_runtime", "import_react", "RuleType", "blockQuote", "breakLine", "breakThematic", "codeBlock", "codeFenced", "codeInline", "footnote", "footnoteReference", "gfmTask", "heading", "headingSetext", "htmlBlock", "htmlComment", "htmlSelfClosing", "image", "link", "linkAngleBraceStyleDetector", "linkBareUrlDetector", "linkMailtoDetector", "new<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderedList", "paragraph", "ref", "refImage", "refLink", "table", "tableSeparator", "text", "textBolded", "textEmphasized", "textEscaped", "textMarked", "textStrikethroughed", "unorderedList", "Priority", "ATTRIBUTE_TO_JSX_PROP_MAP", "reduce", "obj", "x", "toLowerCase", "class", "for", "namedCodesToUnicode", "amp", "apos", "gt", "lt", "nbsp", "quot", "DO_NOT_PROCESS_HTML_ELEMENTS", "ATTR_EXTRACTOR_R", "AUTOLINK_MAILTO_CHECK_R", "BLOCK_END_R", "BLOCKQUOTE_R", "BLOCKQUOTE_TRIM_LEFT_MULTILINE_R", "BLOCKQUOTE_ALERT_R", "BREAK_LINE_R", "BREAK_THEMATIC_R", "CODE_BLOCK_FENCED_R", "CODE_BLOCK_R", "CODE_INLINE_R", "CONSECUTIVE_NEWLINE_R", "CR_NEWLINE_R", "FOOTNOTE_R", "FOOTNOTE_REFERENCE_R", "FORMFEED_R", "FRONT_MATTER_R", "GFM_TASK_R", "HEADING_R", "HEADING_ATX_COMPLIANT_R", "HEADING_SETEXT_R", "HTML_BLOCK_ELEMENT_R", "HTML_CHAR_CODE_R", "HTML_COMMENT_R", "HTML_CUSTOM_ATTR_R", "HTML_SELF_CLOSING_ELEMENT_R", "INTERPOLATION_R", "LINK_AUTOLINK_BARE_URL_R", "LINK_AUTOLINK_MAILTO_R", "LINK_AUTOLINK_R", "CAPTURE_LETTER_AFTER_HYPHEN", "NP_TABLE_R", "REFERENCE_IMAGE_OR_LINK", "REFERENCE_IMAGE_R", "REFERENCE_LINK_R", "SHOULD_RENDER_AS_BLOCK_R", "TAB_R", "TABLE_TRIM_PIPES", "TABLE_CENTER_ALIGN", "TABLE_LEFT_ALIGN", "TABLE_RIGHT_ALIGN", "INLINE_SKIP_R", "TEXT_BOLD_R", "RegExp", "TEXT_EMPHASIZED_R", "TEXT_MARKED_R", "TEXT_STRIKETHROUGHED_R", "TEXT_ESCAPED_R", "TEXT_UNESCAPE_R", "TEXT_PLAIN_R", "TRIM_STARTING_NEWLINES", "HTML_LEFT_TRIM_AMOUNT_R", "UNESCAPE_URL_R", "LIST_LOOKBEHIND_R", "ORDERED_LIST_BULLET", "UNORDERED_LIST_BULLET", "generateListItemPrefix", "type", "ORDERED_LIST_ITEM_PREFIX", "UNORDERED_LIST_ITEM_PREFIX", "generateListItemPrefixRegex", "ORDERED_LIST_ITEM_PREFIX_R", "UNORDERED_LIST_ITEM_PREFIX_R", "generateListItemRegex", "ORDERED_LIST_ITEM_R", "UNORDERED_LIST_ITEM_R", "generateListRegex", "bullet", "ORDERED_LIST_R", "UNORDERED_LIST_R", "generateListRule", "h", "ordered", "LIST_R", "LIST_ITEM_R", "LIST_ITEM_PREFIX_R", "match", "allowInline", "source", "state", "isStartOfLine", "exec", "prevCapture", "list", "inline", "simple", "order", "parse", "capture", "start", "items", "replace", "lastItemWasAParagraph", "map", "item", "i", "space", "length", "spaceRegex", "content", "isLastItem", "thisItemIsAParagraph", "indexOf", "oldStateInline", "oldStateList", "adjustedContent", "trimEnd", "result", "render", "node", "output", "key", "undefined", "LINK_R", "IMAGE_R", "NON_PARAGRAPH_BLOCK_SYNTAXES", "BLOCK_SYNTAXES", "str", "end", "slice", "slugify", "parseTableAlignCapture", "alignCapture", "test", "parseTableRow", "tableOutput", "prevInTable", "inTable", "cells", "acc", "flush", "cell", "push", "apply", "trim", "split", "filter", "Boolean", "for<PERSON>ach", "fragment", "arr", "parseTable", "align", "rowText", "header", "children", "getTableStyle", "colIndex", "textAlign", "fn", "inlineRegex", "regex", "simpleInlineRegex", "blockRegex", "anyScopeRegex", "matchParagraph", "every", "line", "some", "captured", "sanitizer", "url", "decodeURIComponent", "e", "unescapeUrl", "rawUrlString", "parseInline", "isCurrentlyInline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseSimpleInline", "parseBlock", "parseCaptureInline", "captureNothing", "renderNothing", "cx", "args", "join", "get", "src", "path", "fb", "ptr", "frags", "shift", "compiler", "markdown", "options", "tag", "props", "overrideProps", "overrides", "createElement", "override", "_extends", "className", "compile", "input", "forceInline", "forceBlock", "emitter", "parser", "pop", "wrapper", "jsx", "forceWrapper", "attrStringToMap", "attributes", "raw", "delimiterIdx", "_", "letter", "toUpperCase", "value", "first", "<PERSON><PERSON><PERSON>", "normalizedValue", "sanitizeUrlFn", "styles", "kvPair", "substr", "footnotes", "refs", "rules", "alert", "unshift", "attrs", "noInnerParse", "__", "lang", "identifier", "target", "href", "completed", "checked", "readOnly", "enforceAtxHeadings", "id", "level", "whitespace", "trimmer", "trimmed", "parseFunc", "r", "tagName", "ast", "inAnchor", "alt", "title", "disableAutoLink", "address", "fallback<PERSON><PERSON><PERSON><PERSON>", "style", "row", "c", "full", "inner", "disableParsingRawHTML", "ruleList", "Object", "keys", "nestedParse", "rule", "parsed", "ruleType", "currCaptureString", "substring", "sort", "typeA", "typeB", "orderA", "orderB", "userRender", "renderer", "renderRule", "patchedRender", "Array", "isArray", "<PERSON><PERSON><PERSON>", "lastWasString", "nodeOut", "isString", "def", "_ref", "_excluded", "cloneElement", "fields", "<PERSON><PERSON><PERSON>F<PERSON>", "_jsxs", "_jsx", "name", "import_jsx_runtime", "import_react", "fields", "_jsx", "FieldTemplate", "FieldHelpTemplate", "FieldErrorTemplate", "formData", "id", "_jsxs", "_Fragment", "_schema", "import_jsx_runtime", "widgets", "_jsx", "import_react", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "Copy<PERSON><PERSON><PERSON>", "MoveDownButton", "MoveUpButton", "RemoveButton", "_jsxs", "_jsx", "import_jsx_runtime", "ArrayFieldDescriptionTemplate", "ArrayFieldItemTemplate", "ArrayFieldTitleTemplate", "AddButton", "_jsxs", "_jsx", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "import_react", "value", "_jsxs", "_Fragment", "_jsx", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "import_jsx_runtime", "_jsx", "_jsx", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "_jsxs", "_jsx", "i", "import_jsx_runtime", "import_jsx_runtime", "_jsxs", "_jsx", "WrapIfAdditionalTemplate", "_jsx", "_jsxs", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "AddButton", "_jsxs", "_jsx", "import_jsx_runtime", "REQUIRED_FIELD_SYMBOL", "_jsxs", "_jsx", "import_jsx_runtime", "_jsxs", "_jsx", "import_jsx_runtime", "templates", "RemoveButton", "_jsx", "_jsxs", "import_jsx_runtime", "import_react", "SelectWidget", "_jsx", "value", "state", "_jsxs", "i", "import_jsx_runtime", "AltDateWidget", "_jsx", "import_jsx_runtime", "import_react", "_jsxs", "_jsx", "import_jsx_runtime", "import_react", "_jsx", "_jsxs", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "import_react", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "value", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "import_react", "_jsx", "_jsxs", "_Fragment", "RemoveButton", "e", "BaseInputTemplate", "_", "i", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "import_react", "_jsx", "i", "_jsxs", "import_jsx_runtime", "BaseInputTemplate", "_jsxs", "_jsx", "import_jsx_runtime", "import_react", "o", "_jsxs", "_jsx", "value", "i", "disabled", "import_jsx_runtime", "import_react", "value", "_jsx", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "import_react", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "import_jsx_runtime", "BaseInputTemplate", "_jsx", "fields", "_", "_jsx", "templates", "widgets", "SubmitButton", "_jsxs", "import_jsx_runtime", "import_react", "fields", "widgets", "templates", "_jsx"]}