<!DOCTYPE html>
<html xmlns='http://www.w3.org/1999/xhtml'>
  <head>
    <title>Code coverage for Fluid-droplet-dynamic-bundle</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <script src='./assets/0.13.2/application.js' type='text/javascript'></script>
    <link href='./assets/0.13.2/application.css' media='screen, projection, print' rel='stylesheet' type='text/css' />
    <link rel="icon" type="image/png" href="./assets/0.13.2/favicon_red.png" />
  </head>

  <body>
    <div id="loading">
      <img src="./assets/0.13.2/loading.gif" alt="loading"/>
    </div>
    <div id="wrapper" class="hide">
      <div class="timestamp">Generated <abbr class="timeago" title="2025-08-25T00:19:04-03:00">2025-08-25T00:19:04-03:00</abbr></div>
      <ul class="group_tabs"></ul>

      <div id="content">
        <div class="file_list_container" id="AllFiles">
  <h2>
    <span class="group_name">All Files</span>
    (<span class="covered_percent">
      <span class="red">
  3.65%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.04
       </span>
    </span> hits/line
    )
  </h2>

  <a name="AllFiles"></a>

  <div>
    <b>41</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>1068</b> relevant lines,
    <span class="green"><b>39</b> lines covered</span> and
    <span class="red"><b>1029</b> lines missed. </span>
    (<span class="red">
  3.65%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#3b8e3e27bb25b6c72bc138893ca5bd5f08927980" class="src_link" title="app/clients/fluid/callback_definitions.rb">app/clients/fluid/callback_definitions.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">17</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#fd16daaad1e55df484b9ebbf59a1cfe06f613ee6" class="src_link" title="app/clients/fluid/callback_registrations.rb">app/clients/fluid/callback_registrations.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">65</td>
            <td class="cell--number">52</td>
            <td class="cell--number">0</td>
            <td class="cell--number">52</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#184d531b8387fb7ae81126d1c39ed890e0d09ac5" class="src_link" title="app/clients/fluid/droplets.rb">app/clients/fluid/droplets.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">45</td>
            <td class="cell--number">38</td>
            <td class="cell--number">0</td>
            <td class="cell--number">38</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#e870a5f73977ef2bad6f742d74c26251255da5b9" class="src_link" title="app/clients/fluid/webhooks.rb">app/clients/fluid/webhooks.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">48</td>
            <td class="cell--number">40</td>
            <td class="cell--number">0</td>
            <td class="cell--number">40</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#1bad93b0e75f7d9049cb5eff1804ef7511b29145" class="src_link" title="app/clients/fluid_client.rb">app/clients/fluid_client.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">57</td>
            <td class="cell--number">46</td>
            <td class="cell--number">0</td>
            <td class="cell--number">46</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#3a4529101a6578c9b669265fe733d2c7430b2291" class="src_link" title="app/controllers/admin/callbacks_controller.rb">app/controllers/admin/callbacks_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">41</td>
            <td class="cell--number">32</td>
            <td class="cell--number">0</td>
            <td class="cell--number">32</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#14d366c89ba56e80246a9be160307cd4d2cfe332" class="src_link" title="app/controllers/admin/dashboard_controller.rb">app/controllers/admin/dashboard_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#c2cb3eb2de263502181d8be5d8ad456e7c8348fe" class="src_link" title="app/controllers/admin/droplets_controller.rb">app/controllers/admin/droplets_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">21</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#12ac00333d433418601b4d6d51642cfd7690da04" class="src_link" title="app/controllers/admin/settings_controller.rb">app/controllers/admin/settings_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">23</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a2f7a5673d6578212d50fc2d02614a9e7de7a405" class="src_link" title="app/controllers/admin/users_controller.rb">app/controllers/admin/users_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">61</td>
            <td class="cell--number">49</td>
            <td class="cell--number">0</td>
            <td class="cell--number">49</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#0e40eb5a6f21641c417a93116defb8cc68757a2b" class="src_link" title="app/controllers/admin_controller.rb">app/controllers/admin_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#34db47121387ead9e9139b3d637978ea45404afc" class="src_link" title="app/controllers/application_controller.rb">app/controllers/application_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">14</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#b19ff36f58779fd6e29494c20154df1288203446" class="src_link" title="app/controllers/home_controller.rb">app/controllers/home_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#7ccb987b16d7392f931086e3f3dbb93ae115010f" class="src_link" title="app/controllers/webhooks_controller.rb">app/controllers/webhooks_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">57</td>
            <td class="cell--number">44</td>
            <td class="cell--number">0</td>
            <td class="cell--number">44</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#ee849736c9ac76e19759aa9f4f07c4749b115b67" class="src_link" title="app/helpers/application_helper.rb">app/helpers/application_helper.rb</a></td>
            <td class="red strong cell--number t-file__coverage">28.57 %</td>
            <td class="cell--number">12</td>
            <td class="cell--number">7</td>
            <td class="cell--number">2</td>
            <td class="cell--number">5</td>
            <td class="cell--number">0.29</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#69b2964d8141f6e9a6c6e4006b4114a5f31d40a1" class="src_link" title="app/jobs/application_job.rb">app/jobs/application_job.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">1</td>
            <td class="cell--number">1</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#51e145cb00e8bffa4c0b313cb2d8e47cd0faaa17" class="src_link" title="app/jobs/droplet_installed_job.rb">app/jobs/droplet_installed_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">14.81 %</td>
            <td class="cell--number">77</td>
            <td class="cell--number">27</td>
            <td class="cell--number">4</td>
            <td class="cell--number">23</td>
            <td class="cell--number">0.15</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#6e9dfa7d815c5dc472374d4527e2190ec53c164a" class="src_link" title="app/jobs/droplet_reinstalled_job.rb">app/jobs/droplet_reinstalled_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">13</td>
            <td class="cell--number">11</td>
            <td class="cell--number">0</td>
            <td class="cell--number">11</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#6391f7cbe079ea11f5f8a7ba5da2475bf33b081e" class="src_link" title="app/jobs/droplet_uninstalled_job.rb">app/jobs/droplet_uninstalled_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">29.41 %</td>
            <td class="cell--number">34</td>
            <td class="cell--number">17</td>
            <td class="cell--number">5</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.29</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#71adb49c92c8420912cd3879939abb8ef40ca3a7" class="src_link" title="app/jobs/webhook_event_job.rb">app/jobs/webhook_event_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">42.86 %</td>
            <td class="cell--number">72</td>
            <td class="cell--number">35</td>
            <td class="cell--number">15</td>
            <td class="cell--number">20</td>
            <td class="cell--number">0.43</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#9398b5c67d10988e4a77b3dadc81610a1be138dc" class="src_link" title="app/mailers/application_mailer.rb">app/mailers/application_mailer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#099d17218468e3c21f20bbfe287f7a540fd7787f" class="src_link" title="app/models/application_record.rb">app/models/application_record.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">3</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a2a8d6eb70f4d6a92c911f5bfe637eb394a67164" class="src_link" title="app/models/callback.rb">app/models/callback.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">26</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f93ec52377b8a9501af8906f81fd5a044c32485a" class="src_link" title="app/models/company.rb">app/models/company.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">17</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#8d960afa5231990f469c8bb70e23ea2ac0b020f0" class="src_link" title="app/models/event.rb">app/models/event.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">15</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a172b4a0fb81eda427cf23c9f2a48d43daad8990" class="src_link" title="app/models/integration_setting.rb">app/models/integration_setting.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#10f9c47d217e0bbd4c743eca51eecd0ae454ee81" class="src_link" title="app/models/setting.rb">app/models/setting.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">64</td>
            <td class="cell--number">45</td>
            <td class="cell--number">0</td>
            <td class="cell--number">45</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#d7e5a2fd2d200fb4b33dc0b16ce68d4d7e69ef9c" class="src_link" title="app/models/user.rb">app/models/user.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">20</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#73efe6a19d96afa84617caeef034231ecf5e86a2" class="src_link" title="app/models/webhook.rb">app/models/webhook.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#b12d165e8284bc2dc26c01296e2c31f89be5282c" class="src_link" title="app/permissions/ability.rb">app/permissions/ability.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">11</td>
            <td class="cell--number">8</td>
            <td class="cell--number">0</td>
            <td class="cell--number">8</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#8a9d4b3e35f45e57f59addb6878b3f2ff7cdc4cc" class="src_link" title="app/permissions/admin_permissions.rb">app/permissions/admin_permissions.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">5</td>
            <td class="cell--number">5</td>
            <td class="cell--number">0</td>
            <td class="cell--number">5</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f93e3ef4f043d56c0ab77d9953e5dc5bad58f740" class="src_link" title="app/permissions/permission_set.rb">app/permissions/permission_set.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">22</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#6cff8dc186d03488a8a34d477dd63170d0c98718" class="src_link" title="app/services/callback_sync_service.rb">app/services/callback_sync_service.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">42</td>
            <td class="cell--number">35</td>
            <td class="cell--number">0</td>
            <td class="cell--number">35</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#ed7ca1552d6e9fc7deeda5062b0b6b7cb008d0dd" class="src_link" title="app/services/droplet_manager.rb">app/services/droplet_manager.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">27</td>
            <td class="cell--number">20</td>
            <td class="cell--number">0</td>
            <td class="cell--number">20</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#66e4e321d8a0191f21b779541da3716e55cba135" class="src_link" title="app/services/event_handler.rb">app/services/event_handler.rb</a></td>
            <td class="red strong cell--number t-file__coverage">45.45 %</td>
            <td class="cell--number">71</td>
            <td class="cell--number">22</td>
            <td class="cell--number">10</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.59</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#ebf3bc29c3f50abe663932e9bca1eb48b35ea2de" class="src_link" title="app/services/webhook_manager.rb">app/services/webhook_manager.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">90</td>
            <td class="cell--number">71</td>
            <td class="cell--number">0</td>
            <td class="cell--number">71</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#63e19654ed31b97a18c492397a9ec4451b0b4d79" class="src_link" title="app/use_cases/droplet_use_case/base.rb">app/use_cases/droplet_use_case/base.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">39</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#c5df8b4bbcb3388df29f8f1f0ec51ab9062397fc" class="src_link" title="app/use_cases/droplet_use_case/create.rb">app/use_cases/droplet_use_case/create.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">20</td>
            <td class="cell--number">17</td>
            <td class="cell--number">0</td>
            <td class="cell--number">17</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#874a7f13e0f610800a5cdde5ffc3cb539afa76d8" class="src_link" title="app/use_cases/droplet_use_case/update.rb">app/use_cases/droplet_use_case/update.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">16</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#16d05e3d999cc89d761b2efe6039fb367c55dd00" class="src_link" title="lib/tasks/settings.rb">lib/tasks/settings.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">218</td>
            <td class="cell--number">206</td>
            <td class="cell--number">0</td>
            <td class="cell--number">206</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#955b2fc36df5562abfb0fead63313ac65381c0e8" class="src_link" title="lib/tasks/setup.rb">lib/tasks/setup.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">40</td>
            <td class="cell--number">37</td>
            <td class="cell--number">0</td>
            <td class="cell--number">37</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>


        
          <div class="file_list_container" id="Controllers">
  <h2>
    <span class="group_name">Controllers</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Controllers"></a>

  <div>
    <b>9</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>183</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>183</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#3a4529101a6578c9b669265fe733d2c7430b2291" class="src_link" title="app/controllers/admin/callbacks_controller.rb">app/controllers/admin/callbacks_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">41</td>
            <td class="cell--number">32</td>
            <td class="cell--number">0</td>
            <td class="cell--number">32</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#14d366c89ba56e80246a9be160307cd4d2cfe332" class="src_link" title="app/controllers/admin/dashboard_controller.rb">app/controllers/admin/dashboard_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#c2cb3eb2de263502181d8be5d8ad456e7c8348fe" class="src_link" title="app/controllers/admin/droplets_controller.rb">app/controllers/admin/droplets_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">21</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#12ac00333d433418601b4d6d51642cfd7690da04" class="src_link" title="app/controllers/admin/settings_controller.rb">app/controllers/admin/settings_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">23</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a2f7a5673d6578212d50fc2d02614a9e7de7a405" class="src_link" title="app/controllers/admin/users_controller.rb">app/controllers/admin/users_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">61</td>
            <td class="cell--number">49</td>
            <td class="cell--number">0</td>
            <td class="cell--number">49</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#0e40eb5a6f21641c417a93116defb8cc68757a2b" class="src_link" title="app/controllers/admin_controller.rb">app/controllers/admin_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#34db47121387ead9e9139b3d637978ea45404afc" class="src_link" title="app/controllers/application_controller.rb">app/controllers/application_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">14</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0</td>
            <td class="cell--number">10</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#b19ff36f58779fd6e29494c20154df1288203446" class="src_link" title="app/controllers/home_controller.rb">app/controllers/home_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#7ccb987b16d7392f931086e3f3dbb93ae115010f" class="src_link" title="app/controllers/webhooks_controller.rb">app/controllers/webhooks_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">57</td>
            <td class="cell--number">44</td>
            <td class="cell--number">0</td>
            <td class="cell--number">44</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Channels">
  <h2>
    <span class="group_name">Channels</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Channels"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Models">
  <h2>
    <span class="group_name">Models</span>
    (<span class="covered_percent">
      <span class="red">
  1.8%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.02
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Models"></a>

  <div>
    <b>8</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>111</b> relevant lines,
    <span class="green"><b>2</b> lines covered</span> and
    <span class="red"><b>109</b> lines missed. </span>
    (<span class="red">
  1.8%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#099d17218468e3c21f20bbfe287f7a540fd7787f" class="src_link" title="app/models/application_record.rb">app/models/application_record.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">3</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a2a8d6eb70f4d6a92c911f5bfe637eb394a67164" class="src_link" title="app/models/callback.rb">app/models/callback.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">26</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f93ec52377b8a9501af8906f81fd5a044c32485a" class="src_link" title="app/models/company.rb">app/models/company.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">17</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#8d960afa5231990f469c8bb70e23ea2ac0b020f0" class="src_link" title="app/models/event.rb">app/models/event.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">15</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a172b4a0fb81eda427cf23c9f2a48d43daad8990" class="src_link" title="app/models/integration_setting.rb">app/models/integration_setting.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#10f9c47d217e0bbd4c743eca51eecd0ae454ee81" class="src_link" title="app/models/setting.rb">app/models/setting.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">64</td>
            <td class="cell--number">45</td>
            <td class="cell--number">0</td>
            <td class="cell--number">45</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#d7e5a2fd2d200fb4b33dc0b16ce68d4d7e69ef9c" class="src_link" title="app/models/user.rb">app/models/user.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">20</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#73efe6a19d96afa84617caeef034231ecf5e86a2" class="src_link" title="app/models/webhook.rb">app/models/webhook.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Mailers">
  <h2>
    <span class="group_name">Mailers</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Mailers"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>4</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>4</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#9398b5c67d10988e4a77b3dadc81610a1be138dc" class="src_link" title="app/mailers/application_mailer.rb">app/mailers/application_mailer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Helpers">
  <h2>
    <span class="group_name">Helpers</span>
    (<span class="covered_percent">
      <span class="red">
  28.57%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.29
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Helpers"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>7</b> relevant lines,
    <span class="green"><b>2</b> lines covered</span> and
    <span class="red"><b>5</b> lines missed. </span>
    (<span class="red">
  28.57%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#ee849736c9ac76e19759aa9f4f07c4749b115b67" class="src_link" title="app/helpers/application_helper.rb">app/helpers/application_helper.rb</a></td>
            <td class="red strong cell--number t-file__coverage">28.57 %</td>
            <td class="cell--number">12</td>
            <td class="cell--number">7</td>
            <td class="cell--number">2</td>
            <td class="cell--number">5</td>
            <td class="cell--number">0.29</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Jobs">
  <h2>
    <span class="group_name">Jobs</span>
    (<span class="covered_percent">
      <span class="red">
  27.47%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.27
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Jobs"></a>

  <div>
    <b>5</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>91</b> relevant lines,
    <span class="green"><b>25</b> lines covered</span> and
    <span class="red"><b>66</b> lines missed. </span>
    (<span class="red">
  27.47%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#69b2964d8141f6e9a6c6e4006b4114a5f31d40a1" class="src_link" title="app/jobs/application_job.rb">app/jobs/application_job.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">1</td>
            <td class="cell--number">1</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#51e145cb00e8bffa4c0b313cb2d8e47cd0faaa17" class="src_link" title="app/jobs/droplet_installed_job.rb">app/jobs/droplet_installed_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">14.81 %</td>
            <td class="cell--number">77</td>
            <td class="cell--number">27</td>
            <td class="cell--number">4</td>
            <td class="cell--number">23</td>
            <td class="cell--number">0.15</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#6e9dfa7d815c5dc472374d4527e2190ec53c164a" class="src_link" title="app/jobs/droplet_reinstalled_job.rb">app/jobs/droplet_reinstalled_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">13</td>
            <td class="cell--number">11</td>
            <td class="cell--number">0</td>
            <td class="cell--number">11</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#6391f7cbe079ea11f5f8a7ba5da2475bf33b081e" class="src_link" title="app/jobs/droplet_uninstalled_job.rb">app/jobs/droplet_uninstalled_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">29.41 %</td>
            <td class="cell--number">34</td>
            <td class="cell--number">17</td>
            <td class="cell--number">5</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.29</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#71adb49c92c8420912cd3879939abb8ef40ca3a7" class="src_link" title="app/jobs/webhook_event_job.rb">app/jobs/webhook_event_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">42.86 %</td>
            <td class="cell--number">72</td>
            <td class="cell--number">35</td>
            <td class="cell--number">15</td>
            <td class="cell--number">20</td>
            <td class="cell--number">0.43</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Libraries">
  <h2>
    <span class="group_name">Libraries</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Libraries"></a>

  <div>
    <b>2</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>243</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>243</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#16d05e3d999cc89d761b2efe6039fb367c55dd00" class="src_link" title="lib/tasks/settings.rb">lib/tasks/settings.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">218</td>
            <td class="cell--number">206</td>
            <td class="cell--number">0</td>
            <td class="cell--number">206</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#955b2fc36df5562abfb0fead63313ac65381c0e8" class="src_link" title="lib/tasks/setup.rb">lib/tasks/setup.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">40</td>
            <td class="cell--number">37</td>
            <td class="cell--number">0</td>
            <td class="cell--number">37</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Services">
  <h2>
    <span class="group_name">Services</span>
    (<span class="covered_percent">
      <span class="red">
  6.76%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.09
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Services"></a>

  <div>
    <b>4</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>148</b> relevant lines,
    <span class="green"><b>10</b> lines covered</span> and
    <span class="red"><b>138</b> lines missed. </span>
    (<span class="red">
  6.76%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#6cff8dc186d03488a8a34d477dd63170d0c98718" class="src_link" title="app/services/callback_sync_service.rb">app/services/callback_sync_service.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">42</td>
            <td class="cell--number">35</td>
            <td class="cell--number">0</td>
            <td class="cell--number">35</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#ed7ca1552d6e9fc7deeda5062b0b6b7cb008d0dd" class="src_link" title="app/services/droplet_manager.rb">app/services/droplet_manager.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">27</td>
            <td class="cell--number">20</td>
            <td class="cell--number">0</td>
            <td class="cell--number">20</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#66e4e321d8a0191f21b779541da3716e55cba135" class="src_link" title="app/services/event_handler.rb">app/services/event_handler.rb</a></td>
            <td class="red strong cell--number t-file__coverage">45.45 %</td>
            <td class="cell--number">71</td>
            <td class="cell--number">22</td>
            <td class="cell--number">10</td>
            <td class="cell--number">12</td>
            <td class="cell--number">0.59</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#ebf3bc29c3f50abe663932e9bca1eb48b35ea2de" class="src_link" title="app/services/webhook_manager.rb">app/services/webhook_manager.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">90</td>
            <td class="cell--number">71</td>
            <td class="cell--number">0</td>
            <td class="cell--number">71</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Interactors">
  <h2>
    <span class="group_name">Interactors</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Interactors"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Lib">
  <h2>
    <span class="group_name">Lib</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Lib"></a>

  <div>
    <b>2</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>243</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>243</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#16d05e3d999cc89d761b2efe6039fb367c55dd00" class="src_link" title="lib/tasks/settings.rb">lib/tasks/settings.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">218</td>
            <td class="cell--number">206</td>
            <td class="cell--number">0</td>
            <td class="cell--number">206</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#955b2fc36df5562abfb0fead63313ac65381c0e8" class="src_link" title="lib/tasks/setup.rb">lib/tasks/setup.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">40</td>
            <td class="cell--number">37</td>
            <td class="cell--number">0</td>
            <td class="cell--number">37</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Ungrouped">
  <h2>
    <span class="group_name">Ungrouped</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Ungrouped"></a>

  <div>
    <b>11</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>281</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>281</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#3b8e3e27bb25b6c72bc138893ca5bd5f08927980" class="src_link" title="app/clients/fluid/callback_definitions.rb">app/clients/fluid/callback_definitions.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">17</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#fd16daaad1e55df484b9ebbf59a1cfe06f613ee6" class="src_link" title="app/clients/fluid/callback_registrations.rb">app/clients/fluid/callback_registrations.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">65</td>
            <td class="cell--number">52</td>
            <td class="cell--number">0</td>
            <td class="cell--number">52</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#184d531b8387fb7ae81126d1c39ed890e0d09ac5" class="src_link" title="app/clients/fluid/droplets.rb">app/clients/fluid/droplets.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">45</td>
            <td class="cell--number">38</td>
            <td class="cell--number">0</td>
            <td class="cell--number">38</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#e870a5f73977ef2bad6f742d74c26251255da5b9" class="src_link" title="app/clients/fluid/webhooks.rb">app/clients/fluid/webhooks.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">48</td>
            <td class="cell--number">40</td>
            <td class="cell--number">0</td>
            <td class="cell--number">40</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#1bad93b0e75f7d9049cb5eff1804ef7511b29145" class="src_link" title="app/clients/fluid_client.rb">app/clients/fluid_client.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">57</td>
            <td class="cell--number">46</td>
            <td class="cell--number">0</td>
            <td class="cell--number">46</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#b12d165e8284bc2dc26c01296e2c31f89be5282c" class="src_link" title="app/permissions/ability.rb">app/permissions/ability.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">11</td>
            <td class="cell--number">8</td>
            <td class="cell--number">0</td>
            <td class="cell--number">8</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#8a9d4b3e35f45e57f59addb6878b3f2ff7cdc4cc" class="src_link" title="app/permissions/admin_permissions.rb">app/permissions/admin_permissions.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">5</td>
            <td class="cell--number">5</td>
            <td class="cell--number">0</td>
            <td class="cell--number">5</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f93e3ef4f043d56c0ab77d9953e5dc5bad58f740" class="src_link" title="app/permissions/permission_set.rb">app/permissions/permission_set.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">22</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0</td>
            <td class="cell--number">18</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#63e19654ed31b97a18c492397a9ec4451b0b4d79" class="src_link" title="app/use_cases/droplet_use_case/base.rb">app/use_cases/droplet_use_case/base.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">39</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0</td>
            <td class="cell--number">29</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#c5df8b4bbcb3388df29f8f1f0ec51ab9062397fc" class="src_link" title="app/use_cases/droplet_use_case/create.rb">app/use_cases/droplet_use_case/create.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">20</td>
            <td class="cell--number">17</td>
            <td class="cell--number">0</td>
            <td class="cell--number">17</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#874a7f13e0f610800a5cdde5ffc3cb539afa76d8" class="src_link" title="app/use_cases/droplet_use_case/update.rb">app/use_cases/droplet_use_case/update.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">16</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0</td>
            <td class="cell--number">13</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
      </div>

      <div id="footer">
        Generated by <a href="https://github.com/simplecov-ruby/simplecov">simplecov</a> v0.22.0
        and simplecov-html v0.13.2<br/>
        using RSpec
      </div>

      <div class="source_files">
      
        <div class="source_table" id="3b8e3e27bb25b6c72bc138893ca5bd5f08927980">
  <div class="header">
    <h3>app/clients/fluid/callback_definitions.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>15</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>15</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Fluid</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  module CallbackDefinitions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    def callback_definitions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      @callback_definitions ||= Resource.new(self)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    class Resource</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      def initialize(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        @client = client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      def get</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        @client.get(&quot;/api/callback/definitions&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="fd16daaad1e55df484b9ebbf59a1cfe06f613ee6">
  <div class="header">
    <h3>app/clients/fluid/callback_registrations.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>52</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>52</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Fluid</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  module CallbackRegistrations</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    def callback_registrations</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      @callback_registrations ||= Resource.new(self)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    class Resource</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      def initialize(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        @client = client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      def get(params = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        query_string = build_query_string(params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">        @client.get(&quot;/api/callback/registrations#{query_string}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      def create(attributes = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">        @client.post(&quot;/api/callback/registrations&quot;, body: payload(attributes))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">      def show(uuid)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">        @client.get(&quot;/api/callback/registrations/#{uuid}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">      def update(uuid, attributes = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">        @client.put(&quot;/api/callback/registrations/#{uuid}&quot;, body: payload(attributes, uuid))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">      def delete(uuid)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">        @client.delete(&quot;/api/callback/registrations/#{uuid}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">      def payload(attributes = {}, uuid = nil)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">        payload_data = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">          &quot;callback_registration&quot; =&gt; {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">            &quot;definition_name&quot; =&gt; attributes[:definition_name],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">            &quot;url&quot; =&gt; attributes[:url],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">            &quot;timeout_in_seconds&quot; =&gt; attributes.key?(:timeout_in_seconds) ? attributes[:timeout_in_seconds] : 20,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">            &quot;active&quot; =&gt; attributes.key?(:active) ? attributes[:active] : true,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">          },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">        }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">        payload_data[&quot;uuid&quot;] = uuid if uuid</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">        payload_data</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">      def build_query_string(params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">        return &quot;&quot; if params.empty?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">        query_params = []</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">        query_params &lt;&lt; &quot;active=#{params[:active]}&quot; if params.key?(:active)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="55">
            

            

            <code class="ruby">        query_params &lt;&lt; &quot;company_id=#{params[:company_id]}&quot; if params.key?(:company_id)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">        query_params &lt;&lt; &quot;definition_name=#{params[:definition_name]}&quot; if params.key?(:definition_name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">        query_params &lt;&lt; &quot;page=#{params[:page]}&quot; if params.key?(:page)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">        query_params &lt;&lt; &quot;per_page=#{params[:per_page]}&quot; if params.key?(:per_page)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="59">
            

            

            <code class="ruby">        query_params &lt;&lt; &quot;sorted_by=#{params[:sorted_by]}&quot; if params.key?(:sorted_by)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="60">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">        &quot;?#{query_params.join(&#39;&amp;&#39;)}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="63">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="64">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="65">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="184d531b8387fb7ae81126d1c39ed890e0d09ac5">
  <div class="header">
    <h3>app/clients/fluid/droplets.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>38</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>38</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Fluid</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  module Droplets</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    def droplets</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      @droplets ||= Resource.new(self)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    class Resource</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      def initialize(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        @client = client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      def get</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        @client.get(&quot;/api/droplets/#{uuid}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">        @client.post(&quot;/api/droplets&quot;, body: payload)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">        @client.put(&quot;/api/droplets/#{uuid}&quot;, body: payload)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      def delete</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">        @client.delete(&quot;/api/droplets/#{uuid}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      def payload</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">        {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">          &quot;droplet&quot; =&gt; Setting.droplet.values.merge(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">            &quot;settings&quot; =&gt; {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">              &quot;marketplace_page&quot; =&gt; Setting.marketplace_page.values,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">              &quot;details_page&quot; =&gt; Setting.details_page.values,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">              &quot;service_operational_countries&quot; =&gt; Setting.service_operational_countries.values[&quot;countries&quot;],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">          ),</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">        }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">      def uuid</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">        Setting.droplet.uuid</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="e870a5f73977ef2bad6f742d74c26251255da5b9">
  <div class="header">
    <h3>app/clients/fluid/webhooks.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>40</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>40</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Fluid</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  module Webhooks</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    def webhooks</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      @webhooks ||= Resource.new(self)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    class Resource</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      def initialize(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        @client = client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      def get</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        @client.get(&quot;/api/company/webhooks&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      def create(attributes = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">        @client.post(&quot;/api/company/webhooks&quot;, body: payload(attributes))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      def update(webhook_id, attributes = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">        @client.put(&quot;/api/company/webhooks/#{webhook_id}&quot;, body: payload(attributes))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      def delete(webhook_id)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">        @client.delete(&quot;/api/company/webhooks/#{webhook_id}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      def payload(attributes = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">        {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">          &quot;webhook&quot; =&gt; {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">            &quot;resource&quot; =&gt; attributes[:resource] || &quot;droplet&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">            &quot;url&quot; =&gt; attributes[:url] || webhook_url,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">            &quot;active&quot; =&gt; attributes[:active] || true,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">            &quot;auth_token&quot; =&gt; attributes[:auth_token] || &quot;secret_token&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">            &quot;event&quot; =&gt; attributes[:event] || &quot;installed&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">            &quot;http_method&quot; =&gt; attributes[:http_method] || &quot;post&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">          },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">        }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">      def webhook_url</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">        Rails.application.routes.url_helpers.webhook_url(host: Setting.host_server.base_url)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="1bad93b0e75f7d9049cb5eff1804ef7511b29145">
  <div class="header">
    <h3>app/clients/fluid_client.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>46</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>46</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class FluidClient</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  include HTTParty</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  include Fluid::Droplets</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  include Fluid::Webhooks</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  include Fluid::CallbackDefinitions</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  include Fluid::CallbackRegistrations</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  base_uri Setting.fluid_api.base_url</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  headers &quot;Authorization&quot; =&gt; &quot;Bearer #{Setting.fluid_api.api_key}&quot;, &quot;Content-Type&quot; =&gt; &quot;application/json&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  format :json</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  Error                 = Class.new(StandardError)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  AuthenticationError   = Class.new(Error)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  ResourceNotFoundError = Class.new(Error)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">  APIError              = Class.new(Error)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">  def initialize</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    @http = self.class</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">  def get(path, options = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    handle_response(@http.get(path, format_options(options)))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">  def post(path, options = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">    handle_response(@http.post(path, format_options(options)))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">  def put(path, options = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    handle_response(@http.put(path, format_options(options)))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">  def delete(path, options = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">    handle_response(@http.delete(path, format_options(options)))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">  def format_options(options)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">    options[:body] = options[:body].to_json if options[:body].is_a?(Hash)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">    options</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">  def handle_response(response)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">    case response.code</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">    when 200..299</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">      response.parsed_response</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">    when 401</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">      raise AuthenticationError, response</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">    when 404</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">      raise ResourceNotFoundError, response</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">      raise APIError, response</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="55">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="3a4529101a6578c9b669265fe733d2c7430b2291">
  <div class="header">
    <h3>app/controllers/admin/callbacks_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>32</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>32</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Admin::CallbacksController &lt; AdminController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  before_action :authenticate_user!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  before_action :set_callback, only: %i[show edit update]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    @callbacks = ::Callback.all.order(:name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  def show; end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  def edit; end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">    if @callback.update(callback_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      redirect_to admin_callback_path(@callback), notice: &quot;Callback was successfully updated.&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      render :edit, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">  def sync</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    service = CallbackSyncService.new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    result = service.sync</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    if result[:success]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      redirect_to admin_callbacks_path, notice: result[:message]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      redirect_to admin_callbacks_path, alert: result[:message]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">  def set_callback</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    @callback = ::Callback.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">  def callback_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">    params.require(:callback).permit(:name, :description, :active, :url, :timeout_in_seconds)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="14d366c89ba56e80246a9be160307cd4d2cfe332">
  <div class="header">
    <h3>app/controllers/admin/dashboard_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Admin::DashboardController &lt; AdminController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="c2cb3eb2de263502181d8be5d8ad456e7c8348fe">
  <div class="header">
    <h3>app/controllers/admin/droplets_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>18</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>18</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Admin::DropletsController &lt; AdminController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    result = DropletUseCase::Create.call</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    if result[:success]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      redirect_to admin_dashboard_index_path, notice: &quot;Droplet created successfully&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      redirect_to admin_dashboard_index_path, alert: &quot;Failed to create droplet: #{result[:error]}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">    result = DropletUseCase::Update.call</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    if result[:success]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      redirect_to admin_dashboard_index_path, notice: &quot;Droplet updated successfully&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      redirect_to admin_dashboard_index_path, alert: &quot;Failed to update droplet: #{result[:error]}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="12ac00333d433418601b4d6d51642cfd7690da04">
  <div class="header">
    <h3>app/controllers/admin/settings_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>18</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>18</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Admin::SettingsController &lt; AdminController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    @settings = Setting.order(:name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  def edit</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    @setting = Setting.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">    @name = @setting.name.humanize.titleize</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    @setting = Setting.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">    @setting.update(setting_params)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    render json: { success: true }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  def setting_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    params.require(:setting).permit(values: {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="a2f7a5673d6578212d50fc2d02614a9e7de7a405">
  <div class="header">
    <h3>app/controllers/admin/users_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>49</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>49</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Admin::UsersController &lt; AdminController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  before_action :set_user, only: %i[ edit update destroy ]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  before_action :set_permission_sets, only: %i[ new edit update create ]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    @users = User.all</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  def new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">    @user = User.new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">    @user = User.new(user_params)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    if @user.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      redirect_to admin_users_path, notice: &quot;User created successfully&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">      flash.now[:error] = @user.errors.full_messages.to_sentence</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      render :new, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">  def edit</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">  def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">    if params[:user][:password].blank? &amp;&amp; params[:user][:password_confirmation].blank?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">      params[:user].delete(:password)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">      params[:user].delete(:password_confirmation)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">    if @user.update(user_params)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">      redirect_to admin_users_path, notice: &quot;#{@user.email} updated successfully&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">      flash.now[:error] = @user.errors.full_messages.to_sentence</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">      render :edit, status: :unprocessable_entity</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">  def destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">    @user.destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">    redirect_to admin_users_path, notice: &quot;User deleted successfully&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">  def set_user</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">    @user = User.find(params[:id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">  def set_permission_sets</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">    @permission_sets = PermissionSet.descendants.map { |p| [ p.name, p.name ] }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">  def user_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">    permitted = params.require(:user).permit(:email, :password, :password_confirmation, permission_sets: [])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">    permitted[:permission_sets] &amp;= PermissionSet.descendants.map(&amp;:name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="59">
            

            

            <code class="ruby">    permitted</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="60">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="0e40eb5a6f21641c417a93116defb8cc68757a2b">
  <div class="header">
    <h3>app/controllers/admin_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class AdminController &lt; ApplicationController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  layout &quot;admin&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  before_action :authenticate_user!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="34db47121387ead9e9139b3d637978ea45404afc">
  <div class="header">
    <h3>app/controllers/application_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>10</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>10</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationController &lt; ActionController::Base</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  allow_browser versions: :modern</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">protected</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  def after_sign_in_path_for(resource)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">    admin_dashboard_index_path</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  def current_ability</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    @current_ability ||= Ability.new(user: current_user)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="b19ff36f58779fd6e29494c20154df1288203446">
  <div class="header">
    <h3>app/controllers/home_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class HomeController &lt; ApplicationController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  def index</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="7ccb987b16d7392f931086e3f3dbb93ae115010f">
  <div class="header">
    <h3>app/controllers/webhooks_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>44</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>44</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class WebhooksController &lt; ApplicationController</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  skip_before_action :verify_authenticity_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  before_action :authenticate_webhook_token, unless: :droplet_installed_for_first_time?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    event_type = &quot;#{params[:resource]}.#{params[:event]}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    version = params[:version]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    payload = params.to_unsafe_h.deep_dup</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">    if EventHandler.route(event_type, payload, version: version)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">      # A 202 Accepted indicates that we have accepted the webhook and queued</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">      # the appropriate background job for processing.</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      head :accepted</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      head :no_content</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">  def droplet_installed_for_first_time?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    params[:resource] == &quot;droplet&quot; &amp;&amp; params[:event] == &quot;installed&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">  def authenticate_webhook_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    company = find_company</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">    if company.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">      render json: { error: &quot;Company not found&quot; }, status: :not_found</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    elsif !valid_auth_token?(company)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">      render json: { error: &quot;Unauthorized&quot; }, status: :unauthorized</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">  def valid_auth_token?(company)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby">    # Check header auth token first, then fall back to params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">    auth_header = request.headers[&quot;AUTH_TOKEN&quot;] || request.headers[&quot;X-Auth-Token&quot;] || request.env[&quot;HTTP_AUTH_TOKEN&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">    webhook_auth_token = Setting.fluid_webhook.auth_token</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">    auth_header.present? &amp;&amp; auth_header == webhook_auth_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">  def find_company</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">    Company.find_by(droplet_installation_uuid: company_params[:droplet_installation_uuid]) ||</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">      Company.find_by(fluid_company_id: company_params[:fluid_company_id])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">  def company_params</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">    params.require(:company).permit(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">      :company_droplet_uuid,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">      :droplet_installation_uuid,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">      :fluid_company_id,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">      :webhook_verification_token,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">      :authentication_token</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="55">
            

            

            <code class="ruby">    )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="ee849736c9ac76e19759aa9f4f07c4749b115b67">
  <div class="header">
    <h3>app/helpers/application_helper.rb</h3>
    <h4>
      <span class="red">
  28.57%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>7</b> relevant lines.
      <span class="green"><b>2</b> lines covered</span> and
      <span class="red"><b>5</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">module ApplicationHelper</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def format_settings_values(values)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    return &quot;&quot; if values.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    formatted_values = values.first(4).map do |key, value|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      &quot;&lt;span class=&#39;font-bold&#39;&gt;#{key}&lt;/span&gt;: #{value}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">    formatted_values &lt;&lt; &quot;...&quot; if values.size &gt; 4</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">    formatted_values.join(&quot;, &quot;).html_safe</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="69b2964d8141f6e9a6c6e4006b4114a5f31d40a1">
  <div class="header">
    <h3>app/jobs/application_job.rb</h3>
    <h4>
      <span class="green">
  100.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>1</b> relevant lines.
      <span class="green"><b>1</b> lines covered</span> and
      <span class="red"><b>0</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class ApplicationJob &lt; ActiveJob::Base</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Automatically retry jobs that encountered a deadlock</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  # retry_on ActiveRecord::Deadlocked</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">  # Most jobs are safe to ignore if the underlying records are no longer available</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">  # discard_on ActiveJob::DeserializationError</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="51e145cb00e8bffa4c0b313cb2d8e47cd0faaa17">
  <div class="header">
    <h3>app/jobs/droplet_installed_job.rb</h3>
    <h4>
      <span class="red">
  14.81%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>27</b> relevant lines.
      <span class="green"><b>4</b> lines covered</span> and
      <span class="red"><b>23</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class DropletInstalledJob &lt; WebhookEventJob</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # payload - Hash received from the webhook controller.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  # Expected structure (example):</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby">  # {</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">  #   &quot;company&quot; =&gt; {</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">  #     &quot;fluid_shop&quot; =&gt; &quot;example.myshopify.com&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">  #     &quot;name&quot; =&gt; &quot;Example Shop&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">  #     &quot;fluid_company_id&quot; =&gt; 123,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">  #     &quot;company_droplet_uuid&quot; =&gt; &quot;uuid&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">  #     &quot;authentication_token&quot; =&gt; &quot;token&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">  #     &quot;webhook_verification_token&quot; =&gt; &quot;verify&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">  #   }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">  # }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="14">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def process_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">    # Validate required keys in payload</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    validate_payload_keys(&quot;company&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    company_attributes = get_payload.fetch(&quot;company&quot;, {})</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    company = Company.find_by(fluid_shop: company_attributes[&quot;fluid_shop&quot;]) || Company.new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    company.assign_attributes(company_attributes.slice(</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby">      &quot;fluid_shop&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">      &quot;name&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">      &quot;fluid_company_id&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby">      &quot;authentication_token&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">      &quot;webhook_verification_token&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby">      &quot;droplet_installation_uuid&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">    ))</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">    company.company_droplet_uuid = company_attributes.fetch(&quot;droplet_uuid&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    company.active = true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    unless company.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">      Rails.logger.error(</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">        &quot;[DropletInstalledJob] Failed to create company: #{company.errors.full_messages.join(&#39;, &#39;)}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">      )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">      return</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">    register_active_callbacks</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="41">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="43">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def register_active_callbacks</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">    client = FluidClient.new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">    active_callbacks = ::Callback.active</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">    installed_callback_ids = []</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">    active_callbacks.each do |callback|</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">      begin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="50">
            

            

            <code class="ruby">        callback_attributes = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">          definition_name: callback.name,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby">          url: callback.url,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby">          timeout_in_seconds: callback.timeout_in_seconds,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby">          active: true,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby">        }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="56">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">        response = client.callback_registrations.create(callback_attributes)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">        if response &amp;&amp; response[&quot;callback_registration&quot;][&quot;uuid&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="59">
            

            

            <code class="ruby">          installed_callback_ids &lt;&lt; response[&quot;callback_registration&quot;][&quot;uuid&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="60">
            

            

            <code class="ruby">        else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">          Rails.logger.warn(</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="62">
            

            

            <code class="ruby">            &quot;[DropletInstalledJob] Callback registered but no UUID returned for: #{callback.name}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="63">
            

            

            <code class="ruby">          )</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="64">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="65">
            

            

            <code class="ruby">      rescue =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="66">
            

            

            <code class="ruby">        Rails.logger.error(</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="67">
            

            

            <code class="ruby">          &quot;[DropletInstalledJob] Failed to register callback #{callback.name}: #{e.message}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="68">
            

            

            <code class="ruby">        )</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="69">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="70">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="71">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="72">
            

            

            <code class="ruby">    if installed_callback_ids.any?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="73">
            

            

            <code class="ruby">      company = get_company</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="74">
            

            

            <code class="ruby">      company.update(installed_callback_ids: installed_callback_ids)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="75">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="76">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="77">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="6e9dfa7d815c5dc472374d4527e2190ec53c164a">
  <div class="header">
    <h3>app/jobs/droplet_reinstalled_job.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>11</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>11</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class DropletReinstalledJob &lt; WebhookEventJob</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Expects event_type, service_id, payload</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  def process_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">    validate_payload_keys(&quot;company&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    company = get_company</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    if company.present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      company.update(uninstalled_at: nil)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      Rails.logger.warn(&quot;[DropletReinstalledJob] Company not found for payload: #{payload.inspect}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="6391f7cbe079ea11f5f8a7ba5da2475bf33b081e">
  <div class="header">
    <h3>app/jobs/droplet_uninstalled_job.rb</h3>
    <h4>
      <span class="red">
  29.41%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>17</b> relevant lines.
      <span class="green"><b>5</b> lines covered</span> and
      <span class="red"><b>12</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class DropletUninstalledJob &lt; WebhookEventJob</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  queue_as :default</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="4">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def process_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    validate_payload_keys(&quot;company&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    company = get_company</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">    if company.present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">      delete_installed_callbacks(company)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">      company.update(uninstalled_at: Time.current)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">      Rails.logger.warn(&quot;[DropletUninstalledJob] Company not found for payload: #{get_payload.inspect}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="17">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="19">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def delete_installed_callbacks(company)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    return unless company.installed_callback_ids.present?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    client = FluidClient.new</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    company.installed_callback_ids.each do |callback_id|</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">      begin</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">        client.callback_registrations.delete(callback_id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">      rescue =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">        Rails.logger.error(&quot;[DropletUninstalledJob] Failed to delete callback #{callback_id}: #{e.message}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">    company.update(installed_callback_ids: [])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="71adb49c92c8420912cd3879939abb8ef40ca3a7">
  <div class="header">
    <h3>app/jobs/webhook_event_job.rb</h3>
    <h4>
      <span class="red">
  42.86%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>35</b> relevant lines.
      <span class="green"><b>15</b> lines covered</span> and
      <span class="red"><b>20</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class WebhookEventJob &lt; ApplicationJob</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  queue_as :webhook_events</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby">  # Retry with exponential backoff (2^n seconds) up to 5 attempts.</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="5">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  retry_on StandardError, attempts: 5, wait: -&gt;(executions) { (2**executions).seconds }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">  # Ensure idempotency for deserialization errors</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="8">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  discard_on ActiveJob::DeserializationError</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="10">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  class &lt;&lt; self</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="11">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def event_type</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      EventHandler::EVENT_HANDLERS.key(self)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="16">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def perform(payload)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    @payload = payload</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    @event_type = self.class.event_type</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    @company = find_company</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    ActiveRecord::Base.transaction do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">      process_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    Rails.logger.info(&quot;Successfully processed #{self.class.name} for company #{@company&amp;.id}, event: #{@event_type}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">  rescue StandardError =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">    Rails.logger.error(</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">      &quot;Error processing #{self.class.name} for company #{@company&amp;.id}, &quot; \</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">      &quot;event: #{@event_type}: #{e.message}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">    )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">    raise e</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">  # To be implemented by subclasses</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="34">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def process_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    raise NotImplementedError, &quot;#{self.class.name} must implement #process_webhook&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby">  # Protected accessors for subclasses</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="39">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">protected</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby">  # Get the payload from the webhook</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="42">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def get_payload</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">    @payload</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="46">
            

            

            <code class="ruby">  # Get the company associated with this webhook event</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="47">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def get_company</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">    @company</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="50">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby">  # Get the event type for this webhook</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="52">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def get_event_type</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">    @event_type</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="56">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="57">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="58">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def validate_payload_keys(*required_keys)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="59">
            

            

            <code class="ruby">    missing_keys = required_keys - @payload.keys</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="60">
            

            

            <code class="ruby">    if missing_keys.any?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">      Rails.logger.error(&quot;Missing required payload keys: #{missing_keys.join(&#39;, &#39;)}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">      raise ArgumentError, &quot;Missing required payload keys: #{missing_keys.join(&#39;, &#39;)}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="63">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="64">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="65">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="66">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def find_company</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="67">
            

            

            <code class="ruby">    uuid = @payload.dig(&quot;company&quot;, &quot;company_droplet_uuid&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="68">
            

            

            <code class="ruby">    fluid_company_id = @payload.dig(&quot;company&quot;, &quot;fluid_company_id&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="69">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="70">
            

            

            <code class="ruby">    Company.find_by(company_droplet_uuid: uuid) || Company.find_by(fluid_company_id: fluid_company_id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="71">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="72">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="9398b5c67d10988e4a77b3dadc81610a1be138dc">
  <div class="header">
    <h3>app/mailers/application_mailer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationMailer &lt; ActionMailer::Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  default from: &quot;<EMAIL>&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  layout &quot;mailer&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="099d17218468e3c21f20bbfe287f7a540fd7787f">
  <div class="header">
    <h3>app/models/application_record.rb</h3>
    <h4>
      <span class="green">
  100.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>2</b> relevant lines.
      <span class="green"><b>2</b> lines covered</span> and
      <span class="red"><b>0</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class ApplicationRecord &lt; ActiveRecord::Base</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  primary_abstract_class</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="a2a8d6eb70f4d6a92c911f5bfe637eb394a67164">
  <div class="header">
    <h3>app/models/callback.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>18</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>18</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">class Callback &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  validates :name, presence: true, uniqueness: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  validates :description, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  validates :timeout_in_seconds, numericality: { greater_than: 0, less_than_or_equal_to: 20, only_integer: true },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby"> allow_nil: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  validate :validate_active_requirements</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  scope :active, -&gt; { where(active: true) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">  def validate_active_requirements</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    return unless active?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    if url.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">      errors.add(:active, &quot;cannot be enabled without a URL&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">    if timeout_in_seconds.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">      errors.add(:active, &quot;cannot be enabled without a timeout&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="f93ec52377b8a9501af8906f81fd5a044c32485a">
  <div class="header">
    <h3>app/models/company.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>12</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>12</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Company &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  has_many :events, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  has_one :integration_setting, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  validates :fluid_shop, :authentication_token, :name, :fluid_company_id, :company_droplet_uuid, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  validates :authentication_token, uniqueness: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  scope :active, -&gt; { where(active: true) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  after_initialize :set_default_installed_callback_ids, if: :new_record?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  def set_default_installed_callback_ids</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    self.installed_callback_ids ||= []</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="8d960afa5231990f469c8bb70e23ea2ac0b020f0">
  <div class="header">
    <h3>app/models/event.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>13</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>13</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Event &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  belongs_to :company</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  enum :status, {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    pending: 0,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">    processed: 1,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    failed: 2,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  }, default: :pending</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  validates :identifier, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  validates :name, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  validates :payload, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">  validates :timestamp, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  validates :status, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="a172b4a0fb81eda427cf23c9f2a48d43daad8990">
  <div class="header">
    <h3>app/models/integration_setting.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">class IntegrationSetting &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  belongs_to :company</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  validates :company_id, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="10f9c47d217e0bbd4c743eca51eecd0ae454ee81">
  <div class="header">
    <h3>app/models/setting.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>45</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>45</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">class Setting &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby">  # OpenAPI 3.1.0 dialect for JSON Schema</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">  META_SCHEMA = &quot;https://spec.openapis.org/oas/3.1/dialect/base&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  validates :name, presence: true, uniqueness: true</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  validate :validate_schema</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  validate :validate_values</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  def self.method_missing(name, *args, **kwargs, &amp;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    Tasks::Settings.create_defaults if Setting.count.zero?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">    if Setting.exists?(name: name.to_s)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      define_singleton_method(name) { Setting.find_by(name: name.to_s) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      return send(name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    super</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">  def self.respond_to_missing?(name, include_private = false)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    Setting.exists?(name: name.to_s) || super</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">  def method_missing(name, *args, **kwargs, &amp;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">    value = values[name.to_s]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    return value if value.present?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    super</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">  def respond_to_missing?(name, include_private = false)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    values.key?(name.to_s) || super</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">  def validate_schema</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">    return errors.add(:schema, &quot;is missing&quot;) if schema.nil?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">    return nil if JSONSchemer.valid_schema?(schema, meta_schema: META_SCHEMA)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="43">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">    JSONSchemer.validate_schema(schema, meta_schema: META_SCHEMA).each do |e|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">      errors.add(:schema, e[&quot;error&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">  rescue StandardError =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">    Rails.logger.error(&quot;Error validating schema: #{e.message}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">    errors.add(:schema, &quot;is invalid&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">  def validate_values</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">    return nil if JSONSchemer.schema(schema, meta_schema: META_SCHEMA).valid?(values)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="55">
            

            

            <code class="ruby">    JSONSchemer.schema(schema, meta_schema: META_SCHEMA).validate(values).each do |e|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">      errors.add(:values, e[&quot;error&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">  rescue StandardError =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby">    # https://github.com/davishmcclurg/json_schemer/issues/215</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="60">
            

            

            <code class="ruby">    # TODO: Rescue JSONSchemer::SchemaError when it&#39;s fixed</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">    Rails.logger.error(&quot;Error validating values: #{e.message}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">    errors.add(:values, &quot;is invalid&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="63">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="64">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="d7e5a2fd2d200fb4b33dc0b16ce68d4d7e69ef9c">
  <div class="header">
    <h3>app/models/user.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>15</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>15</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class User &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Include default devise modules. Others available are:</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  devise :database_authenticatable, :registerable,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">         :recoverable, :rememberable, :validatable</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">  def has_permission_set?(set_name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">    permission_sets.include?(set_name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">  def add_permission_set(set_name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    self.permission_sets = (permission_sets + [ set_name ]).uniq</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">    save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  def remove_permission_set(set_name)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    self.permission_sets = permission_sets - [ set_name ]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="73efe6a19d96afa84617caeef034231ecf5e86a2">
  <div class="header">
    <h3>app/models/webhook.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>2</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>2</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class Webhook &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="b12d165e8284bc2dc26c01296e2c31f89be5282c">
  <div class="header">
    <h3>app/permissions/ability.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>8</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>8</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">class Ability</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  include CanCan::Ability</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  def initialize(user:)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    (user || User.new).permission_sets.each do |permission_set|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      permission_set.safe_constantize&amp;.apply(ability: self, user: user)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="8a9d4b3e35f45e57f59addb6878b3f2ff7cdc4cc">
  <div class="header">
    <h3>app/permissions/admin_permissions.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>5</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>5</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class AdminPermissions &lt; PermissionSet</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  def self.apply(ability:, user:)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    ability.can :manage, :all</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="f93e3ef4f043d56c0ab77d9953e5dc5bad58f740">
  <div class="header">
    <h3>app/permissions/permission_set.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>18</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>18</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class PermissionSet</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  @@descendants = []</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  class &lt;&lt; self</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    def inherited(subclass)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      @@descendants &lt;&lt; subclass</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    def descendants</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      load_descendants if @@descendants.empty?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">      @@descendants.dup</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">    def apply(ability:, user:)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      raise NotImplementedError, &quot;Subclasses must implement the apply method&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    def load_descendants</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">      Dir[Rails.root.join(&quot;app/permissions/**/*.rb&quot;)].each { |file| require_dependency file }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="6cff8dc186d03488a8a34d477dd63170d0c98718">
  <div class="header">
    <h3>app/services/callback_sync_service.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>35</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>35</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class CallbackSyncService</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  def initialize</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    @client = FluidClient.new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  def sync</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    begin</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">      response = @client.callback_definitions.get</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      if response&amp;.dig(&quot;definitions&quot;)&amp;.any?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">        sync_callbacks(response[&quot;definitions&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">        { success: true, message: &quot;Successfully synced #{response[&#39;definitions&#39;].length} callbacks&quot; }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">        { success: false, message: &quot;No callback definitions found&quot; }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    rescue =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      Rails.logger.error &quot;Callback sync failed: #{e.message}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">      { success: false, message: &quot;Sync failed: #{e.message}&quot; }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">  def sync_callbacks(definitions)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    definitions.each do |definition|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      create_or_update_callback(definition)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">  def create_or_update_callback(definition)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    callback = Callback.find_or_initialize_by(name: definition[&quot;name&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">    callback.assign_attributes(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">      description: definition[&quot;description&quot;],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">      active: false</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">    )</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">    callback.save!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">  rescue =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">    Rails.logger.error &quot;Failed to sync callback #{definition[&#39;name&#39;]}: #{e.message}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="ed7ca1552d6e9fc7deeda5062b0b6b7cb008d0dd">
  <div class="header">
    <h3>app/services/droplet_manager.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>20</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>20</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">class DropletManager</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  def initialize(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    @client = client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    response = @client.droplets.create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">    update_droplet_setting(response[&quot;droplet&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">    response[&quot;droplet&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">  def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    @client.droplets.update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  attr_reader :client</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">  def update_droplet_setting(droplet_data)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    setting = Setting.droplet</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    setting.values = droplet_data.slice(&quot;uuid&quot;, &quot;name&quot;, &quot;active&quot;, &quot;embed_url&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    setting.save!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="66e4e321d8a0191f21b779541da3716e55cba135">
  <div class="header">
    <h3>app/services/event_handler.rb</h3>
    <h4>
      <span class="red">
  45.45%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>22</b> relevant lines.
      <span class="green"><b>10</b> lines covered</span> and
      <span class="red"><b>12</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class EventHandler</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Stores the mapping of event type (optionally namespaced by version) to handler classes.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  #   { &quot;v1.company_droplet.created&quot; =&gt; DropletInstalledJob, ... }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby">  # HashWithIndifferentAccess is used so callers can pass either String or Symbol keys.</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="5">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  EVENT_HANDLERS = ActiveSupport::HashWithIndifferentAccess.new</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="7">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  class &lt;&lt; self</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">    # Public: Routes the given `event_type` to the handler that has been</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">    # registered for it. If no handler can be found the method will log a</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">    # warning and return false so that the caller can decide how to respond.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">    #</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">    # event_type - A String like &quot;company_droplet.created&quot;. The string can be</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">    #              prefixed with a version (e.g. &quot;v2.company_droplet.created&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">    #              but you can also pass the version in via the `version:` kwarg.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">    # *args      - Arguments that will be forwarded to the `perform_later` call</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">    #              on the job class.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby">    # version:   - Optional. When supplied the router will look for a mapping</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">    #              under `&quot;#{version}.#{event_type}&quot;` first and fall back to the</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">    #              plain event type if that fails.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">    #</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby">    # Returns true when a handler was found and the job was enqueued, otherwise</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">    # false.</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="23">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def route(event_type, *args, version: nil)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      key = build_key(event_type, version)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">      handler_class = EVENT_HANDLERS[key] || EVENT_HANDLERS[event_type]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      unless handler_class</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">        Rails.logger.warn(&quot;[EventHandler] No handler found for event type: #{key}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">        return false</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">      # We only support ActiveJob compatible handlers for now. If you want to</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">      # support PORO handlers you could extend this section.</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">      if handler_class.respond_to?(:perform_later)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">        handler_class.perform_later(*args)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby">      else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">        Rails.logger.error(&quot;[EventHandler] Handler #{handler_class} does not respond to `perform_later`.&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">        return false</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">      true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="43">
            

            

            <code class="ruby">    rescue StandardError =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">      Rails.logger.error(&quot;[EventHandler] Error while routing event &#39;#{key}&#39;: #{e.class} - #{e.message}&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">      false</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="46">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby">    # Public: Registers a handler for the given event type.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">    #</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="50">
            

            

            <code class="ruby">    # event_type   - String like &quot;company_droplet.created&quot;.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby">    # handler_class- A class that responds to `perform_later` (e.g. an ActiveJob).</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby">    # version:     - Optional version namespace (e.g. &quot;v2&quot;). When supplied the</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby">    #               mapping will be stored under `&quot;#{version}.#{event_type}&quot;`.</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby">    #</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby">    # Examples</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="56">
            

            

            <code class="ruby">    #   EventHandler.register_handler(&quot;company_droplet.created&quot;, MyJob)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="57">
            

            

            <code class="ruby">    #   EventHandler.register_handler(&quot;company_droplet.created&quot;, V2::MyJob, version: &quot;v2&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="58">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def register_handler(event_type, handler_class, version: nil)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="59">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">      key = build_key(event_type, version)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="60">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">      EVENT_HANDLERS[key] = handler_class</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="61">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="62">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="63">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="64">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="65">
            

            

            <code class="ruby">    # Builds an internal lookup key for the given event_type and optional</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="66">
            

            

            <code class="ruby">    # version.</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="67">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    def build_key(event_type, version)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="68">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">      version.present? ? &quot;#{version}.#{event_type}&quot; : event_type.to_s</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="69">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="70">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="71">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="ebf3bc29c3f50abe663932e9bca1eb48b35ea2de">
  <div class="header">
    <h3>app/services/webhook_manager.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>71</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>71</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">class WebhookManager</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  def initialize(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    @client = client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">  def create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    installation_webhook = create_installation_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">    uninstallation_webhook = create_uninstallation_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    update_webhook_setting(installation_webhook, uninstallation_webhook)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">    {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      installation_webhook: installation_webhook,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">      uninstallation_webhook: uninstallation_webhook,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">    }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">  def update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    webhook_setting = Setting.fluid_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    installation_webhook = update_installation_webhook(webhook_setting.values[&quot;webhook_installation_id&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">    uninstallation_webhook = update_uninstallation_webhook(webhook_setting.values[&quot;webhook_uninstallation_id&quot;])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">    {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">      installation_webhook: installation_webhook,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      uninstallation_webhook: uninstallation_webhook,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">  attr_reader :client</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">  def create_installation_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">    response = @client.webhooks.create(installation_webhook_attributes)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">    response[&quot;webhook&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">  def create_uninstallation_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">    response = @client.webhooks.create(uninstallation_webhook_attributes)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="43">
            

            

            <code class="ruby">    response[&quot;webhook&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">  def update_installation_webhook(webhook_id)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">    return nil if webhook_id.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">    response = @client.webhooks.update(webhook_id, installation_webhook_attributes)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">    response[&quot;webhook&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">  def update_uninstallation_webhook(webhook_id)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">    return nil if webhook_id.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">    response = @client.webhooks.update(webhook_id, uninstallation_webhook_attributes)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">    response[&quot;webhook&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="60">
            

            

            <code class="ruby">  def update_webhook_setting(installation_webhook, uninstallation_webhook)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">    webhook_setting = Setting.fluid_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">    webhook_setting.values[&quot;webhook_installation_id&quot;] = installation_webhook[&quot;id&quot;].to_s</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="63">
            

            

            <code class="ruby">    webhook_setting.values[&quot;webhook_uninstallation_id&quot;] = uninstallation_webhook[&quot;id&quot;].to_s</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="64">
            

            

            <code class="ruby">    webhook_setting.save!</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="65">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="66">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="67">
            

            

            <code class="ruby">  def installation_webhook_attributes</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="68">
            

            

            <code class="ruby">    webhook_setting = Setting.fluid_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="69">
            

            

            <code class="ruby">    {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="70">
            

            

            <code class="ruby">      resource: &quot;droplet&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="71">
            

            

            <code class="ruby">      url: webhook_setting.url,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="72">
            

            

            <code class="ruby">      active: true,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="73">
            

            

            <code class="ruby">      auth_token: webhook_setting.auth_token,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="74">
            

            

            <code class="ruby">      event: &quot;installed&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="75">
            

            

            <code class="ruby">      http_method: webhook_setting.http_method.downcase,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="76">
            

            

            <code class="ruby">    }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="77">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="78">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="79">
            

            

            <code class="ruby">  def uninstallation_webhook_attributes</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="80">
            

            

            <code class="ruby">    webhook_setting = Setting.fluid_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="81">
            

            

            <code class="ruby">    {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="82">
            

            

            <code class="ruby">      resource: &quot;droplet&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="83">
            

            

            <code class="ruby">      url: webhook_setting.url,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="84">
            

            

            <code class="ruby">      active: true,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="85">
            

            

            <code class="ruby">      auth_token: webhook_setting.auth_token,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="86">
            

            

            <code class="ruby">      event: &quot;uninstalled&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="87">
            

            

            <code class="ruby">      http_method: webhook_setting.http_method.downcase,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="88">
            

            

            <code class="ruby">    }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="89">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="90">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="63e19654ed31b97a18c492397a9ec4451b0b4d79">
  <div class="header">
    <h3>app/use_cases/droplet_use_case/base.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>29</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>29</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">module DropletUseCase</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  class Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    def self.call(**args)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      new(**args).call</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">    def initialize(**args)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">      args.each { |key, value| instance_variable_set(&quot;@#{key}&quot;, value) }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">    def call</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">      raise NotImplementedError, &quot;#{self.class} must implement #call&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">    def success(data = {})</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">      { success: true }.merge(data)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">    def failure(error_message)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      { success: false, error: error_message }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">    def droplet_manager</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      @droplet_manager ||= DropletManager.new(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    def webhook_manager</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">      @webhook_manager ||= WebhookManager.new(client)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">    def client</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">      @client ||= FluidClient.new</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="c5df8b4bbcb3388df29f8f1f0ec51ab9062397fc">
  <div class="header">
    <h3>app/use_cases/droplet_use_case/create.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>17</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>17</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">module DropletUseCase</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  class Create &lt; Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    def call</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      ActiveRecord::Base.transaction do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">        droplet_data = droplet_manager.create</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">        webhook_data = webhook_manager.create</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        success(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">          droplet: droplet_data,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">          installation_webhook: webhook_data[:installation_webhook],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">          uninstallation_webhook: webhook_data[:uninstallation_webhook]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">        )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">    rescue FluidClient::Error =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">      failure(e.message)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="874a7f13e0f610800a5cdde5ffc3cb539afa76d8">
  <div class="header">
    <h3>app/use_cases/droplet_use_case/update.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>13</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>13</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">module DropletUseCase</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">  class Update &lt; Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">    def call</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">      ActiveRecord::Base.transaction do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">        droplet_manager.update</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">        webhook_data = webhook_manager.update</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        success(webhook: webhook_data)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">    rescue FluidClient::Error =&gt; e</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">      failure(e.message)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="16d05e3d999cc89d761b2efe6039fb367c55dd00">
  <div class="header">
    <h3>lib/tasks/settings.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>206</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>206</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Tasks</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class Settings</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    class &lt;&lt; self</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      def create_defaults</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">        create_host_server_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">        create_fluid_api_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">        create_droplet_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">        create_marketplace_page_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">        create_details_page_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        create_service_operational_countries_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="11">
            

            

            <code class="ruby">        create_fluid_webhook_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">      def remove_defaults</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">        Setting.where(name: %w[</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="17">
            

            

            <code class="ruby">          fluid_api</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">          droplet</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">          marketplace_page</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">          details_page</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">          service_operational_countries</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">          fluid_webhook</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">        ]).delete_all</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">    private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">      def create_host_server_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;host_server&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">          setting.description = &quot;Settings for the hosting server&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">            required: %w[ base_url ],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="34">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">              base_url: { type: &quot;string&quot; },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">            base_url: &quot;http://localhost:3000&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="41">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="42">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="43">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="44">
            

            

            <code class="ruby">      def create_fluid_api_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="45">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;fluid_api&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="46">
            

            

            <code class="ruby">          setting.description = &quot;Settings for the Fluid API&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="47">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="48">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="49">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="50">
            

            

            <code class="ruby">              base_url: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="51">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="52">
            

            

            <code class="ruby">                format: &quot;uri&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="53">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="54">
            

            

            <code class="ruby">              api_key: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="55">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="56">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="57">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="58">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="59">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="60">
            

            

            <code class="ruby">            base_url: &quot;https://api.fluid.com&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="61">
            

            

            <code class="ruby">            api_key: &quot;change-me&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="62">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="63">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="64">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="65">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="66">
            

            

            <code class="ruby">      def create_droplet_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="67">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;droplet&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="68">
            

            

            <code class="ruby">          setting.description = &quot;General settings for the Droplet. &quot; \</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="69">
            

            

            <code class="ruby">                                &quot;The UUID is automatically set when the Droplet is created.&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="70">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="71">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="72">
            

            

            <code class="ruby">            required: %w[ name embed_url active ],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="73">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="74">
            

            

            <code class="ruby">              name: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="75">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="76">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="77">
            

            

            <code class="ruby">              embed_url: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="78">
            

            

            <code class="ruby">                type: %w[string null],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="79">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="80">
            

            

            <code class="ruby">              uuid: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="81">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="82">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="83">
            

            

            <code class="ruby">              active: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="84">
            

            

            <code class="ruby">                type: &quot;boolean&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="85">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="86">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="87">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="88">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="89">
            

            

            <code class="ruby">            name: &quot;Placeholder&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="90">
            

            

            <code class="ruby">            embed_url: &quot;https://example.com&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="91">
            

            

            <code class="ruby">            active: true,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="92">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="93">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="94">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="95">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="96">
            

            

            <code class="ruby">      def create_marketplace_page_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="97">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;marketplace_page&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="98">
            

            

            <code class="ruby">          setting.description = &quot;Values for the Droplet Marketplace Page&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="99">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="100">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="101">
            

            

            <code class="ruby">            required: [ &quot;title&quot; ],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="102">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="103">
            

            

            <code class="ruby">              title: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="104">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="105">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="106">
            

            

            <code class="ruby">              logo_url: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="107">
            

            

            <code class="ruby">                type: %w[string null],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="108">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="109">
            

            

            <code class="ruby">              summary: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="110">
            

            

            <code class="ruby">                type: %w[string null],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="111">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="112">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="113">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="114">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="115">
            

            

            <code class="ruby">            title: &quot;Placeholder&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="116">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="117">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="118">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="119">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="120">
            

            

            <code class="ruby">      def create_details_page_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="121">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;details_page&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="122">
            

            

            <code class="ruby">          setting.description = &quot;Values for the Droplet Details Page&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="123">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="124">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="125">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="126">
            

            

            <code class="ruby">              title: { type: &quot;string&quot; },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="127">
            

            

            <code class="ruby">              logo_url: { type: %w[string null] },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="128">
            

            

            <code class="ruby">              summary: { type: %w[string null] },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="129">
            

            

            <code class="ruby">              features: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="130">
            

            

            <code class="ruby">                type: %w[array null],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="131">
            

            

            <code class="ruby">                items: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="132">
            

            

            <code class="ruby">                  type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="133">
            

            

            <code class="ruby">                  properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="134">
            

            

            <code class="ruby">                    name: { type: &quot;string&quot; },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="135">
            

            

            <code class="ruby">                    summary: { type: %w[string null] },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="136">
            

            

            <code class="ruby">                    details: { type: %w[string null] },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="137">
            

            

            <code class="ruby">                    image_url: { type: %w[string null] },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="138">
            

            

            <code class="ruby">                    video_url: { type: %w[string null] },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="139">
            

            

            <code class="ruby">                  },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="140">
            

            

            <code class="ruby">                  required: %w[name],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="141">
            

            

            <code class="ruby">                },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="142">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="143">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="144">
            

            

            <code class="ruby">            required: %w[title],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="145">
            

            

            <code class="ruby">            }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="146">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="147">
            

            

            <code class="ruby">            title: &quot;Placeholder&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="148">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="149">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="150">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="151">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="152">
            

            

            <code class="ruby">      def create_service_operational_countries_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="153">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;service_operational_countries&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="154">
            

            

            <code class="ruby">          setting.description = &quot;Countries where the service is operational (ISO Country Codes). &quot; \</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="155">
            

            

            <code class="ruby">                                &quot;Leave blank if the Droplet is available worldwide.&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="156">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="157">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="158">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="159">
            

            

            <code class="ruby">              countries: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="160">
            

            

            <code class="ruby">                type: &quot;array&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="161">
            

            

            <code class="ruby">                items: { type: &quot;string&quot; },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="162">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="163">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="164">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="165">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="166">
            

            

            <code class="ruby">            countries: [],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="167">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="168">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="169">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="170">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="171">
            

            

            <code class="ruby">      def create_fluid_webhook_setting</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="172">
            

            

            <code class="ruby">        Setting.find_or_create_by!(name: &quot;fluid_webhook&quot;) do |setting|</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="173">
            

            

            <code class="ruby">          setting.description = &quot;Settings for creating webhooks in Fluid Core&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="174">
            

            

            <code class="ruby">          setting.schema = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="175">
            

            

            <code class="ruby">            type: &quot;object&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="176">
            

            

            <code class="ruby">            required: %w[ url auth_token http_method ],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="177">
            

            

            <code class="ruby">            properties: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="178">
            

            

            <code class="ruby">              url: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="179">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="180">
            

            

            <code class="ruby">                format: &quot;uri&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="181">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="182">
            

            

            <code class="ruby">              auth_token: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="183">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="184">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="185">
            

            

            <code class="ruby">              http_method: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="186">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="187">
            

            

            <code class="ruby">                enum: %w[ GET POST PUT PATCH DELETE ],</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="188">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="189">
            

            

            <code class="ruby">              webhook_installation_id: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="190">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="191">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="192">
            

            

            <code class="ruby">              webhook_uninstallation_id: {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="193">
            

            

            <code class="ruby">                type: &quot;string&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="194">
            

            

            <code class="ruby">              },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="195">
            

            

            <code class="ruby">            },</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="196">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="197">
            

            

            <code class="ruby">          setting.values = {</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="198">
            

            

            <code class="ruby">            url: &quot;https://api.example.com&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="199">
            

            

            <code class="ruby">            auth_token: &quot;change-me&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="200">
            

            

            <code class="ruby">            http_method: &quot;POST&quot;,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="201">
            

            

            <code class="ruby">          }</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="202">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="203">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="204">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="205">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="206">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="207">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="208">
            

            

            <code class="ruby">namespace :settings do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="209">
            

            

            <code class="ruby">  desc &quot;Create default settings&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="210">
            

            

            <code class="ruby">  task create_defaults: :environment do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="211">
            

            

            <code class="ruby">    Tasks::Settings.create_defaults</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="212">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="213">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="214">
            

            

            <code class="ruby">  desc &quot;Remove default settings&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="215">
            

            

            <code class="ruby">  task remove_defaults: :environment do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="216">
            

            

            <code class="ruby">    Tasks::Settings.remove_defaults</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="217">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="218">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="955b2fc36df5562abfb0fead63313ac65381c0e8">
  <div class="header">
    <h3>lib/tasks/setup.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>37</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>37</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module Tasks</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  class Setup</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">    class &lt;&lt; self</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">      def create_admin</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="5">
            

            

            <code class="ruby">        email = ENV[&quot;ADMIN_EMAIL&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="6">
            

            

            <code class="ruby">        password = ENV[&quot;ADMIN_PASSWORD&quot;]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">        if email.blank? || password.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="8">
            

            

            <code class="ruby">          puts &quot;ADMIN_EMAIL and ADMIN_PASSWORD must both be set&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="9">
            

            

            <code class="ruby">          return</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="10">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="12">
            

            

            <code class="ruby">        user = User.find_by(email: email)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="13">
            

            

            <code class="ruby">        if user.present?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="14">
            

            

            <code class="ruby">          puts &quot;Admin user for #{email} already exists&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="15">
            

            

            <code class="ruby">          return</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="16">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="18">
            

            

            <code class="ruby">        user = User.new(</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="19">
            

            

            <code class="ruby">          email: email,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="20">
            

            

            <code class="ruby">          password: password,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="21">
            

            

            <code class="ruby">          password_confirmation: password,</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="22">
            

            

            <code class="ruby">          permission_sets: [ &quot;AdminPermissions&quot; ]</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="23">
            

            

            <code class="ruby">        )</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="24">
            

            

            <code class="ruby">        if user.save</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="25">
            

            

            <code class="ruby">          puts &quot;Admin user for #{email} created&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="26">
            

            

            <code class="ruby">        else</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="27">
            

            

            <code class="ruby">          puts &quot;Admin user for #{email} creation failed&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="28">
            

            

            <code class="ruby">          puts user.errors.full_messages.join(&quot;, &quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="29">
            

            

            <code class="ruby">        end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="30">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="31">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="32">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="33">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="35">
            

            

            <code class="ruby">namespace :setup do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="36">
            

            

            <code class="ruby">  desc &quot;Create an admin user from environment variables one doesn&#39;t exist&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="37">
            

            

            <code class="ruby">  task create_admin: :environment do</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="38">
            

            

            <code class="ruby">    Tasks::Setup.create_admin</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="39">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="40">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
      </div>
    </div>
  </body>
</html>
