{"RSpec": {"coverage": {"/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/services/event_handler.rb": {"lines": [1, null, null, null, 1, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 0, null, 0, null, 0, 0, 0, null, null, null, null, 0, 0, null, 0, 0, null, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, 1, 2, 2, null, null, 1, null, null, null, 1, 2, null, null, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/jobs/droplet_uninstalled_job.rb": {"lines": [1, 1, null, 1, 0, 0, null, 0, 0, null, 0, null, 0, null, null, null, 1, null, 1, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 0, null, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/jobs/webhook_event_job.rb": {"lines": [1, 1, null, null, 1, null, null, 1, null, 1, 1, 0, null, null, null, 1, 0, 0, 0, 0, 0, null, null, 0, null, 0, null, null, null, 0, null, null, null, 1, 0, null, null, null, 1, null, null, 1, 0, null, null, null, 1, 0, null, null, null, 1, 0, null, null, 1, null, 1, 0, 0, 0, 0, null, null, null, 1, 0, 0, null, 0, null, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/jobs/application_job.rb": {"lines": [1, null, null, null, null, null, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/jobs/droplet_installed_job.rb": {"lines": [1, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, 0, 0, null, 0, 0, null, null, null, null, null, null, null, 0, 0, null, 0, 0, null, null, 0, null, null, 0, null, null, 1, null, 1, 0, 0, 0, null, 0, null, null, 0, null, null, null, null, null, 0, 0, 0, null, 0, null, null, null, null, 0, null, null, null, null, null, 0, 0, 0, null, null, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/helpers/application_helper.rb": {"lines": [1, 1, 0, null, 0, 0, null, 0, null, 0, null, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/application_record.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/clients/fluid/callback_definitions.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/clients/fluid/callback_registrations.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/clients/fluid/droplets.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/clients/fluid/webhooks.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/clients/fluid_client.rb": {"lines": [0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/admin/callbacks_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/admin/dashboard_controller.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/admin/droplets_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/admin/settings_controller.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/admin/users_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/admin_controller.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/application_controller.rb": {"lines": [0, null, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/home_controller.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/controllers/webhooks_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/jobs/droplet_reinstalled_job.rb": {"lines": [0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/mailers/application_mailer.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/callback.rb": {"lines": [null, null, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/company.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/event.rb": {"lines": [0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/integration_setting.rb": {"lines": [null, null, 0, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/setting.rb": {"lines": [null, null, 0, null, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/user.rb": {"lines": [0, null, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/models/webhook.rb": {"lines": [0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/permissions/ability.rb": {"lines": [null, null, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/permissions/admin_permissions.rb": {"lines": [0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/permissions/permission_set.rb": {"lines": [0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/services/callback_sync_service.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/services/droplet_manager.rb": {"lines": [null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/services/webhook_manager.rb": {"lines": [null, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/use_cases/droplet_use_case/base.rb": {"lines": [null, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/use_cases/droplet_use_case/create.rb": {"lines": [null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/app/use_cases/droplet_use_case/update.rb": {"lines": [null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/lib/tasks/settings.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/fluid-droplet-dynamic-bundle/lib/tasks/setup.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}}, "timestamp": 1756091944}}